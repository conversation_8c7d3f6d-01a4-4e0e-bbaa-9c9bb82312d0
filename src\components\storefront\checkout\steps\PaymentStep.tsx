
import React from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useCart } from "@/contexts";
import PaymentMethodSelector from "@/components/storefront/checkout/PaymentMethodSelector";
import MpesaPaymentForm from "@/components/storefront/checkout/payment-methods/MpesaPaymentForm";
import PaypalPaymentForm from "@/components/storefront/checkout/payment-methods/PaypalPaymentForm";
import CreditCardPaymentForm from "@/components/storefront/checkout/payment-methods/CreditCardPaymentForm";
import { ChevronLeft } from "lucide-react";

interface PaymentStepProps {
  paymentMethod: string;
  onPrev: () => void;
  onNext: () => void;
  onSelectMethod: (method: string) => void;
  isProcessing: boolean;
}

const PaymentStep: React.FC<PaymentStepProps> = ({
  paymentMethod,
  onPrev,
  onNext,
  onSelectMethod,
  isProcessing
}) => {
  const { total } = useCart();

  const renderPaymentForm = () => {
    switch (paymentMethod) {
      case "mpesa":
        return <MpesaPaymentForm onPaymentComplete={() => {}} amount={total} />;
      case "paypal":
        return <PaypalPaymentForm onPaymentComplete={() => {}} amount={total} />;
      case "credit-card":
        return <CreditCardPaymentForm onPaymentComplete={() => {}} amount={total} />;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Payment Method</h2>
          <PaymentMethodSelector
            selectedMethod={paymentMethod}
            onSelectMethod={onSelectMethod}
          />
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Payment Details</h2>
          {renderPaymentForm()}
        </CardContent>
      </Card>

      <div className="flex justify-between pt-4">
        <Button 
          variant="outline" 
          onClick={onPrev}
          disabled={isProcessing}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Shipping
        </Button>
        <Button 
          onClick={onNext} 
          disabled={isProcessing}
        >
          {isProcessing ? "Processing..." : "Complete Order"}
        </Button>
      </div>
    </div>
  );
};

export default PaymentStep;
