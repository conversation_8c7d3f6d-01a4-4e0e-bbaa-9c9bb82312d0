
import React from 'react';
import { Filter, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

interface ProductFilterControlsProps {
  selectedCategories: string[];
  sortBy: string;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;
  toggleCategory: (category: string) => void;
  onSortChange: (value: string) => void;
  searchQuery: string;
  currentPage: number;
}

const ProductFilterControls: React.FC<ProductFilterControlsProps> = ({
  selectedCategories,
  sortBy,
  showFilters,
  setShowFilters,
  toggleCategory,
  onSortChange,
  searchQuery,
  currentPage
}) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div className="flex items-center gap-2">
        <Button 
          variant="outline" 
          size="sm" 
          className="lg:hidden"
          onClick={() => setShowFilters(!showFilters)}
        >
          <Filter className="h-4 w-4 mr-2" />
          Filters
        </Button>
        
        {selectedCategories.length > 0 && (
          <div className="flex items-center gap-2 flex-wrap">
            {selectedCategories.map((cat, index) => (
              <Badge key={index} variant="outline" className="flex items-center gap-1">
                {cat}
                <button onClick={() => toggleCategory(cat)}>
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        )}
      </div>
      
      <div className="flex items-center gap-2 w-full sm:w-auto">
        <Select 
          value={sortBy} 
          onValueChange={(value) => onSortChange(value)}
        >
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="newest">Newest</SelectItem>
            <SelectItem value="price-low">Price: Low to High</SelectItem>
            <SelectItem value="price-high">Price: High to Low</SelectItem>
            <SelectItem value="name-asc">Name: A to Z</SelectItem>
            <SelectItem value="name-desc">Name: Z to A</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default ProductFilterControls;
