/// <reference types="vitest" />
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { VitePWA } from 'vite-plugin-pwa';
// Only import componentTagger in development when needed
import { componentTagger } from "lovable-tagger";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === "development";

  return {
    base: "/",
    server: {
      host: "0.0.0.0",
      port: 8080,
      open: false,
      hmr: {
        overlay: false,
      },
    },
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "react-router-dom",
        "@radix-ui/react-dialog",
        "@radix-ui/react-dropdown-menu",
        "lucide-react",
        "framer-motion",
        "react-error-boundary",
      ],
      exclude: ["recharts"],
    },
    plugins: [
      react({
        jsxImportSource: "react",
      }),
      VitePWA({
        registerType: 'autoUpdate',
        includeAssets: ['favicon.ico', 'apple-touch-icon.png', 'safari-pinned-tab.svg'],
        manifest: {
          name: 'M-duka - WhatsApp Digital Store',
          short_name: 'M-duka',
          description: 'Transform your WhatsApp Business into a powerful digital storefront. Create your online store in minutes, manage orders, and grow your business effortlessly.',
          theme_color: '#4CAF50',
          background_color: '#ffffff',
          display: 'standalone',
          orientation: 'portrait',
          scope: '/',
          start_url: '/',
          icons: [
            {
              src: '/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png',
              sizes: '192x192',
              type: 'image/png'
            },
            {
              src: '/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png',
              sizes: '512x512',
              type: 'image/png'
            },
            {
              src: '/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png',
              sizes: '512x512',
              type: 'image/png',
              purpose: 'any maskable'
            }
          ]
        },
        workbox: {
          globPatterns: ['**/*.{js,css,html,ico,png,svg,webp,jpg,jpeg}'],
          maximumFileSizeToCacheInBytes: 4 * 1024 * 1024, // 4MB limit
          runtimeCaching: [
            {
              urlPattern: /^https:\/\/fonts\.googleapis\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'google-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365
                }
              }
            },
            {
              urlPattern: /^https:\/\/fonts\.gstatic\.com\/.*/i,
              handler: 'CacheFirst',
              options: {
                cacheName: 'gstatic-fonts-cache',
                expiration: {
                  maxEntries: 10,
                  maxAgeSeconds: 60 * 60 * 24 * 365
                }
              }
            },
            {
              urlPattern: /\.(png|jpg|jpeg|svg|gif|webp)$/,
              handler: 'CacheFirst',
              options: {
                cacheName: 'images-cache',
                expiration: {
                  maxEntries: 60,
                  maxAgeSeconds: 60 * 60 * 24 * 30
                }
              }
            }
          ]
        },
        devOptions: {
          enabled: true
        }
      }),
      isDev && componentTagger(),
    ].filter(Boolean),
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
        // Ensure react-is alias is defined properly
        "react-is": path.resolve(__dirname, "./src/utils/react-is-compat.ts"),
        // Add prop-types alias to resolve the issue
        "prop-types": path.resolve(__dirname, "./src/utils/prop-types-compat.ts"),
        // Add eventemitter3 alias
        "eventemitter3": path.resolve(__dirname, "./src/utils/eventemitter3-compat.js"),
        // Add direct aliases for each lodash function used by recharts
        "lodash/isNil": path.resolve(__dirname, "./src/utils/lodash-functions/isNil.js"),
        "lodash/isFunction": path.resolve(__dirname, "./src/utils/lodash-functions/isFunction.js"),
        "lodash/isObject": path.resolve(__dirname, "./src/utils/lodash-functions/isObject.js"),
        "lodash/isString": path.resolve(__dirname, "./src/utils/lodash-functions/isString.js"),
        "lodash/isEqual": path.resolve(__dirname, "./src/utils/lodash-functions/isEqual.js"),
        "lodash/sortBy": path.resolve(__dirname, "./src/utils/lodash-functions/sortBy.js"),
        "lodash/throttle": path.resolve(__dirname, "./src/utils/lodash-functions/throttle.js"),
        "lodash/last": path.resolve(__dirname, "./src/utils/lodash-functions/last.js"),
        "lodash/get": path.resolve(__dirname, "./src/utils/lodash-functions/get.js"),
        "lodash/upperFirst": path.resolve(__dirname, "./src/utils/lodash-functions/upperFirst.js"),
        "lodash/uniqBy": path.resolve(__dirname, "./src/utils/lodash-functions/uniqBy.js"),
        "lodash/max": path.resolve(__dirname, "./src/utils/lodash-functions/max.js"),
        "lodash/mapValues": path.resolve(__dirname, "./src/utils/lodash-functions/mapValues.js"),
        "lodash/find": path.resolve(__dirname, "./src/utils/lodash-functions/find.js"),
        "lodash/range": path.resolve(__dirname, "./src/utils/lodash-functions/range.js"),
        "lodash/maxBy": path.resolve(__dirname, "./src/utils/lodash-functions/maxBy.js"),
        "lodash/minBy": path.resolve(__dirname, "./src/utils/lodash-functions/minBy.js"),
        "lodash/first": path.resolve(__dirname, "./src/utils/lodash-functions/first.js"),
        "lodash/some": path.resolve(__dirname, "./src/utils/lodash-functions/some.js"),
        "lodash/isNaN": path.resolve(__dirname, "./src/utils/lodash-functions/isNaN.js"),
        "lodash/omit": path.resolve(__dirname, "./src/utils/lodash-functions/omit.js"),
        "lodash/min": path.resolve(__dirname, "./src/utils/lodash-functions/min.js"),
        "lodash/sumBy": path.resolve(__dirname, "./src/utils/lodash-functions/sumBy.js"),
        "lodash/isNumber": path.resolve(__dirname, "./src/utils/lodash-functions/isNumber.js"),
        "lodash/flatMap": path.resolve(__dirname, "./src/utils/lodash-functions/flatMap.js"),
        "lodash/isPlainObject": path.resolve(__dirname, "./src/utils/lodash-functions/isPlainObject.js"),
        "lodash/isBoolean": path.resolve(__dirname, "./src/utils/lodash-functions/isBoolean.js"),
        "lodash/every": path.resolve(__dirname, "./src/utils/lodash-functions/every.js"),
        "lodash/memoize": path.resolve(__dirname, "./src/utils/lodash-functions/memoize.js"),
        // Fix the lodash import to point directly to the index.js file
        "lodash": path.resolve(__dirname, "./src/utils/lodash-functions/index.js"),
      },
    },
    css: {
      devSourcemap: false,
    },
    build: {
      sourcemap: !isDev,
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          manualChunks: (id) => {
            // Core React libraries
            if (
              id.includes("node_modules/react/") ||
              id.includes("node_modules/react-dom/") ||
              id.includes("node_modules/react-router-dom/")
            ) {
              return "vendor-react";
            }

            // UI component libraries
            if (
              id.includes("node_modules/@radix-ui/") ||
              id.includes("node_modules/class-variance-authority/") ||
              id.includes("node_modules/clsx/") ||
              id.includes("node_modules/tailwind-merge/")
            ) {
              return "vendor-ui";
            }

            // Date and form libraries
            if (
              id.includes("node_modules/date-fns/") ||
              id.includes("node_modules/react-day-picker/") ||
              id.includes("node_modules/react-hook-form/") ||
              id.includes("node_modules/@hookform/")
            ) {
              return "vendor-forms";
            }

            // Charts and data visualization
            if (
              id.includes("node_modules/recharts/") ||
              id.includes("node_modules/d3-")
            ) {
              return "vendor-charts";
            }

            // Animation and styling
            if (
              id.includes("node_modules/framer-motion/") ||
              id.includes("node_modules/vaul/")
            ) {
              return "vendor-animations";
            }

            // Icons
            if (id.includes("node_modules/lucide-react/")) {
              return "vendor-icons";
            }

            // Supabase and API
            if (
              id.includes("node_modules/@supabase/") ||
              id.includes("node_modules/@tanstack/")
            ) {
              return "vendor-api";
            }

            // Any other node_modules
            if (id.includes("node_modules/")) {
              return "vendor-others";
            }
            
            return undefined;
          },
        },
      },
    },
    // Add esbuild options as a string literal for the logOverride
    esbuild: {
      logOverride: { "this-is-undefined-in-esm": "silent" },
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: "./src/tests/setup.ts",
    },
  };
});
