import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

interface SettingsCardProps {
  title: string;
  description?: string; // Description is optional
  children: React.ReactNode;
  className?: string; // Allow passing additional classes like mb-8 from SubscriptionCard
}

const SettingsCard: React.FC<SettingsCardProps> = ({ title, description, children, className }) => {
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {children}
      </CardContent>
    </Card>
  );
};

export default SettingsCard;
