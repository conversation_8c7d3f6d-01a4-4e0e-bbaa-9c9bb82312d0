
import { useWishlistLoading } from './hooks/useWishlistLoading';
import { useWishlistItems } from './hooks/useWishlistItems';
import { useWishlistOperations } from './hooks/useWishlistOperations';

export const useWishlistProvider = () => {
  const { isLoading, setIsLoading, isAddingToWishlist, setIsAddingToWishlist } = useWishlistLoading();
  const { wishlistItems, setWishlistItems } = useWishlistItems();
  const { addToWishlist, removeFromWishlist, clearWishlist, isInWishlist } = useWishlistOperations(
    wishlistItems,
    setWishlistItems,
    setIsLoading,
    setIsAddingToWishlist
  );

  return {
    wishlistItems,
    isLoading,
    isAddingToWishlist,
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist
  };
};
