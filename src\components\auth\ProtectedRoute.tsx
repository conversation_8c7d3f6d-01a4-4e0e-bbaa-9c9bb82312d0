import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/auth/AuthContext';
import { UserRole } from '@/constants/roles';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRoles
}) => {
  const { user, isAuthenticated, isLoading } = useAuth();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading) {
      // console.log("ProtectedRoute Check:", {
      //   path: location.pathname,
      //   isAuthenticated,
      //   userRole: user?.role,
      //   requiredRoles
      // });
    }
  }, [isAuthenticated, isLoading, user, location.pathname, requiredRoles]);

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen gap-4 bg-white">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="text-gray-500 text-lg">Checking sign-in status…</p>
      </div>
    );
  }

  if (!isAuthenticated || !user) {
    toast.error("Please sign in to access this page.");
    return <Navigate to="/signin" state={{ from: location }} replace />;
  }

  if (requiredRoles && requiredRoles.length > 0 && !requiredRoles.includes(user.role as UserRole)) {
    toast.error("Access Denied: You don't have the necessary permissions.");
    return <Navigate to="/unauthorized" state={{ requiredRoles }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
