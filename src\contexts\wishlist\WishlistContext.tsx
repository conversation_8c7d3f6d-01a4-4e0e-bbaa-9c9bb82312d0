
import React, { createContext, useContext, useState, useEffect } from 'react';
import { Product } from '@/types/unified-product';
import { WishlistContextType, WishlistItem } from './types';
import { databaseToUIProduct } from '@/utils/typeConverters';
import { toast } from 'sonner';

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export const WishlistProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [items, setItems] = useState<WishlistItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddingToWishlist, setIsAddingToWishlist] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Load wishlist from localStorage on mount
  useEffect(() => {
    const savedWishlist = localStorage.getItem('wishlist');
    if (savedWishlist) {
      try {
        const parsedWishlist = JSON.parse(savedWishlist);
        // Convert simple products to WishlistItems if needed
        const formattedItems: WishlistItem[] = parsedWishlist.map((item: any) => {
          if ('product' in item) {
            return item;
          } else {
            // If it's just a product, convert to WishlistItem format
            return {
              id: item.id,
              product_id: item.id,
              product: item
            };
          }
        });
        setItems(formattedItems);
      } catch (err) {
        console.error('Failed to load wishlist from localStorage:', err);
      }
    }
  }, []);

  // Save wishlist to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('wishlist', JSON.stringify(items));
  }, [items]);

  const addToWishlist = async (product: Product) => {
    try {
      setIsAddingToWishlist(true);
      setError(null);

      // Check if product already exists in wishlist
      const existingItem = items.find(item => item.product_id === product.id);
      if (existingItem) {
        toast("Product already in wishlist");
        return;
      }

      // Convert product if needed
      const uiProduct = 'sale_price' in product ? databaseToUIProduct(product) : product;

      // Add new item to wishlist
      const wishlistItem: WishlistItem = {
        id: crypto.randomUUID(),
        product_id: uiProduct.id,
        product: uiProduct
      };
      
      setItems(prev => [...prev, wishlistItem]);
      toast("Product added to wishlist");
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to add item to wishlist'));
      throw err;
    } finally {
      setIsAddingToWishlist(false);
    }
  };

  // Support for string ID parameter or full product object
  const addItem = async (productIdOrProduct: string | Product) => {
    if (typeof productIdOrProduct === 'string') {
      // Find product in existing items
      const existingItem = items.find(item => item.product_id === productIdOrProduct);
      if (existingItem) return; // Already in wishlist
      
      // This is a mock product - in a real app you would fetch it
      const mockProduct: Product = {
        id: productIdOrProduct,
        name: `Product ${productIdOrProduct}`,
        description: 'Product added to wishlist',
        price: 0,
        stock_quantity: 0,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        currency: 'USD',
        sku: '',
        images: [],
        category: '',
        tags: [],
        store_id: '',
        available: true,
        sale_price: null,
        specifications: null,
        views_count: 0,
      };
      
      await addToWishlist(mockProduct);
    } else {
      await addToWishlist(productIdOrProduct);
    }
  };

  const removeFromWishlist = async (productId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      setItems(prev => prev.filter(item => item.product_id !== productId));
      toast("Product removed from wishlist");
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to remove item from wishlist'));
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Alias removeItem to removeFromWishlist for API consistency
  const removeItem = removeFromWishlist;

  const isInWishlist = (productId: string) => {
    return items.some(item => item.product_id === productId);
  };

  // Helper function to clear all items from wishlist
  const clearWishlist = async () => {
    try {
      setIsLoading(true);
      setError(null);
      setItems([]);
      toast("Wishlist cleared");
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to clear wishlist'));
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <WishlistContext.Provider
      value={{
        items,
        isLoading,
        error,
        addToWishlist,
        removeFromWishlist,
        isInWishlist,
        isAddingToWishlist,
        clearWishlist,
        // Add these aliases for consistency with other contexts
        addItem,
        removeItem
      }}
    >
      {children}
    </WishlistContext.Provider>
  );
};

export const useWishlist = () => {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
};
