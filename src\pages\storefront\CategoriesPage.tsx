
import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';
import { Container } from '@/components/ui/container';
import { useProduct } from '@/contexts';
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList, 
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from '@/components/ui/breadcrumb';
import { HomeIcon } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

const CategoriesPage: React.FC = () => {
  const { products, isLoading } = useProduct();
  
  // Extract unique categories from products
  const categories = useMemo(() => {
    if (!products.length) return [];
    
    const categoryMap = products.reduce((acc: Record<string, number>, product) => {
      if (product.category && product.is_active && (product.stock_quantity || 0) > 0) {
        acc[product.category] = (acc[product.category] || 0) + 1;
      }
      return acc;
    }, {});
    
    return Object.entries(categoryMap)
      .sort((a, b) => b[1] - a[1]) // Sort by product count
      .map(([category, count]) => ({ 
        name: category,
        count,
        slug: category.toLowerCase().replace(/\s+/g, '-')
      }));
  }, [products]);

  // Generate background colors for categories
  const getCategoryColor = (index: number) => {
    const colors = [
      'bg-blue-100 hover:bg-blue-200 border-blue-200',
      'bg-green-100 hover:bg-green-200 border-green-200',
      'bg-yellow-100 hover:bg-yellow-200 border-yellow-200',
      'bg-purple-100 hover:bg-purple-200 border-purple-200',
      'bg-pink-100 hover:bg-pink-200 border-pink-200',
      'bg-indigo-100 hover:bg-indigo-200 border-indigo-200',
      'bg-red-100 hover:bg-red-200 border-red-200',
      'bg-teal-100 hover:bg-teal-200 border-teal-200',
    ];
    
    return colors[index % colors.length];
  };

  return (
    <Container className="py-8 md:py-12">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/shop">
              <HomeIcon className="h-3.5 w-3.5 mr-1" />
              Home
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Categories</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      <h1 className="text-3xl font-bold mb-8">Shop by Category</h1>
      
      {isLoading ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-24 bg-gray-100 animate-pulse rounded-md"></div>
          ))}
        </div>
      ) : categories.length === 0 ? (
        <Card>
          <CardContent className="py-8 text-center">
            <p className="text-muted-foreground">No categories available.</p>
            <Link to="/shop" className="text-primary hover:underline mt-2 inline-block">
              Back to Shop
            </Link>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {categories.map((category, index) => (
            <Link 
              to={`/shop/category/${category.slug}`} 
              key={category.name}
              className={cn(
                "flex flex-col justify-center items-center p-6 rounded-md border transition-colors duration-200",
                "hover:shadow-md text-center h-full",
                getCategoryColor(index)
              )}
            >
              <h2 className="font-medium text-lg">{category.name}</h2>
              <p className="text-sm text-muted-foreground mt-1">
                {category.count} {category.count === 1 ? 'product' : 'products'}
              </p>
            </Link>
          ))}
        </div>
      )}
    </Container>
  );
};

export default CategoriesPage;
