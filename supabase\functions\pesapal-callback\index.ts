
import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.36.0';

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Get payment status from Pesapal
async function getPaymentStatusFromPesapal(trackingId: string) {
  try {
    console.log('Getting payment status for tracking ID:', trackingId);
    
    // In a real implementation, we would make an actual API call to Pesapal
    // The URL would be something like: ${pesapalApiUrl}/api/Transactions/GetTransactionStatus
    
    // For now, we'll simulate the response
    // In production, this would be a real API call:
    /*
    const response = await fetch(`${pesapalApiUrl}/api/Transactions/GetTransactionStatus?orderTrackingId=${trackingId}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get payment status: ${response.status} ${errorText}`);
    }
    
    return await response.json();
    */
    
    // Simulated response for development
    return {
      status: 'COMPLETED',
      payment_method: 'VISA',
      payment_amount: 100,
      payment_date: new Date().toISOString(),
      payment_description: 'Payment for order',
      completed_at: new Date().toISOString()
    };
  } catch (error) {
    console.error('Error getting payment status from Pesapal:', error);
    return null;
  }
}

// Handle requests
serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Parse URL to get query parameters
    const url = new URL(req.url);
    const pesapalTrackingId = url.searchParams.get('pesapal_transaction_tracking_id');
    const pesapalMerchantReference = url.searchParams.get('pesapal_merchant_reference');
    const pesapalNotification = url.searchParams.get('pesapal_notification_type');
    
    console.log('Pesapal callback received:', {
      pesapalTrackingId,
      pesapalMerchantReference,
      pesapalNotification
    });
    
    // Validate required parameters
    if (!pesapalTrackingId || !pesapalMerchantReference) {
      console.error('Missing required parameters:', { pesapalTrackingId, pesapalMerchantReference });
      return new Response(
        JSON.stringify({
          error: 'Missing required parameters',
          details: 'Both tracking ID and merchant reference are required',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }
    
    // Get payment status from Pesapal
    const paymentStatus = await getPaymentStatusFromPesapal(pesapalTrackingId);
    
    if (!paymentStatus) {
      console.error('Failed to get payment status from Pesapal');
      return new Response(
        JSON.stringify({
          error: 'Failed to get payment status',
          details: 'Could not retrieve payment status from payment gateway',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      );
    }
    
    // Find the order using merchant reference (orderId)
    const orderId = pesapalMerchantReference;
    
    // Find the transaction for this order
    const { data: transactions, error: txError } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('order_id', orderId)
      .limit(1);
    
    if (txError) {
      console.error('Database error when fetching transaction:', txError);
      throw txError;
    }
    
    if (!transactions || transactions.length === 0) {
      console.error(`No transaction found for order: ${orderId}`);
      return new Response(
        JSON.stringify({
          error: 'Transaction not found',
          details: `No transaction exists for order: ${orderId}`,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      );
    }
    
    const transaction = transactions[0];
    const isPaymentCompleted = paymentStatus.status === 'COMPLETED';
    
    // Update transaction
    const { error: updateError } = await supabase
      .from('payment_transactions')
      .update({
        status: isPaymentCompleted ? 'completed' : 'failed',
        payment_data: {
          ...transaction.payment_data,
          pesapal_status_response: paymentStatus,
          pesapal_tracking_id: pesapalTrackingId,
          payment_method: paymentStatus.payment_method,
          completed_at: isPaymentCompleted ? paymentStatus.completed_at : null,
          failure_reason: isPaymentCompleted ? null : 'Payment not completed',
          updated_at: new Date().toISOString()
        }
      })
      .eq('id', transaction.id);
    
    if (updateError) {
      console.error('Error updating transaction:', updateError);
      throw updateError;
    }
    
    // Update order status
    const { error: orderUpdateError } = await supabase
      .from('orders')
      .update({ 
        status: isPaymentCompleted ? 'paid' : 'payment_failed' 
      })
      .eq('id', orderId);
    
    if (orderUpdateError) {
      console.error('Error updating order status:', orderUpdateError);
      throw orderUpdateError;
    }
    
    console.log(`Successfully processed Pesapal callback for order ${orderId}`);
    
    // Redirect to order confirmation page
    return new Response(
      null,
      {
        headers: {
          ...corsHeaders,
          'Location': `/shop/order-confirmation?order_id=${pesapalMerchantReference}`
        },
        status: 302,
      }
    );
  } catch (error) {
    console.error('Error processing Pesapal callback:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
