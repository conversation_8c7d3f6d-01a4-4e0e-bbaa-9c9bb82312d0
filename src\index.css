@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 33% 99%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 210 100% 47%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222 47% 11%;

    --muted: 210 40% 96%;
    --muted-foreground: 215 16% 47%;

    --accent: 216 100% 97%;
    --accent-foreground: 222 47% 11%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 222 84% 5%;

    --radius: 0.5rem;

    --sidebar-background: 210 33% 99%;
    --sidebar-foreground: 215 16% 30%;
    --sidebar-primary: 215 16% 15%;
    --sidebar-primary-foreground: 210 33% 99%;
    --sidebar-accent: 215 16% 95%;
    --sidebar-accent-foreground: 215 16% 15%;
    --sidebar-border: 215 16% 90%;
    --sidebar-ring: 210 100% 47%;
  }

  .dark {
    --background: 222 47% 4%;
    --foreground: 210 40% 98%;

    --card: 222 47% 5%;
    --card-foreground: 210 40% 98%;

    --popover: 222 47% 5%;
    --popover-foreground: 210 40% 98%;

    --primary: 210, 100%, 60%;
    --primary-foreground: 222 47% 11%;

    --secondary: 217 33% 17%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217 33% 17%;
    --muted-foreground: 215 20% 65%;

    --accent: 217 33% 18%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;

    --border: 217 33% 18%;
    --input: 217 33% 18%;
    --ring: 213 27% 84%;
    
    --sidebar-background: 222 47% 5%;
    --sidebar-foreground: 215 16% 90%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 220 47% 12%;
    --sidebar-accent-foreground: 215 16% 90%;
    --sidebar-border: 220 47% 12%;
    --sidebar-ring: 210 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  
  html, body {
    @apply h-full antialiased scroll-smooth;
  }
  
  body {
    @apply bg-background text-foreground font-sans;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-display tracking-tight;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Focus styles */
  :focus-visible {
    @apply outline-none ring-2 ring-primary ring-offset-2;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary/20 text-foreground;
  }
  
  /* Custom scrollbar for webkit browsers */
  ::-webkit-scrollbar {
    @apply w-2 h-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/40;
  }
}

@layer components {
  /* Glass card effect */
  .glass {
    @apply bg-white/80 dark:bg-black/60 backdrop-blur-md border border-white/20 dark:border-white/10;
  }
  
  /* Container for sections */
  .section-container {
    @apply container px-4 md:px-6 py-12 md:py-24;
  }
  
  /* Text gradient */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-mduka-600 to-blue-500;
  }
  
  /* Button hover effect */
  .btn-hover-effect {
    @apply relative overflow-hidden transition-all duration-300;
  }
  
  .btn-hover-effect:after {
    @apply content-[''] absolute inset-0 bg-white/20 dark:bg-white/10 scale-x-0 origin-left transition-transform duration-300 ease-out;
  }
  
  .btn-hover-effect:hover:after {
    @apply scale-x-100;
  }
  
  /* Animated underline */
  .animated-underline {
    @apply relative;
  }
  
  .animated-underline:after {
    @apply content-[''] absolute left-0 bottom-0 w-0 h-0.5 bg-primary transition-all duration-300 ease-in-out;
  }
  
  .animated-underline:hover:after {
    @apply w-full;
  }
  
  /* Perspective card */
  .perspective-card {
    @apply transition-all duration-300 ease-out;
    transform-style: preserve-3d;
    perspective: 1000px;
  }
  
  .perspective-card:hover {
    transform: translateY(-5px) rotateX(5deg);
  }
}

@layer utilities {
  /* Backdrop filter utilities */
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* Text balance for better typography */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Text pretty for better breaks */
  .text-pretty {
    text-wrap: pretty;
  }
  
  /* Rotate on hover */
  .hover-rotate {
    @apply transition-transform duration-300;
  }
  
  .hover-rotate:hover {
    @apply rotate-3;
  }
  
  /* Scale on hover */
  .hover-scale {
    @apply transition-transform duration-300;
  }
  
  .hover-scale:hover {
    @apply scale-105;
  }
  
  /* Responsive padding helpers */
  .px-responsive {
    @apply px-4 sm:px-6 md:px-8 lg:px-12;
  }
  
  .py-responsive {
    @apply py-8 md:py-12 lg:py-16;
  }
}

/* Smooth image rendering */
img {
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

/* Font loading and optimization */
@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: normal;
  src: url('https://rsms.me/inter/font-files/Inter-roman.var.woff2?v=3.19') format('woff2');
}

@font-face {
  font-family: 'Inter var';
  font-weight: 100 900;
  font-display: swap;
  font-style: italic;
  src: url('https://rsms.me/inter/font-files/Inter-italic.var.woff2?v=3.19') format('woff2');
}

/* ===============================================
   PWA & MOBILE RESPONSIVENESS ENHANCEMENTS
   =============================================== */

/* Safe area handling for devices with notches */
@supports (padding: max(0px)) {
  body {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
  }
  
  .safe-top {
    padding-top: max(12px, env(safe-area-inset-top));
  }
  
  .safe-bottom {
    padding-bottom: max(12px, env(safe-area-inset-bottom));
  }
}

/* Mobile-first responsive design patterns */
@layer components {
  /* Mobile navigation */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-md border-t border-border z-50 safe-bottom;
    transform: translateY(0);
    transition: transform 0.3s ease-in-out;
  }
  
  .mobile-nav.hidden {
    transform: translateY(100%);
  }
  
  /* Mobile container */
  .mobile-container {
    @apply px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto;
  }
  
  /* Touch-friendly buttons */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }
  
  /* Mobile card layout */
  .mobile-card {
    @apply bg-background border border-border rounded-lg p-4 shadow-sm;
  }
  
  /* Full viewport height on mobile */
  .mobile-full-height {
    min-height: 100vh;
    min-height: 100dvh; /* Dynamic viewport height for mobile */
  }
  
  /* Mobile-optimized spacing */
  .mobile-section {
    @apply py-8 sm:py-12 lg:py-16;
  }
  
  .mobile-gap {
    @apply space-y-4 sm:space-y-6 lg:space-y-8;
  }
}

@layer utilities {
  /* Typography responsiveness */
  .text-responsive-xs {
    @apply text-xs sm:text-sm;
  }
  
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }
  
  .text-responsive-base {
    @apply text-base sm:text-lg;
  }
  
  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }
  
  .text-responsive-xl {
    @apply text-xl sm:text-2xl lg:text-3xl;
  }
  
  .text-responsive-2xl {
    @apply text-2xl sm:text-3xl lg:text-4xl;
  }
  
  .text-responsive-3xl {
    @apply text-3xl sm:text-4xl lg:text-5xl;
  }
  
  /* Grid responsiveness */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4;
  }
  
  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2;
  }
  
  .grid-responsive-3 {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }
  
  /* Flex responsiveness */
  .flex-responsive {
    @apply flex flex-col sm:flex-row;
  }
  
  .flex-responsive-reverse {
    @apply flex flex-col-reverse sm:flex-row;
  }
  
  /* Hide/show utilities */
  .mobile-only {
    @apply block sm:hidden;
  }
  
  .desktop-only {
    @apply hidden sm:block;
  }
  
  .tablet-up {
    @apply hidden md:block;
  }
  
  /* Touch-optimized spacing */
  .touch-spacing {
    @apply p-3 sm:p-4 lg:p-6;
  }
  
  .touch-margin {
    @apply m-3 sm:m-4 lg:m-6;
  }
}

/* PWA-specific styles */
@media (display-mode: standalone) {
  /* Styles when app is installed as PWA */
  body {
    user-select: none; /* Prevent text selection in standalone mode */
    -webkit-user-select: none;
    -webkit-touch-callout: none; /* Disable iOS callout */
  }
  
  /* Allow text selection for specific elements */
  input, textarea, [contenteditable] {
    user-select: text;
    -webkit-user-select: text;
  }
  
  /* Hide browser UI elements in standalone mode */
  .pwa-hidden {
    display: none !important;
  }
}

/* iOS specific optimizations */
@supports (-webkit-appearance: none) {
  /* iOS Safari specific styles */
  .ios-scroll {
    -webkit-overflow-scrolling: touch;
  }
  
  /* Fix iOS zoom on input focus */
  input[type="text"], 
  input[type="email"], 
  input[type="password"], 
  input[type="number"], 
  textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
  
  /* iOS safe area adjustments */
  @media screen and (max-width: 768px) {
    .ios-padding {
      padding-left: max(16px, env(safe-area-inset-left));
      padding-right: max(16px, env(safe-area-inset-right));
    }
  }
}

/* Android specific optimizations */
@media screen and (max-width: 768px) {
  /* Android Chrome address bar handling */
  .android-viewport {
    min-height: 100vh;
    min-height: calc(100vh - env(keyboard-inset-height, 0px));
  }
}

/* High DPI display optimizations */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .crisp-edges {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Print styles for PWA */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
}

/* Loading states for better UX */
.loading-skeleton {
  @apply animate-pulse bg-muted/50 rounded;
}

.loading-spinner {
  @apply animate-spin rounded-full border-2 border-muted border-t-primary;
}

/* Enhanced touch interactions */
.touch-feedback {
  @apply active:scale-95 transition-transform duration-75;
}

.touch-feedback:active {
  transform: scale(0.95);
}

/* Improved focus styles for accessibility */
@layer base {
  /* Enhanced focus styles for keyboard navigation */
  .focus-enhanced:focus-visible {
    @apply outline-none ring-2 ring-offset-2 ring-primary ring-offset-background;
  }
  
  /* Skip link for accessibility */
  .skip-link {
    @apply absolute left-0 top-0 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-br-md transform -translate-y-full focus:translate-y-0 transition-transform;
  }
}

/* Responsive breakpoint indicators (development only) */
@media (min-width: 640px) {
  .debug-breakpoint::before {
    content: "sm";
  }
}

@media (min-width: 768px) {
  .debug-breakpoint::before {
    content: "md";
  }
}

@media (min-width: 1024px) {
  .debug-breakpoint::before {
    content: "lg";
  }
}

@media (min-width: 1280px) {
  .debug-breakpoint::before {
    content: "xl";
  }
}

@media (min-width: 1536px) {
  .debug-breakpoint::before {
    content: "2xl";
  }
}
