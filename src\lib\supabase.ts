// Re-export the main Supabase client to maintain compatibility
// This file is deprecated - use @/integrations/supabase/client instead
export { supabase } from '@/integrations/supabase/client';

// Re-export types for backward compatibility
export type { Store, InsertStore, UpdateStore, DatabaseProduct, InsertProduct, UpdateProduct, InsertOrder, Order, UpdateOrder, Customer, InsertCustomer, UpdateCustomer, Wishlist, InsertWishlist, Category, InsertCategory, UpdateCategory } from '@/types/database';

// Store operations
export const storeOperations = {
  create: async (data: InsertStore): Promise<{ data: Store | null, error: Error | null }> => {
    const { data: store, error } = await supabase
      .from('stores')
      .insert(data)
      .select()
      .single();

    return { data: store, error };
  },

  getById: async (id: string): Promise<{ data: Store | null, error: Error | null }> => {
    const { data: store, error } = await supabase
      .from('stores')
      .select('*')
      .eq('id', id)
      .single();

    return { data: store, error };
  },

  getByUrl: async (url: string): Promise<{ data: Store | null, error: Error | null }> => {
    const { data: store, error } = await supabase
      .from('stores')
      .select('*')
      .eq('store_url', url)
      .single();

    return { data: store, error };
  },

  update: async (id: string, data: UpdateStore): Promise<{ data: Store | null, error: Error | null }> => {
    const { data: store, error } = await supabase
      .from('stores')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    return { data: store, error };
  },

  delete: async (id: string): Promise<{ error: Error | null }> => {
    const { error } = await supabase
      .from('stores')
      .delete()
      .eq('id', id);

    return { error };
  }
};

// Product operations
export const productOperations = {
  create: async (data: InsertProduct): Promise<{ data: DatabaseProduct | null, error: Error | null }> => {
    const { data: product, error } = await supabase
      .from('products')
      .insert(data)
      .select()
      .single();

    return { data: product, error };
  },

  getById: async (id: string): Promise<{ data: DatabaseProduct | null, error: Error | null }> => {
    const { data: product, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', id)
      .single();

    return { data: product, error };
  },

  getByStore: async (storeId: string): Promise<{ data: DatabaseProduct[] | null, error: Error | null }> => {
    const { data: products, error } = await supabase
      .from('products')
      .select('*')
      .eq('store_id', storeId)
      .eq('is_active', true);

    return { data: products, error };
  },

  update: async (id: string, data: UpdateProduct): Promise<{ data: DatabaseProduct | null, error: Error | null }> => {
    const { data: product, error } = await supabase
      .from('products')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    return { data: product, error };
  },

  delete: async (id: string): Promise<{ error: Error | null }> => {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', id);

    return { error };
  }
};

// Order operations
export const orderOperations = {
  create: async (data: InsertOrder): Promise<{ data: Order | null, error: Error | null }> => {
    const { data: order, error } = await supabase
      .from('orders')
      .insert(data)
      .select()
      .single();

    return { data: order, error };
  },

  getById: async (id: string): Promise<{ data: Order | null, error: Error | null }> => {
    const { data: order, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          product:products (*)
        )
      `)
      .eq('id', id)
      .single();

    return { data: order, error };
  },

  getByCustomer: async (customerId: string): Promise<{ data: Order[] | null, error: Error | null }> => {
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          product:products (*)
        )
      `)
      .eq('customer_id', customerId)
      .order('created_at', { ascending: false });

    return { data: orders, error };
  },

  getByStore: async (storeId: string): Promise<{ data: Order[] | null, error: Error | null }> => {
    const { data: orders, error } = await supabase
      .from('orders')
      .select(`
        *,
        order_items (
          *,
          product:products (*)
        )
      `)
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    return { data: orders, error };
  },

  update: async (id: string, data: UpdateOrder): Promise<{ data: Order | null, error: Error | null }> => {
    const { data: order, error } = await supabase
      .from('orders')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    return { data: order, error };
  }
};

// Customer operations
export const customerOperations = {
  create: async (data: InsertCustomer): Promise<{ data: Customer | null, error: Error | null }> => {
    const { data: customer, error } = await supabase
      .from('customers')
      .insert(data)
      .select()
      .single();

    return { data: customer, error };
  },

  getById: async (id: string): Promise<{ data: Customer | null, error: Error | null }> => {
    const { data: customer, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', id)
      .single();

    return { data: customer, error };
  },

  getByUser: async (userId: string, storeId: string): Promise<{ data: Customer | null, error: Error | null }> => {
    const { data: customer, error } = await supabase
      .from('customers')
      .select('*')
      .eq('user_id', userId)
      .eq('store_id', storeId)
      .single();

    return { data: customer, error };
  },

  update: async (id: string, data: UpdateCustomer): Promise<{ data: Customer | null, error: Error | null }> => {
    const { data: customer, error } = await supabase
      .from('customers')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    return { data: customer, error };
  }
};

// Wishlist operations
export const wishlistOperations = {
  add: async (data: InsertWishlist): Promise<{ data: Wishlist | null, error: Error | null }> => {
    const { data: wishlist, error } = await supabase
      .from('wishlists')
      .insert(data)
      .select()
      .single();

    return { data: wishlist, error };
  },

  remove: async (customerId: string, productId: string): Promise<{ error: Error | null }> => {
    const { error } = await supabase
      .from('wishlists')
      .delete()
      .eq('customer_id', customerId)
      .eq('product_id', productId);

    return { error };
  },

  getByCustomer: async (customerId: string): Promise<{ data: Wishlist[] | null, error: Error | null }> => {
    const { data: wishlist, error } = await supabase
      .from('wishlists')
      .select(`
        *,
        product:products (*)
      `)
      .eq('customer_id', customerId);

    return { data: wishlist, error };
  }
};

// Category operations
export const categoryOperations = {
  create: async (data: InsertCategory): Promise<{ data: Category | null, error: Error | null }> => {
    const { data: category, error } = await supabase
      .from('categories')
      .insert(data)
      .select()
      .single();

    return { data: category, error };
  },

  getById: async (id: string): Promise<{ data: Category | null, error: Error | null }> => {
    const { data: category, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    return { data: category, error };
  },

  getByStore: async (storeId: string): Promise<{ data: Category[] | null, error: Error | null }> => {
    const { data: categories, error } = await supabase
      .from('categories')
      .select('*')
      .eq('store_id', storeId)
      .order('name');

    return { data: categories, error };
  },

  update: async (id: string, data: UpdateCategory): Promise<{ data: Category | null, error: Error | null }> => {
    const { data: category, error } = await supabase
      .from('categories')
      .update(data)
      .eq('id', id)
      .select()
      .single();

    return { data: category, error };
  },

  delete: async (id: string): Promise<{ error: Error | null }> => {
    const { error } = await supabase
      .from('categories')
      .delete()
      .eq('id', id);

    return { error };
  }
};
