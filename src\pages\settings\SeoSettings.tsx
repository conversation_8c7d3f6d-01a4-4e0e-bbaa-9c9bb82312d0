
import React, { useState } from "react";
import { SEO } from "@/components/SEO";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";

const seoFormSchema = z.object({
  title: z.string().min(10, {
    message: "Title must be at least 10 characters.",
  }).max(70, {
    message: "Title should not exceed 70 characters for optimal SEO."
  }),
  description: z.string().min(50, {
    message: "Description must be at least 50 characters.",
  }).max(160, {
    message: "Description should not exceed 160 characters for optimal SEO."
  }),
  keywords: z.string().min(3, {
    message: "Please add at least a few keywords.",
  }),
  ogImage: z.string().url({
    message: "Please enter a valid URL for the og:image."
  }).optional().or(z.literal('')),
  noIndex: z.boolean().default(false)
});

type SeoFormValues = z.infer<typeof seoFormSchema>;

const SeoSettings: React.FC = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<SeoFormValues>({
    resolver: zodResolver(seoFormSchema),
    defaultValues: {
      title: "My Store | Best Products Online",
      description: "We offer high-quality products at competitive prices. Browse our catalog and find the perfect items for your needs.",
      keywords: "online store, e-commerce, products, shopping",
      ogImage: "",
      noIndex: false
    },
  });

  function onSubmit(data: SeoFormValues) {
    setIsSubmitting(true);
    
    // Simulate API call
    setTimeout(() => {
      console.log("SEO Settings saved:", data);
      toast.success("SEO settings saved successfully!");
      setIsSubmitting(false);
    }, 1000);
  }

  return (
    <div className="container py-10">
      <SEO 
        title="SEO Settings | M-Duka Dashboard"
        description="Configure search engine optimization settings for your M-Duka store"
        noIndex={true}
      />
      
      <div className="mb-8">
        <h1 className="text-2xl font-bold mb-2">SEO Settings</h1>
        <p className="text-muted-foreground">
          Optimize your store for search engines to attract more visitors
        </p>
      </div>
      
      <div className="grid gap-8">
        <Card>
          <CardHeader>
            <CardTitle>Page Metadata</CardTitle>
            <CardDescription>
              Configure how your store appears in search engine results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Page Title</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="My Store | Best Products Online" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Recommended length: 50-60 characters
                        <Badge variant={field.value.length > 70 ? "destructive" : field.value.length > 60 ? "outline" : "secondary"} className="ml-2">
                          {field.value.length} / 70
                        </Badge>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meta Description</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="We offer high-quality products at competitive prices..." 
                          className="resize-none" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Recommended length: 120-155 characters
                        <Badge variant={field.value.length > 160 ? "destructive" : field.value.length > 155 ? "outline" : "secondary"} className="ml-2">
                          {field.value.length} / 160
                        </Badge>
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="keywords"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Keywords</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="online store, e-commerce, products, shopping" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        Separate keywords with commas
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="ogImage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Social Image URL</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="https://example.com/image.jpg" 
                          {...field} 
                        />
                      </FormControl>
                      <FormDescription>
                        The image that will be shown when sharing your store on social media
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="noIndex"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">
                          Hide from Search Engines
                        </FormLabel>
                        <FormDescription>
                          If enabled, search engines will not index your store
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <Button type="submit" disabled={isSubmitting}>
                  {isSubmitting ? "Saving..." : "Save SEO Settings"}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SeoSettings;
