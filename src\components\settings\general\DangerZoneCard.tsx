
import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';

interface DangerZoneCardProps {
  disableCheckout: boolean;
  setDisableCheckout: (value: boolean) => void;
  disableStore: boolean;
  setDisableStore: (value: boolean) => void;
  passwordProtect: boolean;
  setPasswordProtect: (value: boolean) => void;
}

const DangerZoneCard = ({
  disableCheckout,
  setDisableCheckout,
  disableStore,
  setDisableStore,
  passwordProtect,
  setPasswordProtect
}: DangerZoneCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-destructive">Danger Zone</CardTitle>
        <CardDescription>
          Critical actions that can affect your store
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Disable checkout</Label>
            <p className="text-sm text-muted-foreground">
              Entry points to checkout will be disabled and customers cannot place orders anymore.
            </p>
          </div>
          <Switch
            checked={disableCheckout}
            onCheckedChange={setDisableCheckout}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Disable store</Label>
            <p className="text-sm text-muted-foreground">
              Store will be disabled and all visitors will be redirected to WhatsApp.
            </p>
          </div>
          <Switch
            checked={disableStore}
            onCheckedChange={setDisableStore}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label>Protect store with password</Label>
            <p className="text-sm text-muted-foreground">
              Limit who can access your online store.
            </p>
          </div>
          <Switch
            checked={passwordProtect}
            onCheckedChange={setPasswordProtect}
          />
        </div>
        
        <div className="pt-4 border-t">
          <div className="space-y-4">
            <div>
              <h4 className="font-medium text-destructive">Delete store</h4>
              <p className="text-sm text-muted-foreground">
                Orders, products, customers, and all other store-related settings will be permanently deleted.
              </p>
            </div>
            <div className="space-y-2">
              <Input 
                placeholder="Enter mockstore1 to confirm deletion"
              />
              <Button variant="destructive">
                Delete Store
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DangerZoneCard;
