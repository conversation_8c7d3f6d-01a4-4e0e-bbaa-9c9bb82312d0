
/**
 * Native JavaScript utility functions to replace lodash
 */

// Equivalent to lodash's isNil
export function isNil(value: any): boolean {
  return value === null || value === undefined;
}

// Equivalent to lodash's isFunction
export function isFunction(value: any): boolean {
  return typeof value === 'function';
}

// Equivalent to lodash's isObject
export function isObject(value: any): boolean {
  const type = typeof value;
  return value !== null && (type === 'object' || type === 'function');
}

// Equivalent to lodash's isArray
export function isArray(value: any): boolean {
  return Array.isArray(value);
}

// Equivalent to lodash's isNumber
export function isNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value);
}

// Equivalent to lodash's isString
export function isString(value: any): boolean {
  return typeof value === 'string';
}

// Equivalent to lodash's isEqual - simplified version
export function isEqual(value: any, other: any): boolean {
  if (value === other) return true;
  
  if (value === null || other === null || 
      typeof value !== 'object' || typeof other !== 'object') {
    return value === other;
  }
  
  const keysA = Object.keys(value);
  const keysB = Object.keys(other);
  
  if (keysA.length !== keysB.length) return false;
  
  return keysA.every(key => isEqual(value[key], other[key]));
}

// Equivalent to lodash's isPlainObject
export function isPlainObject(value: any): boolean {
  if (!isObject(value)) return false;
  const prototype = Object.getPrototypeOf(value);
  return prototype === null || prototype === Object.prototype;
}

// Equivalent to lodash's sortBy
export function sortBy<T>(array: T[], iteratee: (item: T) => any): T[] {
  if (!array) return [];
  
  const mapped = array.map((item, index) => {
    let value = typeof iteratee === 'function' ? iteratee(item) : item[iteratee as keyof T];
    return { value, index, item };
  });
  
  mapped.sort((a, b) => {
    if (a.value < b.value) return -1;
    if (a.value > b.value) return 1;
    return 0;
  });
  
  return mapped.map(mapped => mapped.item);
}

// Equivalent to lodash's upperFirst
export function upperFirst(string: string): string {
  if (!string || typeof string !== 'string') return '';
  return string.charAt(0).toUpperCase() + string.slice(1);
}

// Equivalent to lodash's min
export function min(array: number[]): number | undefined {
  return array && array.length ? Math.min(...array) : undefined;
}

// Equivalent to lodash's max
export function max(array: number[]): number | undefined {
  return array && array.length ? Math.max(...array) : undefined;
}

// Equivalent to lodash's camelCase - simplified version
export function camelCase(string: string): string {
  if (!string || typeof string !== 'string') return '';
  return string
    .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
    .replace(/^[A-Z]/, chr => chr.toLowerCase());
}

// Equivalent to lodash's debounce
export function debounce<T extends (...args: any[]) => any>(
  func: T, 
  wait: number, 
  options: { leading?: boolean; trailing?: boolean; } = {}
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | undefined;
  let lastArgs: Parameters<T> | undefined;
  const leading = options.leading ?? false;
  const trailing = options.trailing ?? true;
  
  return function(this: any, ...args: Parameters<T>): void {
    const callNow = leading && !timeoutId;
    lastArgs = args;
    
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    
    timeoutId = setTimeout(() => {
      if (trailing && lastArgs) {
        func.apply(this, lastArgs);
      }
      timeoutId = undefined;
      lastArgs = undefined;
    }, wait);
    
    if (callNow) {
      func.apply(this, args);
    }
  };
}

// Equivalent to lodash's throttle
export function throttle<T extends (...args: any[]) => any>(
  func: T, 
  wait: number, 
  options: { leading?: boolean; trailing?: boolean; } = {}
): (...args: Parameters<T>) => void {
  let timeoutId: ReturnType<typeof setTimeout> | undefined;
  let lastArgs: Parameters<T> | undefined;
  let lastCallTime = 0;
  const leading = options.leading ?? true;
  const trailing = options.trailing ?? true;
  
  return function(this: any, ...args: Parameters<T>): void {
    const now = Date.now();
    const remaining = wait - (now - lastCallTime);
    
    lastArgs = args;
    
    if (remaining <= 0) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = undefined;
      }
      lastCallTime = now;
      func.apply(this, args);
    } else if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        lastCallTime = leading ? Date.now() : 0;
        timeoutId = undefined;
        if (lastArgs) {
          func.apply(this, lastArgs);
        }
      }, remaining);
    }
  };
}

// Equivalent to lodash's merge
export function merge<T extends object>(object: T, ...sources: any[]): T {
  if (!sources.length) return object;
  
  const source = sources.shift();
  
  if (isObject(object) && isObject(source)) {
    for (const key in source) {
      if (isObject(source[key])) {
        if (!object[key]) Object.assign(object, { [key]: {} });
        merge(object[key] as object, source[key]);
      } else {
        Object.assign(object, { [key]: source[key] });
      }
    }
  }
  
  return merge(object, ...sources);
}

// Equivalent to lodash's cloneDeep
export function cloneDeep<T>(value: T): T {
  if (value === null || typeof value !== 'object') return value;
  
  if (Array.isArray(value)) {
    return value.map(cloneDeep) as unknown as T;
  }
  
  const result = {} as T;
  for (const key in value) {
    result[key] = cloneDeep(value[key]);
  }
  
  return result;
}

// Equivalent to lodash's uniqBy - simplified
export function uniqBy<T>(array: T[], iteratee: (item: T) => any): T[] {
  if (!array) return [];
  
  const seen = new Set();
  return array.filter(item => {
    const key = iteratee(item);
    if (seen.has(key)) return false;
    seen.add(key);
    return true;
  });
}

// Equivalent to lodash's flatMap
export function flatMap<T, U>(array: T[], iteratee: (item: T, index: number, array: T[]) => U[]): U[] {
  if (!array) return [];
  return array.map((item, index, arr) => iteratee(item, index, arr)).flat();
}

// Equivalent to lodash's last
export function last<T>(array: T[]): T | undefined {
  const length = array ? array.length : 0;
  return length ? array[length - 1] : undefined;
}

// Equivalent to lodash's get
export function get(object: any, path: string | string[], defaultValue?: any): any {
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  
  for (let i = 0; i < keys.length; i++) {
    if (result === undefined || result === null) {
      return defaultValue;
    }
    result = result[keys[i]];
  }
  
  return result === undefined ? defaultValue : result;
}
