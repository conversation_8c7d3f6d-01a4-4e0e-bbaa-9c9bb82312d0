
/**
 * Checks if value is a plain object (created by the Object constructor or with null prototype).
 *
 * @param {*} value - The value to check
 * @returns {boolean} Returns true if value is a plain object, else false
 */
function isPlainObject(value) {
  if (typeof value !== 'object' || value === null) return false;
  
  // Check if direct instance of Object constructor
  if (Object.getPrototypeOf(value) === null) return true;
  
  let proto = value;
  while (Object.getPrototypeOf(proto) !== null) {
    proto = Object.getPrototypeOf(proto);
  }
  
  return Object.getPrototypeOf(value) === proto;
}

// Support both ESM and CJS
export default isPlainObject;
