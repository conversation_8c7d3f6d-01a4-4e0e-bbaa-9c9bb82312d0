
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/ui/container';

const ProductNotFound: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <Container className="py-12">
      <div className="text-center py-16">
        <AlertCircle className="mx-auto h-16 w-16 text-gray-400 mb-4" />
        <h2 className="text-2xl font-semibold mb-2">Product Not Found</h2>
        <p className="text-muted-foreground mb-6">
          The product you are looking for does not exist or has been removed.
        </p>
        <Button onClick={() => navigate("/shop")}>
          Return to Shop
        </Button>
      </div>
    </Container>
  );
};

export default ProductNotFound;
