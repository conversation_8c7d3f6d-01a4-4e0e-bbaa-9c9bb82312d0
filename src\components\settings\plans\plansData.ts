
import { LayoutDashboard, Layers, Globe } from "lucide-react";
import type { PlanCardProps } from "./PlanCard";

export const planCardData: PlanCardProps[] = [
  {
    title: "Basic",
    description: "For hobbyists",
    price: 0,
    priceLabel: "free forever",
    period: "month",
    features: [
      "Unlimited WhatsApp orders",
      "No commissions",
      "Manual payments",
      "Upload up to 20 images"
    ],
    icon: LayoutDashboard,
    buttonText: "Get started",
    buttonVariant: "outline",
    yearlyPrice: 0
  },
  {
    title: "Premium",
    description: "For solo entrepreneurs",
    price: 20,
    period: "month",
    pricePrefix: "Everything in Basic, plus:",
    features: [
      "Unlimited images",
      "Custom domain and email",
      "Card payments (Stripe and more)",
      "Payment proof and processing fee",
      "Analytics, SEO and Meta Pixel",
      "Invoice settings and PDF",
      "CSV export/import",
      "Delivery distance calculation",
      "Customer reviews",
      "Live chat support"
    ],
    icon: Layers,
    buttonText: "Subscribe",
    buttonVariant: "default",
    showCancelText: true,
    yearlyPrice: 240 // $20 * 12
  },
  {
    title: "Business",
    description: "For teams",
    price: 50, // set to $50 for uniformity
    period: "month",
    pricePrefix: "Everything in Premium, plus:",
    features: [
      "5 stores and 5 staff accounts",
      "M-Duka.app logo removal",
      "WhatsApp Workflow and Catalog",
      "Shared Team WhatsApp Inbox",
      "Membership rewards",
      "Membership exclusive access",
      "Wholesale pricing",
      "Webhooks and API",
      "3rd-party apps integrations",
      "Priority support"
    ],
    icon: Globe,
    buttonText: "Subscribe",
    buttonVariant: "premium",
    showCancelText: true,
    yearlyPrice: 480, // $50 * 12 * 0.8 (20% off, rounded up from 479.99)
    yearlyDiscount: "Save 20%"
  }
];

export const comparisonFeatures = [
  { name: "Projects", starter: "5", pro: "Unlimited", enterprise: "Unlimited" },
  { name: "Team Members", starter: "3", pro: "10", enterprise: "Unlimited" },
  { name: "Storage", starter: "500MB", pro: "5GB", enterprise: "Unlimited" },
  { name: "API Calls", starter: "5,000/mo", pro: "50,000/mo", enterprise: "Unlimited" },
  { name: "Custom Domains", starter: false, pro: true, enterprise: true },
  { name: "Advanced Analytics", starter: false, pro: true, enterprise: true },
  { name: "Priority Support", starter: false, pro: true, enterprise: true },
  { name: "Dedicated Account Manager", starter: false, pro: false, enterprise: true }
];

