
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate, useParams } from 'react-router-dom';
import { useProduct } from '@/contexts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { toast } from 'sonner';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProductForm from '@/components/product/ProductForm';
import { Product } from '@/types/unified-product';

const EditProduct: React.FC = () => {
  const navigate = useNavigate();
  const { productId } = useParams<{ productId: string }>();
  const { getProduct, updateProduct, isLoading } = useProduct();
  const [product, setProduct] = useState<Product | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const loadProduct = async () => {
      if (productId) {
        try {
          const productData = await getProduct(productId);
          if (!productData) {
            toast.error('Product not found');
            navigate('/products');
          } else {
            setProduct(productData);
          }
        } catch (error) {
          console.error('Failed to load product:', error);
          toast.error('Failed to load product details');
          navigate('/products');
        }
      }
    };

    loadProduct();
  }, [productId, getProduct, navigate]);

  const handleSubmit = async (data: any) => {
    if (!productId) return;

    setIsSubmitting(true);
    try {
      await updateProduct(productId, data);
      toast.success('Product updated successfully');
      navigate('/products');
    } catch (error) {
      console.error('Failed to update product:', error);
      toast.error('Failed to update product. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading || !product) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <h1 className="text-3xl font-bold mb-6">Edit Product</h1>
          <Card>
            <CardHeader>
              <Skeleton className="h-8 w-1/3" />
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-20 w-full" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
                <Skeleton className="h-10 w-1/4 ml-auto" />
              </div>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <Helmet>
        <title>Edit Product - m-duka</title>
      </Helmet>
      <div className="p-6">
        <h1 className="text-3xl font-bold mb-6">Edit Product</h1>

        <Card>
          <CardHeader>
            <CardTitle>Edit {product.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <ProductForm 
              initialData={product}
              onSubmit={handleSubmit}
              isSubmitting={isSubmitting}
            />
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default EditProduct;
