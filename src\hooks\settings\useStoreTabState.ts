
import { useState, useEffect } from 'react';
import { Store } from '@/types/store';

export const useStoreTabState = (currentStore: Store | null) => {
  const [storeName, setStoreName] = useState(currentStore?.name || '');
  const [storeDescription, setStoreDescription] = useState(currentStore?.description || '');
  const [storeUrl, setStoreUrl] = useState(currentStore?.storeUrl || '');
  const [email, setEmail] = useState(currentStore?.notificationsEmail || '');
  const [address, setAddress] = useState('');
  const [country, setCountry] = useState('');
  const [whatsappNumber, setWhatsappNumber] = useState(currentStore?.whatsappSettings?.businessNumber || '');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [language, setLanguage] = useState('English');
  const [currency, setCurrency] = useState('USD');
  const [taxRate, setTaxRate] = useState('');
  const [taxMethod, setTaxMethod] = useState('inclusive');
  const [taxId, setTaxId] = useState('');
  const [whatsappLink, setWhatsappLink] = useState('');
  const [instagramLink, setInstagramLink] = useState('');
  const [facebookLink, setFacebookLink] = useState('');
  const [mapProvider, setMapProvider] = useState('openstreetmap');
  const [disableCheckout, setDisableCheckout] = useState(false);
  const [disableStore, setDisableStore] = useState(false);
  const [passwordProtect, setPasswordProtect] = useState(false);
  const [enableOrderNotifications, setEnableOrderNotifications] = useState(
    currentStore?.whatsappSettings?.enableOrderNotifications || false
  );
  const [enableCustomerUpdates, setEnableCustomerUpdates] = useState(
    currentStore?.whatsappSettings?.enableCustomerUpdates || false
  );
  const [customMessage, setCustomMessage] = useState(
    currentStore?.whatsappSettings?.customMessage || ''
  );
  const [autoReply, setAutoReply] = useState(
    currentStore?.whatsappSettings?.autoReply || false
  );

  // Update state when currentStore changes
  useEffect(() => {
    if (currentStore) {
      setStoreName(currentStore.name || '');
      setStoreDescription(currentStore.description || '');
      setStoreUrl(currentStore.storeUrl || '');
      setEmail(currentStore.notificationsEmail || '');
      setWhatsappNumber(currentStore.whatsappSettings?.businessNumber || '');
      setEnableOrderNotifications(currentStore.whatsappSettings?.enableOrderNotifications || false);
      setEnableCustomerUpdates(currentStore.whatsappSettings?.enableCustomerUpdates || false);
      setCustomMessage(currentStore.whatsappSettings?.customMessage || '');
      setAutoReply(currentStore.whatsappSettings?.autoReply || false);
    }
  }, [currentStore]);

  return {
    storeName,
    setStoreName,
    storeDescription,
    setStoreDescription,
    storeUrl,
    setStoreUrl,
    email,
    setEmail,
    address,
    setAddress,
    country,
    setCountry,
    whatsappNumber,
    setWhatsappNumber,
    phoneNumber,
    setPhoneNumber,
    language,
    setLanguage,
    currency,
    setCurrency,
    taxRate,
    setTaxRate,
    taxMethod,
    setTaxMethod,
    taxId,
    setTaxId,
    whatsappLink,
    setWhatsappLink,
    instagramLink,
    setInstagramLink,
    facebookLink,
    setFacebookLink,
    mapProvider,
    setMapProvider,
    disableCheckout,
    setDisableCheckout,
    disableStore,
    setDisableStore,
    passwordProtect,
    setPasswordProtect,
    enableOrderNotifications,
    setEnableOrderNotifications,
    enableCustomerUpdates,
    setEnableCustomerUpdates,
    customMessage,
    setCustomMessage,
    autoReply,
    setAutoReply,
  };
};
