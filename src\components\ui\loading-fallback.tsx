
import React from "react";
import { Loader2 } from "lucide-react";

interface LoadingFallbackProps {
  message?: string;
  className?: string;
  size?: "small" | "medium" | "large";
}

export const LoadingFallback: React.FC<LoadingFallbackProps> = ({
  message = "Loading...",
  className = "",
  size = "medium"
}) => {
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-6 w-6",
    large: "h-10 w-10"
  };

  return (
    <div className={`flex flex-col items-center justify-center p-4 ${className}`}>
      <Loader2 className={`animate-spin text-primary ${sizeClasses[size]}`} />
      <p className="mt-2 text-muted-foreground text-sm">{message}</p>
    </div>
  );
};

export default LoadingFallback;
