import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
const testimonials = [{
  content: "<PERSON><PERSON><PERSON><PERSON> has transformed my small business. The platform is incredibly intuitive, and I was able to set up my store in less than a day. The built-in payment integration with M-Pesa made it easy for my local customers to shop.",
  author: "<PERSON>",
  role: "Fashion Boutique Owner",
  image: "https://randomuser.me/api/portraits/women/1.jpg"
}, {
  content: "The AI-powered features have saved me countless hours. The product description generator creates engaging copy that actually converts, and the customer support chatbot handles basic inquiries so I can focus on growing my business.",
  author: "<PERSON>",
  role: "Electronics Store Owner",
  image: "https://randomuser.me/api/portraits/men/32.jpg"
}, {
  content: "What impressed me most was how quickly I could start accepting payments. The integration with multiple payment methods means I can sell to customers both locally and internationally without any hassle.",
  author: "<PERSON><PERSON>",
  role: "Handcraft Shop Owner",
  image: "https://randomuser.me/api/portraits/women/44.jpg"
}];
const Testimonials = () => {
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  return <section id="testimonials" ref={sectionRef} className="py-2 sm:py-24 lg:py-[100px]">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-16">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5
        }} className="inline-flex items-center rounded-full bg-accent px-3 py-1 text-sm font-medium text-mduka-700 mb-4">
            <span className="flex h-2 w-2 rounded-full bg-mduka-500 mr-1.5"></span>
            <span>Success Stories</span>
          </motion.div>
          
          <motion.h2 initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.1
        }} className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-balance">
            <span className="text-gradient">Loved</span> by store owners
          </motion.h2>
          
          <motion.p initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.2
        }} className="text-muted-foreground text-lg max-w-2xl mx-auto text-pretty">
            Hear from the businesses that have transformed their online presence with M-Duka.
          </motion.p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => {
          const [testimonialRef, testimonialInView] = useInView({
            triggerOnce: true,
            threshold: 0.1
          });
          return <motion.div key={index} ref={testimonialRef} initial={{
            opacity: 0,
            y: 30
          }} animate={testimonialInView ? {
            opacity: 1,
            y: 0
          } : {
            opacity: 0,
            y: 30
          }} transition={{
            duration: 0.5,
            delay: index * 0.1
          }} className="bg-background border border-border rounded-xl p-6 hover:shadow-subtle transition-all duration-300">
                <div className="flex flex-col h-full">
                  <div className="mb-6">
                    <div className="flex space-x-1 mb-4">
                      {[...Array(5)].map((_, starIndex) => <svg key={starIndex} className="w-5 h-5 text-yellow-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                          <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
                        </svg>)}
                    </div>
                    
                    <blockquote className="text-pretty">
                      "{testimonial.content}"
                    </blockquote>
                  </div>
                  
                  <div className="mt-auto flex items-center gap-4">
                    <div className="h-10 w-10 rounded-full overflow-hidden">
                      <img src={testimonial.image} alt={testimonial.author} className="h-full w-full object-cover" loading="lazy" />
                    </div>
                    <div>
                      <p className="font-medium">{testimonial.author}</p>
                      <p className="text-sm text-muted-foreground">{testimonial.role}</p>
                    </div>
                  </div>
                </div>
              </motion.div>;
        })}
        </div>
        
        <motion.div initial={{
        opacity: 0,
        y: 20
      }} animate={sectionInView ? {
        opacity: 1,
        y: 0
      } : {
        opacity: 0,
        y: 20
      }} transition={{
        duration: 0.5,
        delay: 0.5
      }} className="mt-16 text-center">
          <div className="bg-gradient-to-r from-mduka-50 to-blue-50 rounded-2xl p-8 md:p-10 lg:p-12 relative overflow-hidden">
            <div className="absolute inset-0 bg-white/50 backdrop-blur-xs z-0"></div>
            
            <div className="relative z-10 max-w-3xl mx-auto">
              <h3 className="text-2xl md:text-3xl font-bold tracking-tight mb-6 text-balance">
                Join over 1,000+ store owners already growing their business with M-Duka
              </h3>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button className="bg-mduka-600 hover:bg-mduka-700 text-white rounded-full h-12 px-8 text-base font-medium" asChild>
                  <Link to="/signup">Get started for free</Link>
                </Button>
                
                <Button variant="outline" className="rounded-full h-12 px-8 text-base font-medium border-muted" asChild>
                  <a href="#pricing">View pricing</a>
                </Button>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>;
};
export default Testimonials;