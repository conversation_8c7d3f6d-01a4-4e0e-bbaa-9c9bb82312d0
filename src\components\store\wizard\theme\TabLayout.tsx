
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { cn } from '@/lib/utils';

interface TabLayoutProps {
  themeOptions: {
    layoutType?: string;
    productCardsPerRow?: number;
    showProductRatings?: boolean;
    showQuickAdd?: boolean;
    productImageStyle?: string;
  };
  handleUpdateThemeOptions: (updates: Partial<TabLayoutProps['themeOptions']>) => void;
  layoutOptions: {
    id: string;
    name: string;
    description: string;
  }[];
}

const TabLayout: React.FC<TabLayoutProps> = ({
  themeOptions,
  handleUpdateThemeOptions,
  layoutOptions,
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Layout Type</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
          {layoutOptions.map((layout) => (
            <Card 
              key={layout.id}
              className={cn(
                "cursor-pointer hover:border-green-500 transition-all",
                themeOptions.layoutType === layout.id && "border-2 border-green-500"
              )}
              onClick={() => handleUpdateThemeOptions({ layoutType: layout.id })}
            >
              <CardHeader className="py-3">
                <CardTitle className="text-base">{layout.name}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription>{layout.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Products Per Row</h3>
        <div className="space-y-2">
          <div className="flex items-center gap-4">
            <Slider 
              min={2}
              max={6}
              step={1}
              value={[themeOptions.productCardsPerRow || 4]}
              onValueChange={(values) => handleUpdateThemeOptions({ productCardsPerRow: values[0] })}
              className="flex-1"
            />
            <span className="text-sm font-medium w-8 text-center">{themeOptions.productCardsPerRow || 4}</span>
          </div>
          <div className="flex gap-1 h-8 mt-2">
            {Array.from({ length: 6 }).map((_, i) => (
              <div 
                key={i} 
                className={cn(
                  "flex-1 bg-green-100 border border-green-200", 
                  i < (themeOptions.productCardsPerRow || 4) ? "opacity-100" : "opacity-30"
                )}
              />
            ))}
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Product Cards</h3>
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="show-ratings">Show Product Ratings</Label>
            <p className="text-sm text-muted-foreground">
              Display star ratings on product cards
            </p>
          </div>
          <Switch 
            id="show-ratings" 
            checked={themeOptions.showProductRatings !== false}
            onCheckedChange={(checked) => handleUpdateThemeOptions({ showProductRatings: checked })}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="quick-add">Show Quick Add Button</Label>
            <p className="text-sm text-muted-foreground">
              Allow adding products to cart directly from the listing
            </p>
          </div>
          <Switch 
            id="quick-add" 
            checked={themeOptions.showQuickAdd !== false}
            onCheckedChange={(checked) => handleUpdateThemeOptions({ showQuickAdd: checked })}
          />
        </div>
        
        <div className="space-y-2">
          <Label>Product Image Style</Label>
          <RadioGroup 
            value={themeOptions.productImageStyle || 'contain'} 
            onValueChange={(value) => handleUpdateThemeOptions({ productImageStyle: value })}
            className="flex flex-wrap gap-4"
          >
            <div className="flex items-center gap-2">
              <RadioGroupItem value="contain" id="contain" />
              <Label htmlFor="contain" className="cursor-pointer">Contain (Show all)</Label>
            </div>
            <div className="flex items-center gap-2">
              <RadioGroupItem value="cover" id="cover" />
              <Label htmlFor="cover" className="cursor-pointer">Cover (Fill space)</Label>
            </div>
          </RadioGroup>
        </div>
      </div>
    </div>
  );
};

export default TabLayout;
