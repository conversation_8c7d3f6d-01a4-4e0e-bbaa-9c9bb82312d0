# Supabase CLI

[![Coverage Status](https://coveralls.io/repos/github/supabase/cli/badge.svg?branch=main)](https://coveralls.io/github/supabase/cli?branch=main) [![Bitbucket Pipelines](https://img.shields.io/bitbucket/pipelines/supabase-cli/setup-cli/master?style=flat-square&label=Bitbucket%20Canary)](https://bitbucket.org/supabase-cli/setup-cli/pipelines) [![Gitlab Pipeline Status](https://img.shields.io/gitlab/pipeline-status/sweatybridge%2Fsetup-cli?label=Gitlab%20Canary)
](https://gitlab.com/sweatybridge/setup-cli/-/pipelines)

[Supabase](https://supabase.io) is an open source Firebase alternative. We're building the features of Firebase using enterprise-grade open source tools.

This repository contains all the functionality for Supabase CLI.

- [x] Running Supabase locally
- [x] Managing database migrations
- [x] Creating and deploying Supabase Functions
- [x] Generating types directly from your database schema
- [x] Making authenticated HTTP requests to [Management API](https://supabase.com/docs/reference/api/introduction)

## Getting started

### Install the CLI

Available via [NPM](https://www.npmjs.com) as dev dependency. To install:

```bash
npm i supabase --save-dev
```

To install the beta release channel:

```bash
npm i supabase@beta --save-dev
```

When installing with yarn 4, you need to disable experimental fetch with the following nodejs config.

```
NODE_OPTIONS=--no-experimental-fetch yarn add supabase
```

> **Note**
For Bun versions below v1.0.17, you must add `supabase` as a [trusted dependency](https://bun.sh/guides/install/trusted) before running `bun add -D supabase`.

<details>
  <summary><b>macOS</b></summary>

  Available via [Homebrew](https://brew.sh). To install:

  ```sh
  brew install supabase/tap/supabase
  ```

  To install the beta release channel:
  
  ```sh
  brew install supabase/tap/supabase-beta
  brew link --overwrite supabase-beta
  ```
  
  To upgrade:

  ```sh
  brew upgrade supabase
  ```
</details>

<details>
  <summary><b>Windows</b></summary>

  Available via [Scoop](https://scoop.sh). To install:

  ```powershell
  scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
  scoop install supabase
  ```

  To upgrade:

  ```powershell
  scoop update supabase
  ```
</details>

<details>
  <summary><b>Linux</b></summary>

  Available via [Homebrew](https://brew.sh) and Linux packages.

  #### via Homebrew

  To install:

  ```sh
  brew install supabase/tap/supabase
  ```

  To upgrade:

  ```sh
  brew upgrade supabase
  ```

  #### via Linux packages

  Linux packages are provided in [Releases](https://github.com/supabase/cli/releases). To install, download the `.apk`/`.deb`/`.rpm`/`.pkg.tar.zst` file depending on your package manager and run the respective commands.

  ```sh
  sudo apk add --allow-untrusted <...>.apk
  ```

  ```sh
  sudo dpkg -i <...>.deb
  ```

  ```sh
  sudo rpm -i <...>.rpm
  ```

  ```sh
  sudo pacman -U <...>.pkg.tar.zst
  ```
</details>

<details>
  <summary><b>Other Platforms</b></summary>

  You can also install the CLI via [go modules](https://go.dev/ref/mod#go-install) without the help of package managers.

  ```sh
  go install github.com/supabase/cli@latest
  ```

  Add a symlink to the binary in `$PATH` for easier access:

  ```sh
  ln -s "$(go env GOPATH)/bin/cli" /usr/bin/supabase
  ```

  This works on other non-standard Linux distros.
</details>

<details>
  <summary><b>Community Maintained Packages</b></summary>

  Available via [pkgx](https://pkgx.sh/). Package script [here](https://github.com/pkgxdev/pantry/blob/main/projects/supabase.com/cli/package.yml).
  To install in your working directory:

  ```bash
  pkgx install supabase
  ```

  Available via [Nixpkgs](https://nixos.org/). Package script [here](https://github.com/NixOS/nixpkgs/blob/master/pkgs/development/tools/supabase-cli/default.nix).
</details>

### Run the CLI

```bash
supabase bootstrap
```

Or using npx:

```bash
npx supabase bootstrap
```

The bootstrap command will guide you through the process of setting up a Supabase project using one of the [starter](https://github.com/supabase-community/supabase-samples/blob/main/samples.json) templates.

## Docs

Command & config reference can be found [here](https://supabase.com/docs/reference/cli/about).

## Breaking changes

We follow semantic versioning for changes that directly impact CLI commands, flags, and configurations.

However, due to dependencies on other service images, we cannot guarantee that schema migrations, seed.sql, and generated types will always work for the same CLI major version. If you need such guarantees, we encourage you to pin a specific version of CLI in package.json.

## Developing

To run from source:

```sh
# Go >= 1.22
go run . help
```

# M-duka - WhatsApp Digital Store Platform

Transform your WhatsApp Business into a powerful digital storefront. Create your online store in minutes, manage orders, and grow your business effortlessly.

## 🚀 Features

- **WhatsApp Integration**: Seamlessly connect with your WhatsApp Business
- **Digital Storefront**: Beautiful, customizable online store
- **Order Management**: Track and manage customer orders
- **Product Catalog**: Easy product management with images and descriptions
- **Customer Management**: Keep track of your customers and their preferences
- **Analytics Dashboard**: Monitor your business performance
- **Mobile-First Design**: Optimized for mobile devices and tablets
- **Progressive Web App (PWA)**: Install as a native app on any device

## 📱 PWA & Mobile Features

### Progressive Web App (PWA)
M-duka is built as a Progressive Web App, providing a native app-like experience:

- **Installable**: Users can install the app on their home screen
- **Offline Capable**: Core functionality works without internet connection
- **Fast Loading**: Optimized caching for instant loading
- **Push Notifications**: Stay connected with your customers (coming soon)
- **Auto-Updates**: Seamless updates without app store downloads

### Mobile Responsiveness
Fully responsive design optimized for all devices:

- **Mobile-First Design**: Designed primarily for mobile devices
- **Touch-Friendly**: Large touch targets and intuitive gestures
- **Safe Area Support**: Proper handling of device notches and safe areas
- **Dynamic Viewport**: Adapts to different screen sizes and orientations
- **Optimized Navigation**: Bottom navigation on mobile, sidebar on desktop

### Device-Specific Optimizations

#### iOS
- **Safari PWA Support**: Full iOS Safari PWA compatibility
- **Home Screen Icons**: Custom app icons for iOS home screen
- **Status Bar Integration**: Seamless status bar styling
- **Touch Callout Disabled**: Prevents unwanted iOS touch callouts in PWA mode

#### Android
- **Adaptive Icons**: Maskable icons that adapt to device themes
- **Chrome PWA Features**: Full Android Chrome PWA support
- **Material Design**: Android-friendly design patterns
- **Keyboard Handling**: Proper virtual keyboard support

### Performance Features
- **Image Optimization**: Crisp rendering on high-DPI displays
- **Font Loading**: Optimized web font loading with fallbacks
- **Lazy Loading**: Images and components load as needed
- **Code Splitting**: Optimized bundle sizes for faster loading
- **Service Worker**: Advanced caching strategies for offline support

## 🛠 Technical Stack

- **Frontend**: React 18 + TypeScript
- **Styling**: Tailwind CSS with custom responsive utilities
- **UI Components**: Radix UI + shadcn/ui
- **State Management**: React Context + Custom Hooks
- **Routing**: React Router v6
- **PWA**: Vite PWA Plugin with Workbox
- **Backend**: Supabase (PostgreSQL + Auth + Storage)
- **Deployment**: Vercel with edge functions

## 🎨 Responsive Design System

### Breakpoints
- **Mobile**: < 640px (sm)
- **Tablet**: 640px - 768px (md)
- **Desktop**: 768px+ (lg, xl, 2xl)

### Responsive Utilities
```css
/* Typography */
.text-responsive-xs    /* xs on mobile, sm on larger screens */
.text-responsive-sm    /* sm on mobile, base on larger screens */
.text-responsive-lg    /* lg on mobile, xl-2xl on larger screens */

/* Layout */
.grid-responsive       /* 1 col mobile, 2-4 cols desktop */
.flex-responsive       /* column mobile, row desktop */
.mobile-container      /* Responsive padding and max-width */

/* Spacing */
.mobile-section        /* Responsive section padding */
.mobile-gap           /* Responsive gap spacing */
.touch-spacing        /* Touch-optimized padding */

/* Visibility */
.mobile-only          /* Show only on mobile */
.desktop-only         /* Show only on desktop */
.tablet-up           /* Show on tablet and up */
```

### Touch Interactions
- **Touch Targets**: Minimum 44px touch targets for accessibility
- **Touch Feedback**: Visual feedback on touch interactions
- **Gesture Support**: Swipe and touch gestures where appropriate
- **Focus Management**: Keyboard navigation support

## 🔧 Installation & Setup

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Supabase account

### Quick Start
```bash
# Clone the repository
git clone https://github.com/your-username/m-duka-app.git
cd m-duka-app

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env.local
# Edit .env.local with your Supabase credentials

# Start development server
npm run dev
```

### PWA Development
The PWA features are enabled by default in development mode:

```bash
# Build for production with PWA
npm run build

# Preview production build with PWA
npm run preview
```

### Testing PWA Features
1. **Install Prompt**: Visit the app in Chrome/Edge and look for install prompt
2. **Offline Mode**: Use Chrome DevTools > Network > Offline to test offline functionality
3. **Mobile Testing**: Use Chrome DevTools device emulation or test on real devices
4. **Lighthouse**: Run Lighthouse audit to check PWA score

## 📱 Mobile Testing Guide

### Browser Testing
- **Chrome Mobile**: Full PWA support with install prompts
- **Safari iOS**: PWA support with "Add to Home Screen"
- **Firefox Mobile**: Basic PWA support
- **Samsung Internet**: Full PWA support with adaptive icons

### Device Testing
1. **iOS Safari**: Test "Add to Home Screen" functionality
2. **Android Chrome**: Test install banner and adaptive icons
3. **Tablet Landscape**: Verify responsive layout transitions
4. **Various Screen Sizes**: Test on different device sizes

### Performance Testing
- **Lighthouse Mobile**: Aim for 90+ PWA score
- **WebPageTest**: Test on real mobile networks
- **Chrome DevTools**: Use device emulation for quick testing

## 🚀 Deployment

### Vercel (Recommended)
```bash
# Deploy to Vercel
npm run build
vercel --prod
```

### Manual Deployment
```bash
# Build for production
npm run build

# Deploy dist/ folder to your hosting provider
```

### PWA Deployment Checklist
- [ ] HTTPS enabled (required for PWA)
- [ ] Service worker registered
- [ ] Web app manifest configured
- [ ] Icons in multiple sizes available
- [ ] Offline fallback pages working
- [ ] Install prompts functioning

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

### Development Guidelines
- Follow mobile-first responsive design principles
- Test on multiple devices and browsers
- Ensure PWA features work correctly
- Maintain accessibility standards (WCAG 2.1)
- Use semantic HTML and proper ARIA labels

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.m-duka.app](https://docs.m-duka.app)
- **Issues**: [GitHub Issues](https://github.com/your-username/m-duka-app/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/m-duka-app/discussions)
- **Email**: <EMAIL>

---

Built with ❤️ for African entrepreneurs and small businesses.
