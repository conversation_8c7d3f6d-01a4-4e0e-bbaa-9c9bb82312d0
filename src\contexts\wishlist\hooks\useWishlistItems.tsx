
import { useState, useEffect } from 'react';
import { WishlistItem } from '../types';
import { useAuth } from '@/contexts/auth/AuthContext';
import { loadWishlistFromLocalStorage, saveWishlistToLocalStorage } from '../localStorage';
import { fetchWishlistItems } from '../wishlistApi';
import { useToast } from '@/hooks/use-toast';

export const useWishlistItems = () => {
  const [wishlistItems, setWishlistItems] = useState<WishlistItem[]>([]);
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();

  useEffect(() => {
    const loadWishlist = async () => {
      try {
        if (isAuthenticated && user) {
          try {
            const items = await fetchWishlistItems(user.id);
            setWishlistItems(items);
          } catch (error) {
            console.error('Failed to load wishlist from Supabase:', error);
            const localItems = loadWishlistFromLocalStorage();
            setWishlistItems(localItems);
            toast({
              title: "Error loading wishlist",
              description: "Using local storage as a fallback.",
              variant: "destructive",
            });
          }
        } else {
          const localItems = loadWishlistFromLocalStorage();
          setWishlistItems(localItems);
        }
      } catch (error) {
        console.error('Error loading wishlist:', error);
        toast({
          title: "Error loading wishlist",
          description: "Could not load your wishlist items.",
          variant: "destructive",
        });
      }
    };
    
    loadWishlist();
  }, [isAuthenticated, user, toast]);

  useEffect(() => {
    if (!isAuthenticated) {
      saveWishlistToLocalStorage(wishlistItems);
    }
  }, [wishlistItems, isAuthenticated]);

  return {
    wishlistItems,
    setWishlistItems
  };
};
