
import { Store, StoreFormData } from '@/types/store';
import { MOCK_STORES } from '../mockData';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

// Create a mock store for development
export const createMockStore = (storeData: StoreFormData, userId: string): Store => {
  console.log('Creating mock store for development');
  
  // Ensure themeOptions has required properties
  const themeOptions = {
    primaryColor: storeData.themeOptions?.primaryColor ?? "#ffffff",
    secondaryColor: storeData.themeOptions?.colorScheme ?? "#000000",
    darkMode: storeData.themeOptions?.darkMode ?? false,
    customHeader: storeData.themeOptions?.customHeader ?? false,
    customFooter: storeData.themeOptions?.customFooter ?? false,
    colorScheme: storeData.themeOptions?.colorScheme,
    fontFamily: storeData.themeOptions?.fontFamily,
    cornerRadius: storeData.themeOptions?.cornerRadius,
    buttonStyle: storeData.themeOptions?.buttonStyle,
    layoutType: storeData.themeOptions?.layoutType,
    productCardsPerRow: storeData.themeOptions?.productCardsPerRow,
    showProductRatings: storeData.themeOptions?.showProductRatings,
    showQuickAdd: storeData.themeOptions?.showQuickAdd,
    productImageStyle: storeData.themeOptions?.productImageStyle,
    enableAnimations: storeData.themeOptions?.enableAnimations,
    heroStyle: storeData.themeOptions?.heroStyle
  };
  
  // Ensure notifications has required properties
  const notifications = {
    email: true, // Make this required field explicitly true
    sms: false,  // Make this required field explicitly false
    orderNotifications: storeData.notifications?.orderNotifications,
    stockNotifications: storeData.notifications?.stockNotifications,
    marketingNotifications: storeData.notifications?.marketingNotifications
  };

  // Ensure whatsapp_settings has required properties
  const whatsappSettings = {
    enabled: true,
    number: storeData.whatsappSettings?.number || '',
    message: storeData.whatsappSettings?.customMessage || 'Thank you for your order!',
    businessNumber: storeData.whatsappSettings?.businessNumber || '',
    enableOrderNotifications: storeData.whatsappSettings?.enableOrderNotifications || false,
    enableCustomerUpdates: storeData.whatsappSettings?.enableCustomerUpdates || false,
    customMessage: storeData.whatsappSettings?.customMessage || '',
    autoReply: storeData.whatsappSettings?.autoReply || false
  };

  const mockStore: Store = {
    id: uuidv4(), // Use UUID v4 for a proper UUID
    name: storeData.name,
    description: storeData.description,
    store_url: storeData.storeUrl,
    owner_id: userId,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    category: storeData.category,
    theme_id: storeData.themeId,
    store_type: storeData.storeType,
    payment_methods: storeData.paymentMethods,
    notificationsEmail: storeData.notificationsEmail,
    notifications: notifications,
    theme_options: themeOptions,
    logo_url: storeData.logo || null,
    whatsapp_settings: whatsappSettings,
    is_active: true
  };
  
  // Add to mock stores
  MOCK_STORES.push(mockStore);
  
  toast.success('Store created successfully');
  return mockStore;
};

// Update a mock store for development
export const updateMockStore = (storeId: string, storeData: Partial<StoreFormData>): Store => {
  console.log('Updating mock store for development');
  const storeIndex = MOCK_STORES.findIndex(store => store.id === storeId);
  
  if (storeIndex === -1) {
    throw new Error('Store not found');
  }
  
  // Ensure themeOptions has required properties if it's being updated
  let updatedThemeOptions = MOCK_STORES[storeIndex].theme_options;
  if (storeData.themeOptions) {
    updatedThemeOptions = {
      primaryColor: storeData.themeOptions.primaryColor ?? MOCK_STORES[storeIndex].theme_options?.primaryColor ?? "#ffffff",
      secondaryColor: storeData.themeOptions.colorScheme ?? MOCK_STORES[storeIndex].theme_options?.secondaryColor ?? "#000000",
      darkMode: storeData.themeOptions.darkMode ?? MOCK_STORES[storeIndex].theme_options?.darkMode ?? false,
      customHeader: storeData.themeOptions.customHeader ?? MOCK_STORES[storeIndex].theme_options?.customHeader ?? false,
      customFooter: storeData.themeOptions.customFooter ?? MOCK_STORES[storeIndex].theme_options?.customFooter ?? false,
      colorScheme: storeData.themeOptions.colorScheme ?? MOCK_STORES[storeIndex].theme_options?.colorScheme,
      fontFamily: storeData.themeOptions.fontFamily ?? MOCK_STORES[storeIndex].theme_options?.fontFamily,
      cornerRadius: storeData.themeOptions.cornerRadius ?? MOCK_STORES[storeIndex].theme_options?.cornerRadius,
      buttonStyle: storeData.themeOptions.buttonStyle ?? MOCK_STORES[storeIndex].theme_options?.buttonStyle,
      layoutType: storeData.themeOptions.layoutType ?? MOCK_STORES[storeIndex].theme_options?.layoutType,
      productCardsPerRow: storeData.themeOptions.productCardsPerRow ?? MOCK_STORES[storeIndex].theme_options?.productCardsPerRow,
      showProductRatings: storeData.themeOptions.showProductRatings ?? MOCK_STORES[storeIndex].theme_options?.showProductRatings,
      showQuickAdd: storeData.themeOptions.showQuickAdd ?? MOCK_STORES[storeIndex].theme_options?.showQuickAdd,
      productImageStyle: storeData.themeOptions.productImageStyle ?? MOCK_STORES[storeIndex].theme_options?.productImageStyle,
      enableAnimations: storeData.themeOptions.enableAnimations ?? MOCK_STORES[storeIndex].theme_options?.enableAnimations,
      heroStyle: storeData.themeOptions.heroStyle ?? MOCK_STORES[storeIndex].theme_options?.heroStyle
    };
  }
  
  // Ensure notifications has required properties if it's being updated
  let updatedNotifications = MOCK_STORES[storeIndex].notifications;
  if (storeData.notifications) {
    updatedNotifications = {
      email: true, // Keep this required field explicitly set
      sms: false, // Keep this required field explicitly set
      orderNotifications: storeData.notifications.orderNotifications ?? updatedNotifications.orderNotifications,
      stockNotifications: storeData.notifications.stockNotifications ?? updatedNotifications.stockNotifications,
      marketingNotifications: storeData.notifications.marketingNotifications ?? updatedNotifications.marketingNotifications
    };
  }

  // Ensure whatsapp_settings has required properties if it's being updated
  let updatedWhatsappSettings = MOCK_STORES[storeIndex].whatsapp_settings;
  if (storeData.whatsappSettings) {
    updatedWhatsappSettings = {
      enabled: true,
      number: storeData.whatsappSettings.number ?? updatedWhatsappSettings?.number ?? "",
      message: storeData.whatsappSettings.message ?? updatedWhatsappSettings?.message ?? "Thank you for your order!",
      businessNumber: storeData.whatsappSettings.businessNumber ?? updatedWhatsappSettings?.businessNumber,
      enableOrderNotifications: storeData.whatsappSettings.enableOrderNotifications ?? updatedWhatsappSettings?.enableOrderNotifications,
      enableCustomerUpdates: storeData.whatsappSettings.enableCustomerUpdates ?? updatedWhatsappSettings?.enableCustomerUpdates,
      customMessage: storeData.whatsappSettings.customMessage ?? updatedWhatsappSettings?.customMessage,
      autoReply: storeData.whatsappSettings.autoReply ?? updatedWhatsappSettings?.autoReply
    };
  }
  
  const updatedStore: Store = {
    ...MOCK_STORES[storeIndex],
    ...(storeData.name !== undefined && { name: storeData.name }),
    ...(storeData.description !== undefined && { description: storeData.description }),
    ...(storeData.storeUrl !== undefined && { store_url: storeData.storeUrl }),
    ...(storeData.category !== undefined && { category: storeData.category }),
    ...(storeData.themeId !== undefined && { theme_id: storeData.themeId }),
    ...(storeData.storeType !== undefined && { store_type: storeData.storeType }),
    ...(storeData.paymentMethods !== undefined && { payment_methods: storeData.paymentMethods }),
    ...(storeData.notificationsEmail !== undefined && { notificationsEmail: storeData.notificationsEmail }),
    notifications: updatedNotifications,
    theme_options: updatedThemeOptions,
    whatsapp_settings: updatedWhatsappSettings,
    updated_at: new Date().toISOString()
  };
  
  MOCK_STORES[storeIndex] = updatedStore;
  
  toast.success('Store updated successfully');
  return updatedStore;
};

// Delete a mock store for development
export const deleteMockStore = (storeId: string): void => {
  console.log('Deleting mock store for development');
  const storeIndex = MOCK_STORES.findIndex(store => store.id === storeId);
  
  if (storeIndex === -1) {
    throw new Error('Store not found');
  }
  
  MOCK_STORES.splice(storeIndex, 1);
  
  toast.success('Store deleted successfully');
};
