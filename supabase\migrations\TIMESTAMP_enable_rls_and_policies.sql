-- Migration: Enable RLS and Define Role-Based Policies

BEGIN;

-- 1. Helper Function to Get User Role
-- Assumes a 'profiles' table exists with user_id (FK to auth.users) and role columns.
-- Adjust if roles are stored differently (e.g., in auth.users.raw_user_meta_data).
CREATE OR REPLACE FUNCTION public.get_my_role()
RETURNS TEXT
LANGUAGE sql
STABLE
SECURITY DEFINER -- Important for accessing profiles table
SET search_path = public
AS $$
  SELECT role
  FROM public.profiles
  WHERE user_id = auth.uid();
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_my_role() TO authenticated;

-- 2. Enable RLS on Tables
ALTER TABLE public.stores ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.wishlists ENABLE ROW LEVEL SECURITY;
-- Add other tables like categories, discounts etc. as needed
-- Example: ALTER TABLE public.categories ENABLE ROW LEVEL SECURITY;

-- 3. Define RLS Policies

-- Policies for 'stores' table
ALTER TABLE public.stores FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow store_owner manage own store" ON public.stores;
CREATE POLICY "Allow store_owner manage own store"
    ON public.stores
    FOR ALL
    USING (
      get_my_role() = 'store_owner' AND owner_id = auth.uid()
    )
    WITH CHECK (
      get_my_role() = 'store_owner' AND owner_id = auth.uid()
    );

DROP POLICY IF EXISTS "Allow admin manage all stores" ON public.stores;
CREATE POLICY "Allow admin manage all stores"
    ON public.stores
    FOR ALL
    USING (
      get_my_role() = 'admin'
    )
    WITH CHECK (
      get_my_role() = 'admin'
    );

DROP POLICY IF EXISTS "Allow authenticated users SELECT stores" ON public.stores;
CREATE POLICY "Allow authenticated users SELECT stores"
    ON public.stores
    FOR SELECT
    USING (
      auth.role() = 'authenticated'
    );

-- Policies for 'products' table
ALTER TABLE public.products FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow store_owner manage own products" ON public.products;
CREATE POLICY "Allow store_owner manage own products"
    ON public.products
    FOR ALL
    USING (
      get_my_role() = 'store_owner'
      AND store_id IN (SELECT id FROM public.stores WHERE owner_id = auth.uid())
    )
    WITH CHECK (
      get_my_role() = 'store_owner'
      AND store_id IN (SELECT id FROM public.stores WHERE owner_id = auth.uid())
    );

DROP POLICY IF EXISTS "Allow admin manage all products" ON public.products;
CREATE POLICY "Allow admin manage all products"
    ON public.products
    FOR ALL
    USING (
      get_my_role() = 'admin'
    )
    WITH CHECK (
      get_my_role() = 'admin'
    );

DROP POLICY IF EXISTS "Allow authenticated users SELECT active products" ON public.products;
CREATE POLICY "Allow authenticated users SELECT active products"
    ON public.products
    FOR SELECT
    USING (
      auth.role() = 'authenticated' AND is_active = TRUE
    );

-- Policies for 'orders' table
ALTER TABLE public.orders FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow customer SELECT/INSERT own orders" ON public.orders;
CREATE POLICY "Allow customer SELECT/INSERT own orders"
    ON public.orders
    FOR ALL -- Covers SELECT and INSERT for customers
    USING (
      get_my_role() = 'customer' AND customer_id = auth.uid() -- Checks the user making the request is the customer
    )
    WITH CHECK (
      get_my_role() = 'customer' AND customer_id = auth.uid() -- Ensures inserts are for the correct customer
    );

DROP POLICY IF EXISTS "Allow store_owner manage own orders" ON public.orders;
CREATE POLICY "Allow store_owner manage own orders"
    ON public.orders
    FOR ALL -- Covers SELECT and UPDATE for store owners (DELETE might be too risky)
    USING (
      get_my_role() = 'store_owner'
      AND store_id IN (SELECT id FROM public.stores WHERE owner_id = auth.uid())
    )
    WITH CHECK (
      get_my_role() = 'store_owner'
      AND store_id IN (SELECT id FROM public.stores WHERE owner_id = auth.uid())
    );

DROP POLICY IF EXISTS "Allow admin manage all orders" ON public.orders;
CREATE POLICY "Allow admin manage all orders"
    ON public.orders
    FOR ALL
    USING (
      get_my_role() = 'admin'
    )
    WITH CHECK (
      get_my_role() = 'admin'
    );

-- Policies for 'order_items' table
-- Often linked implicitly via order policies, but explicit select can be useful.
ALTER TABLE public.order_items FORCE ROW LEVEL SECURITY;

-- Allow users to SELECT items belonging to orders they can access
DROP POLICY IF EXISTS "Allow SELECT based on order access" ON public.order_items;
CREATE POLICY "Allow SELECT based on order access"
    ON public.order_items
    FOR SELECT
    USING (
        order_id IN (SELECT id FROM public.orders) -- Relies on the SELECT policy on 'orders'
    );

-- Allow INSERT/UPDATE/DELETE only if the user can modify the parent order
-- (More complex if direct modification needed; often handled via order logic or functions)
-- Example: Restrict direct inserts
DROP POLICY IF EXISTS "Restrict direct modification" ON public.order_items;
CREATE POLICY "Restrict direct modification"
    ON public.order_items
    FOR INSERT, UPDATE, DELETE
    USING (false); -- Disallow direct modification by default

-- Policies for 'customers' table
ALTER TABLE public.customers FORCE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Allow customer manage own record" ON public.customers;
CREATE POLICY "Allow customer manage own record"
    ON public.customers
    FOR ALL
    USING (
      get_my_role() = 'customer' AND user_id = auth.uid()
    )
    WITH CHECK (
      get_my_role() = 'customer' AND user_id = auth.uid()
    );

DROP POLICY IF EXISTS "Allow store_owner SELECT own customers" ON public.customers;
CREATE POLICY "Allow store_owner SELECT own customers"
    ON public.customers
    FOR SELECT
    USING (
      get_my_role() = 'store_owner'
      -- Select customers who have placed an order in one of the owner's stores
      AND id IN (
          SELECT c.id
          FROM public.customers c
          JOIN public.orders o ON c.user_id = o.customer_id -- Join based on auth user ID
          JOIN public.stores s ON o.store_id = s.id
          WHERE s.owner_id = auth.uid()
      )
      -- OR select customers directly linked to the store if that's the model
      -- AND store_id IN (SELECT id FROM public.stores WHERE owner_id = auth.uid())
    );

DROP POLICY IF EXISTS "Allow admin manage all customers" ON public.customers;
CREATE POLICY "Allow admin manage all customers"
    ON public.customers
    FOR ALL
    USING (
      get_my_role() = 'admin'
    )
    WITH CHECK (
      get_my_role() = 'admin'
    );

-- Policies for 'wishlists' table
ALTER TABLE public.wishlists FORCE ROW LEVEL SECURITY;

-- Allow customers to manage their own wishlist items
DROP POLICY IF EXISTS "Allow customer manage own wishlist" ON public.wishlists;
CREATE POLICY "Allow customer manage own wishlist"
    ON public.wishlists
    FOR ALL
    USING (
        get_my_role() = 'customer'
        AND customer_id IN (SELECT id FROM public.customers WHERE user_id = auth.uid()) -- Check via the customers table link
    )
    WITH CHECK (
        get_my_role() = 'customer'
        AND customer_id IN (SELECT id FROM public.customers WHERE user_id = auth.uid())
    );

-- Add policies for other tables like categories, discounts etc. following similar patterns.

COMMIT;
