
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";
import { AlertTriangle } from "lucide-react";

/**
 * A better experience for unauthorized/denied pages.
 */
const UnauthorizedPage: React.FC = () => {
  const navigate = useNavigate();
  return (
    <div className="min-h-screen w-full flex flex-col items-center justify-center bg-background p-6">
      <div className="max-w-md text-center space-y-5 py-8 px-6 border border-red-200 rounded-lg bg-white shadow">
        <AlertTriangle className="mx-auto text-red-500 h-12 w-12" />
        <h2 className="text-2xl font-bold text-red-600">Access Denied</h2>
        <p className="text-muted-foreground">
          Sorry, you do not have permission to access this page.<br />
          If you believe this is a mistake, contact the platform administrator or try logging in with a different account.
        </p>
        <Button variant="outline" onClick={() => navigate(-1)}>
          Go Back
        </Button>
        <Button variant="default" className="ml-2" onClick={() => navigate("/")}>
          Go to Home
        </Button>
      </div>
    </div>
  );
};

export default UnauthorizedPage;

// -- CHANGELOG --
// - More actionable, friendly access-denied UX.
