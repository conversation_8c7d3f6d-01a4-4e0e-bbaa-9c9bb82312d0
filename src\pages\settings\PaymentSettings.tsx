import React from 'react';
import { useStore } from '@/contexts';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Info } from 'lucide-react';
import SettingsLayout from '@/components/settings/SettingsLayout';
import GlobalPaymentMethods from '@/components/settings/payments/GlobalPaymentMethods';
import AfricanPaymentMethods from '@/components/settings/payments/AfricanPaymentMethods';
import EastAfricanPaymentMethods from '@/components/settings/payments/EastAfricanPaymentMethods';

const PaymentSettings = () => {
  const { currentStore, updateStore } = useStore();
  
  const handleTogglePaymentMethod = (methodId: string, enabled: boolean) => {
    if (!currentStore) return;
    
    // Handle payment_methods as JSON array
    const currentMethods = Array.isArray(currentStore.payment_methods) 
      ? currentStore.payment_methods as string[]
      : [];
    let updatedMethods;
    
    if (enabled && !currentMethods.includes(methodId)) {
      updatedMethods = [...currentMethods, methodId];
    } else if (!enabled && currentMethods.includes(methodId)) {
      updatedMethods = currentMethods.filter(id => id !== methodId);
    } else {
      return; // No change needed
    }
    
    updateStore(currentStore.id, { payment_methods: updatedMethods as any });
  };
  
  const isMethodEnabled = (methodId: string) => {
    const methods = Array.isArray(currentStore?.payment_methods) 
      ? currentStore.payment_methods as string[]
      : [];
    return methods.includes(methodId);
  };
  
  const saveSettings = async () => {
    return Promise.resolve();
  };
  
  const globalContent = (
    <>
      <Alert variant="default" className="bg-blue-50 border-blue-200 mb-6">
        <Info className="h-4 w-4 text-blue-600" />
        <AlertDescription className="text-blue-700">
          Your customers will only see enabled payment methods during checkout. We recommend enabling 
          multiple payment options to improve conversion rates.
        </AlertDescription>
      </Alert>
      
      <GlobalPaymentMethods 
        onToggleMethod={handleTogglePaymentMethod}
        isMethodEnabled={isMethodEnabled}
      />
    </>
  );
  
  const africanContent = (
    <AfricanPaymentMethods 
      onToggleMethod={handleTogglePaymentMethod}
      isMethodEnabled={isMethodEnabled}
    />
  );
  
  const eastAfricanContent = (
    <EastAfricanPaymentMethods 
      onToggleMethod={handleTogglePaymentMethod}
      isMethodEnabled={isMethodEnabled}
    />
  );

  return (
    <SettingsLayout
      title="Payment Methods"
      description="Configure which payment methods your customers can use to pay for orders."
      defaultTab="global"
      tabs={[
        { value: "global", label: "Global", content: globalContent },
        { value: "african", label: "African", content: africanContent },
        { value: "east-african", label: "East African", content: eastAfricanContent },
      ]}
      action={saveSettings}
    />
  );
};

export default PaymentSettings;
