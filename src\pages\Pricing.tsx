import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Link } from "react-router-dom";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Check, Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { planCardData } from "@/components/settings/plans/plansData";
import PlanCard from "@/components/settings/plans/PlanCard";
import PlansComparisonTable from "@/components/settings/plans/PlansComparisonTable";
import { PLAN_FEATURES } from "@/components/settings/plans/planCategories";
import { openPabblyPopup } from "@/utils/pabblyCheckout";
import { useToast } from "@/hooks/use-toast";

// Utility to map PLAN_FEATURES to the correct plan tiers
function generateComparisonFeatures(features) {
  return features.map(f => ({
    name: f.name,
    basic: f.tier === "basic",
    premium: f.tier === "basic" || f.tier === "premium",
    business: true
  }));
}

const comparisonFeatures = generateComparisonFeatures(PLAN_FEATURES);

// Payment links (these are the plan IDs for Pabbly popup)
const PABBLY_PLAN_IDS = {
  Premium: "680bad1766b57e9593152756",
  Business: "680bae0420175f950f72276b"
};

const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  const { toast } = useToast();
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  
  const togglePricing = (value: string) => {
    setIsYearly(value === "yearly");
  };

  // Handler to trigger Pabbly popup
  const handlePabblyPopup = (plan: "Premium" | "Business") => async (e: React.MouseEvent) => {
    e.preventDefault();
    
    if (loadingPlan) {
      return; // Prevent multiple clicks
    }
    
    setLoadingPlan(plan);
    try {
      await openPabblyPopup(PABBLY_PLAN_IDS[plan]);
    } catch (error) {
      console.error('Payment popup error:', error);
      toast({
        title: "Payment popup failed to load",
        description: "Please try again or contact customer support if the problem persists.",
        variant: "destructive"
      });
    } finally {
      setLoadingPlan(null);
    }
    return false;
  };

  return (
    <>
      <section id="pricing" ref={sectionRef} className="py-20 sm:py-24 bg-accent/50 lg:py-[10px]">
        <div className="container px-4 md:px-6">
          <div className="text-center mb-12">
            <motion.div initial={{
              opacity: 0, y: 20
            }} animate={sectionInView ? {
              opacity: 1, y: 0
            } : {
              opacity: 0, y: 20
            }} transition={{
              duration: 0.5
            }} className="inline-flex items-center rounded-full bg-white px-3 py-1 text-sm font-medium text-mduka-700 mb-4">
              <span className="flex h-2 w-2 rounded-full bg-mduka-500 mr-1.5"></span>
              <span>Simple Pricing</span>
            </motion.div>
            
            <motion.h2 initial={{
              opacity: 0, y: 20
            }} animate={sectionInView ? {
              opacity: 1, y: 0
            } : {
              opacity: 0, y: 20
            }} transition={{
              duration: 0.5, delay: 0.1
            }} className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-balance">
              Pricing plans for businesses of all sizes
            </motion.h2>
            
            <motion.p initial={{
              opacity: 0, y: 20
            }} animate={sectionInView ? {
              opacity: 1, y: 0
            } : {
              opacity: 0, y: 20
            }} transition={{
              duration: 0.5, delay: 0.2
            }} className="text-muted-foreground text-lg max-w-2xl mx-auto text-pretty">
              Choose the perfect plan for your business needs. Start with our free tier and upgrade as you grow.
            </motion.p>
          </div>

          <div className="flex justify-center mb-12">
            <motion.div initial={{
              opacity: 0, y: 20
            }} animate={sectionInView ? {
              opacity: 1, y: 0
            } : {
              opacity: 0, y: 20
            }} transition={{
              duration: 0.5, delay: 0.3
            }} className="bg-muted p-1 rounded-lg inline-flex items-center">
              <ToggleGroup type="single" value={isYearly ? 'yearly' : 'monthly'} onValueChange={togglePricing}>
                <ToggleGroupItem value="monthly" className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white">
                  <Clock className="h-3.5 w-3.5" />
                  Monthly
                </ToggleGroupItem>
                <ToggleGroupItem value="yearly" className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white">
                  <Calendar className="h-3.5 w-3.5" />
                  Yearly
                </ToggleGroupItem>
              </ToggleGroup>
            </motion.div>
          </div>
          
          <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-3">
            {planCardData.map((plan, index) => {
              const [planRef, planInView] = useInView({
                triggerOnce: true,
                threshold: 0.1
              });

              // Button logic
              let buttonAction;
              if (plan.title === "Premium" || plan.title === "Business") {
                buttonAction = (
                  <a
                    href="#"
                    onClick={handlePabblyPopup(plan.title as "Premium" | "Business")}
                    className="w-full flex justify-center items-center"
                    aria-label={`Subscribe to ${plan.title} via Pabbly`}
                  >
                    {loadingPlan === plan.title ? "Loading..." : plan.buttonText}
                  </a>
                );
              } else {
                buttonAction = (
                  <Link to="/settings/billing">{plan.buttonText}</Link>
                );
              }

              return (
                <motion.div
                  key={index}
                  ref={planRef}
                  initial={{ opacity: 0, y: 20 }}
                  animate={planInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <PlanCard
                    {...plan}
                    isYearly={isYearly}
                    buttonAction={buttonAction}
                  />
                </motion.div>
              );
            })}
          </div>
          
          <motion.div initial={{
            opacity: 0, y: 20
          }} animate={sectionInView ? {
            opacity: 1, y: 0
          } : {
            opacity: 0, y: 20
          }} transition={{
            duration: 0.5, delay: 0.5
          }} className="text-center mt-12 text-muted-foreground">
            <p className="text-center text-base py-0 my-[100px]">Need a custom plan for your enterprise? <Link to="/pricing" className="text-mduka-600 hover:underline">View all plans</Link></p>
          </motion.div>
        </div>
      </section>
      <div className="max-w-4xl mx-auto">
        <PlansComparisonTable features={comparisonFeatures} collapsible />
        <div className="text-sm text-muted-foreground mt-4 text-center">
          <p>
            Upgrade anytime and unlock features that suit your growth.
            <span className="ml-1 font-semibold text-primary">Cancel any time.</span>
          </p>
        </div>
      </div>
    </>
  );
};

export default Pricing;
