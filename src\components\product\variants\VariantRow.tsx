
import React from 'react';
import { ProductVariant } from '@/types/unified-product';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { TableCell, TableRow } from '@/components/ui/table';
import { Switch } from '@/components/ui/switch';
import { X } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SIZE_OPTIONS, COLOR_OPTIONS } from './constants';

interface VariantRowProps {
  variant: ProductVariant;
  index: number;
  onUpdate: (index: number, field: keyof ProductVariant, value: any) => void;
  onRemove: (index: number) => void;
}

const VariantRow: React.FC<VariantRowProps> = ({
  variant,
  index,
  onUpdate,
  onRemove
}) => {
  return (
    <TableRow>
      <TableCell>
        <Select 
          value={variant.size || 'N/A'}
          onValueChange={(value) => onUpdate(index, 'size', value)}
        >
          <SelectTrigger className="w-24">
            <SelectValue placeholder="Size" />
          </SelectTrigger>
          <SelectContent>
            {SIZE_OPTIONS.map((size) => (
              <SelectItem key={size} value={size}>{size}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </TableCell>
      <TableCell>
        <Select 
          value={variant.color || 'N/A'}
          onValueChange={(value) => onUpdate(index, 'color', value)}
        >
          <SelectTrigger className="w-28">
            <SelectValue placeholder="Color" />
          </SelectTrigger>
          <SelectContent>
            {COLOR_OPTIONS.map((color) => (
              <SelectItem key={color} value={color}>{color}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </TableCell>
      <TableCell>
        <Input
          type="number"
          value={variant.price}
          onChange={(e) => onUpdate(index, 'price', parseFloat(e.target.value))}
          className="w-24"
          min="0"
          step="0.01"
        />
      </TableCell>
      <TableCell>
        <Input
          value={variant.sku || ''}
          onChange={(e) => onUpdate(index, 'sku', e.target.value ? `${e.target.value}-custom` : '')}
          className="w-32"
        />
      </TableCell>
      <TableCell>
        <Input
          type="number"
          value={variant.stock_quantity}
          onChange={(e) => onUpdate(index, 'stock_quantity', parseInt(e.target.value))}
          className="w-20"
          min="0"
        />
      </TableCell>
      <TableCell>
        <Switch
          checked={variant.is_active}
          onCheckedChange={(checked) => onUpdate(index, 'is_active', checked)}
        />
      </TableCell>
      <TableCell>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={() => onRemove(index)}
          className="h-8 w-8 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
};

export default VariantRow;
