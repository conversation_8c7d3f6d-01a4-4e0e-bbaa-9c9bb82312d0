
import { supabase } from '@/integrations/supabase/client';

/**
 * Handle social provider authentication (Google, etc.)
 */
export const signInWithSocialProvider = async (provider: 'google'): Promise<{ success: boolean; error?: any; url?: string }> => {
  try {
    console.log(`Attempting to sign in with ${provider}`);
    
    // Determine the current environment's origin
    const currentOrigin = window.location.origin;
    
    // Create the redirect URL based on the current origin
    const redirectUrl = `${currentOrigin}/dashboard`;
    
    console.log(`Setting redirect URL to: ${redirectUrl}`);
    
    const { data, error } = await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: redirectUrl,
        queryParams: {
          access_type: 'offline', 
          prompt: 'consent'
        }
      },
    });
    
    if (error) {
      console.error(`${provider} sign-in error:`, error.message);
      return { success: false, error };
    }
    
    if (data?.url) {
      console.log(`${provider} sign-in URL generated:`, data.url);
      // Return the URL so we can handle the redirect in the component
      return { success: true, url: data.url };
    }
    
    return { success: true };
  } catch (error: any) {
    console.error(`${provider} sign-in error:`, error);
    return { success: false, error };
  }
};
