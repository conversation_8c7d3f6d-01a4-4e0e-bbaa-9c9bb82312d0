
import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { useStore } from '@/contexts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Plus, Trash2, Edit, Calendar, Tag, ArrowRight, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

interface Discount {
  id: string;
  code: string;
  type: 'percentage' | 'fixed';
  value: number;
  minimumPurchase?: number;
  startDate?: Date;
  endDate?: Date;
  isActive: boolean;
  usageLimit?: number;
  usageCount: number;
  description?: string;
  appliesTo: 'all' | 'specific';
  specificProducts?: string[];
}

const DiscountManagement: React.FC = () => {
  const { currentStore } = useStore();
  const [discounts, setDiscounts] = useState<Discount[]>([
    {
      id: '1',
      code: 'WELCOME10',
      type: 'percentage',
      value: 10,
      minimumPurchase: 0,
      startDate: new Date(),
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
      isActive: true,
      usageLimit: 100,
      usageCount: 0,
      description: 'Welcome discount for new customers',
      appliesTo: 'all',
    },
    {
      id: '2',
      code: 'SUMMER25',
      type: 'percentage',
      value: 25,
      minimumPurchase: 50,
      startDate: new Date(),
      endDate: new Date(new Date().setMonth(new Date().getMonth() + 3)),
      isActive: true,
      usageLimit: 50,
      usageCount: 12,
      description: 'Summer sale discount',
      appliesTo: 'all',
    },
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [discountToDelete, setDiscountToDelete] = useState<Discount | null>(null);
  const [editingDiscount, setEditingDiscount] = useState<Discount | null>(null);

  const [newDiscount, setNewDiscount] = useState<Omit<Discount, 'id' | 'usageCount'>>({
    code: '',
    type: 'percentage',
    value: 10,
    minimumPurchase: 0,
    isActive: true,
    appliesTo: 'all',
  });

  const handleAddDiscount = () => {
    if (!newDiscount.code) {
      toast.error('Discount code is required');
      return;
    }

    if (newDiscount.value <= 0) {
      toast.error('Discount value must be greater than 0');
      return;
    }

    if (discounts.some(d => d.code === newDiscount.code)) {
      toast.error('A discount with this code already exists');
      return;
    }

    const discount: Discount = {
      ...newDiscount,
      id: `discount-${Date.now()}`,
      usageCount: 0,
    };

    setDiscounts([...discounts, discount]);
    setIsDialogOpen(false);
    resetForm();
    toast.success('Discount added successfully');
  };

  const handleUpdateDiscount = () => {
    if (!editingDiscount) return;

    if (!editingDiscount.code) {
      toast.error('Discount code is required');
      return;
    }

    if (editingDiscount.value <= 0) {
      toast.error('Discount value must be greater than 0');
      return;
    }

    const codeExists = discounts.some(
      d => d.code === editingDiscount.code && d.id !== editingDiscount.id
    );

    if (codeExists) {
      toast.error('A discount with this code already exists');
      return;
    }

    const updatedDiscounts = discounts.map(d => 
      d.id === editingDiscount.id ? editingDiscount : d
    );

    setDiscounts(updatedDiscounts);
    setIsDialogOpen(false);
    setEditingDiscount(null);
    toast.success('Discount updated successfully');
  };

  const handleDeleteDiscount = () => {
    if (!discountToDelete) return;

    const updatedDiscounts = discounts.filter(d => d.id !== discountToDelete.id);
    setDiscounts(updatedDiscounts);
    setIsDeleteDialogOpen(false);
    setDiscountToDelete(null);
    toast.success('Discount deleted successfully');
  };

  const handleToggleActive = (id: string) => {
    const updatedDiscounts = discounts.map(d => 
      d.id === id ? { ...d, isActive: !d.isActive } : d
    );
    setDiscounts(updatedDiscounts);
  };

  const resetForm = () => {
    setNewDiscount({
      code: '',
      type: 'percentage',
      value: 10,
      minimumPurchase: 0,
      isActive: true,
      appliesTo: 'all',
    });
  };

  return (
    <DashboardLayout>
      <Helmet>
        <title>Discount Management - m-duka</title>
      </Helmet>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Discount Management</h1>
          <Button 
            onClick={() => {
              setEditingDiscount(null);
              resetForm();
              setIsDialogOpen(true);
            }} 
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Create Discount
          </Button>
        </div>

        <Tabs defaultValue="active">
          <TabsList className="mb-4">
            <TabsTrigger value="active">Active Discounts</TabsTrigger>
            <TabsTrigger value="all">All Discounts</TabsTrigger>
          </TabsList>
          
          <TabsContent value="active">
            {discounts.filter(d => d.isActive).length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mb-4 flex justify-center">
                    <AlertCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Active Discounts</h3>
                  <p className="text-muted-foreground mb-4">
                    You don't have any active discounts. Create a discount to offer special pricing to your customers.
                  </p>
                  <Button onClick={() => setIsDialogOpen(true)}>Create Your First Discount</Button>
                </CardContent>
              </Card>
            ) : (
              <DiscountTable 
                discounts={discounts.filter(d => d.isActive)} 
                onEdit={setEditingDiscount}
                onDelete={setDiscountToDelete}
                onOpenDeleteDialog={setIsDeleteDialogOpen}
                onOpenEditDialog={setIsDialogOpen}
                onToggleActive={handleToggleActive}
              />
            )}
          </TabsContent>
          
          <TabsContent value="all">
            {discounts.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="mb-4 flex justify-center">
                    <AlertCircle className="h-12 w-12 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-medium mb-2">No Discounts</h3>
                  <p className="text-muted-foreground mb-4">
                    You haven't created any discounts yet. Discounts help attract customers and boost sales.
                  </p>
                  <Button onClick={() => setIsDialogOpen(true)}>Create Your First Discount</Button>
                </CardContent>
              </Card>
            ) : (
              <DiscountTable 
                discounts={discounts} 
                onEdit={setEditingDiscount}
                onDelete={setDiscountToDelete}
                onOpenDeleteDialog={setIsDeleteDialogOpen}
                onOpenEditDialog={setIsDialogOpen}
                onToggleActive={handleToggleActive}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Create/Edit Discount Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingDiscount ? 'Edit Discount' : 'Create New Discount'}
            </DialogTitle>
            <DialogDescription>
              {editingDiscount 
                ? 'Update the discount details below.' 
                : 'Add a new discount to offer to your customers.'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-6 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="code">Discount Code</Label>
                <Input
                  id="code"
                  value={editingDiscount ? editingDiscount.code : newDiscount.code}
                  onChange={(e) => {
                    if (editingDiscount) {
                      setEditingDiscount({ ...editingDiscount, code: e.target.value.toUpperCase() });
                    } else {
                      setNewDiscount({ ...newDiscount, code: e.target.value.toUpperCase() });
                    }
                  }}
                  placeholder="e.g., SUMMER20, WELCOME10"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="isActive">Status</Label>
                <div className="flex items-center space-x-2 pt-2">
                  <Switch
                    id="isActive"
                    checked={editingDiscount ? editingDiscount.isActive : newDiscount.isActive}
                    onCheckedChange={(checked) => {
                      if (editingDiscount) {
                        setEditingDiscount({ ...editingDiscount, isActive: checked });
                      } else {
                        setNewDiscount({ ...newDiscount, isActive: checked });
                      }
                    }}
                  />
                  <Label htmlFor="isActive" className="cursor-pointer">
                    {(editingDiscount ? editingDiscount.isActive : newDiscount.isActive) 
                      ? 'Active' 
                      : 'Inactive'}
                  </Label>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="type">Discount Type</Label>
                <Select
                  value={editingDiscount ? editingDiscount.type : newDiscount.type}
                  onValueChange={(value: 'percentage' | 'fixed') => {
                    if (editingDiscount) {
                      setEditingDiscount({ ...editingDiscount, type: value });
                    } else {
                      setNewDiscount({ ...newDiscount, type: value });
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Percentage (%)</SelectItem>
                    <SelectItem value="fixed">Fixed Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="value">Discount Value</Label>
                <div className="flex items-center">
                  <Input
                    id="value"
                    type="number"
                    value={editingDiscount ? editingDiscount.value : newDiscount.value}
                    onChange={(e) => {
                      const value = parseFloat(e.target.value);
                      if (editingDiscount) {
                        setEditingDiscount({ ...editingDiscount, value });
                      } else {
                        setNewDiscount({ ...newDiscount, value });
                      }
                    }}
                    min={0}
                  />
                  <span className="ml-2">
                    {(editingDiscount ? editingDiscount.type : newDiscount.type) === 'percentage' ? '%' : 'KES'}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="minimumPurchase">Minimum Purchase Amount (Optional)</Label>
                <div className="flex items-center">
                  <span className="mr-2">KES</span>
                  <Input
                    id="minimumPurchase"
                    type="number"
                    value={editingDiscount ? (editingDiscount.minimumPurchase || 0) : (newDiscount.minimumPurchase || 0)}
                    onChange={(e) => {
                      const minimumPurchase = parseFloat(e.target.value);
                      if (editingDiscount) {
                        setEditingDiscount({ ...editingDiscount, minimumPurchase });
                      } else {
                        setNewDiscount({ ...newDiscount, minimumPurchase });
                      }
                    }}
                    min={0}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="usageLimit">Usage Limit (Optional)</Label>
                <Input
                  id="usageLimit"
                  type="number"
                  value={editingDiscount ? (editingDiscount.usageLimit || '') : (newDiscount.usageLimit || '')}
                  onChange={(e) => {
                    const value = e.target.value === '' ? undefined : parseInt(e.target.value);
                    if (editingDiscount) {
                      setEditingDiscount({ ...editingDiscount, usageLimit: value });
                    } else {
                      setNewDiscount({ ...newDiscount, usageLimit: value });
                    }
                  }}
                  min={0}
                  placeholder="Unlimited"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                value={editingDiscount ? (editingDiscount.description || '') : (newDiscount.description || '')}
                onChange={(e) => {
                  if (editingDiscount) {
                    setEditingDiscount({ ...editingDiscount, description: e.target.value });
                  } else {
                    setNewDiscount({ ...newDiscount, description: e.target.value });
                  }
                }}
                placeholder="Brief description of this discount"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsDialogOpen(false);
              if (!editingDiscount) {
                resetForm();
              }
            }}>
              Cancel
            </Button>
            <Button onClick={editingDiscount ? handleUpdateDiscount : handleAddDiscount}>
              {editingDiscount ? 'Update Discount' : 'Create Discount'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the discount code "{discountToDelete?.code}"?
              This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteDiscount}>
              Delete Discount
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

interface DiscountTableProps {
  discounts: Discount[];
  onEdit: (discount: Discount) => void;
  onDelete: (discount: Discount) => void;
  onOpenEditDialog: (open: boolean) => void;
  onOpenDeleteDialog: (open: boolean) => void;
  onToggleActive: (id: string) => void;
}

const DiscountTable: React.FC<DiscountTableProps> = ({ 
  discounts, 
  onEdit, 
  onDelete, 
  onOpenEditDialog, 
  onOpenDeleteDialog,
  onToggleActive
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Discount Codes</CardTitle>
        <CardDescription>
          Manage your store's discount codes and promotions
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Code</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Value</TableHead>
              <TableHead>Min. Purchase</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Usage</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {discounts.map((discount) => (
              <TableRow key={discount.id}>
                <TableCell className="font-medium">{discount.code}</TableCell>
                <TableCell>
                  {discount.type === 'percentage' ? 'Percentage' : 'Fixed Amount'}
                </TableCell>
                <TableCell>
                  {discount.type === 'percentage' 
                    ? `${discount.value}%` 
                    : `KES ${discount.value.toFixed(2)}`}
                </TableCell>
                <TableCell>
                  {discount.minimumPurchase 
                    ? `KES ${discount.minimumPurchase.toFixed(2)}` 
                    : 'None'}
                </TableCell>
                <TableCell>
                  <Badge variant={discount.isActive ? "default" : "secondary"}>
                    {discount.isActive ? 'Active' : 'Inactive'}
                  </Badge>
                </TableCell>
                <TableCell>
                  {discount.usageCount}
                  {discount.usageLimit ? ` / ${discount.usageLimit}` : ''}
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => onToggleActive(discount.id)}
                    >
                      {discount.isActive ? 'Disable' : 'Enable'}
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      onClick={() => {
                        onEdit(discount);
                        onOpenEditDialog(true);
                      }}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="text-destructive hover:text-destructive"
                      onClick={() => {
                        onDelete(discount);
                        onOpenDeleteDialog(true);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default DiscountManagement;
