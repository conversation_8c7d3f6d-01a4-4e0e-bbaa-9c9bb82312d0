
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageCircle, ShoppingCart, Package } from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

const WorkflowFeatureNotFound = () => {
  const navigate = useNavigate();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Feature Not Found</CardTitle>
        <CardDescription>
          The workflow feature you're looking for doesn't exist. Please select a feature from the sidebar.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">
          You might be looking for one of these popular workflow features:
        </p>
        <div className="flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={() => navigate('/settings/workflow/whatsapp')}>
            <MessageCircle className="h-4 w-4 mr-2" />
            WhatsApp
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigate('/settings/workflow/orders')}>
            <ShoppingCart className="h-4 w-4 mr-2" />
            Orders
          </Button>
          <Button variant="outline" size="sm" onClick={() => navigate('/settings/workflow/inventory')}>
            <Package className="h-4 w-4 mr-2" />
            Inventory
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default WorkflowFeatureNotFound;
