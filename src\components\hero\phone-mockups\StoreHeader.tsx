
import { Camera, Map } from "lucide-react";
import { motion } from "framer-motion";

const StoreHeader = () => {
  return (
    <div className="bg-gradient-to-r from-green-600 to-green-500 text-white p-4 rounded-t-lg">
      <div className="flex items-center justify-between mb-1">
        <div className="text-xl font-bold flex items-center">
          <div className="relative mr-1">
            <div className="bg-white/20 px-2.5 py-1.5 rounded-xl text-white inline-block relative">
              M
              {/* SMS-style tail */}
              <div className="absolute -bottom-[6px] left-1/2 transform -translate-x-1/2 w-3 h-3 bg-white/20 rotate-45 rounded-sm"></div>
            </div>
          </div>
          <span>-Duka</span>
        </div>
        <Camera className="h-4 w-4" />
      </div>
      
      <div className="text-xs flex items-center">
        <Map className="h-3 w-3 mr-1" />
        <span>Dar es Salaam, Tanzania</span>
      </div>
      
      {/* Status indicator */}
      <motion.div 
        className="mt-2 flex items-center gap-1 bg-white/20 px-2 py-0.5 rounded-full w-max"
        animate={{ opacity: [0.7, 1, 0.7] }}
        transition={{ duration: 2, repeat: Infinity }}
      >
        <span className="h-1.5 w-1.5 bg-green-200 rounded-full"></span>
        <span className="text-xs text-white/90">Online</span>
      </motion.div>
    </div>
  );
};

export default StoreHeader;
