
import React from 'react';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

export interface SidebarItemProps {
  icon: React.ReactNode;
  label: string;
  href: string;
  active?: boolean;
  end?: boolean;
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon,
  label,
  href,
  active,
  end,
}) => {
  return (
    <Button
      variant="ghost"
      className={cn(
        "w-full justify-start px-3 py-2 h-auto",
        active && "bg-accent"
      )}
      asChild
    >
      <Link to={href}>
        <span className="mr-2">{icon}</span>
        <span>{label}</span>
      </Link>
    </Button>
  );
};

export default SidebarItem;
