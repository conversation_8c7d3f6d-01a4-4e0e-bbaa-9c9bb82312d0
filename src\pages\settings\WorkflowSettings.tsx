
import React from 'react';
import { useLocation, Routes, Route, Navigate } from 'react-router-dom';
import { useStore, useAuth } from '@/contexts';
import { Heading } from '@/components/ui/heading';
import { Workflow } from 'lucide-react';

import WorkflowSidebar from '@/components/settings/workflow/WorkflowSidebar';
import WorkflowHeader from './workflow/WorkflowHeader';
import WorkflowDashboard from './workflow/WorkflowDashboard';
import WorkflowFeatureNotFound from './workflow/WorkflowFeatureNotFound';
import workflowTypeData from './workflow/workflowTypeData';

// Import workflow components
import WhatsAppWorkflow from '@/components/settings/workflow/WhatsAppWorkflow';
import OrderWorkflow from '@/components/settings/workflow/order/OrderWorkflow';
import InventoryWorkflow from '@/components/settings/workflow/inventory/InventoryWorkflow';
import ComingSoonPlaceholder from '@/components/settings/workflow/ComingSoonPlaceholder';

const WorkflowSettings = () => {
  const { user } = useAuth();
  const { currentStore } = useStore();
  const location = useLocation();
  
  const workflowType = location.pathname.split('/settings/workflow/')[1];
  
  return (
    <div className="fixed inset-0 bg-background z-50 flex flex-col h-full">
      <WorkflowHeader />
      
      <div className="flex flex-1 overflow-hidden">
        <WorkflowSidebar />
        
        <div className="flex-1 p-6 overflow-auto">
          <Heading 
            title={`Workflow Automation${workflowType ? ` - ${workflowTypeData[workflowType]?.title || workflowType.charAt(0).toUpperCase() + workflowType.slice(1)}` : ''}`} 
            description="Automate your store operations and communications"
            icon={<Workflow className="h-6 w-6" />}
          />
          
          <div className="mt-6">
            <Routes>
              <Route path="/" element={
                <WorkflowDashboard store={currentStore} />
              } />
              <Route path="whatsapp" element={<WhatsAppWorkflow />} />
              <Route path="orders" element={<OrderWorkflow />} />
              <Route path="inventory" element={<InventoryWorkflow />} />
              
              {Object.entries(workflowTypeData).map(([key, data]) => {
                if (key !== 'whatsapp' && key !== 'orders' && key !== 'inventory') {
                  return (
                    <Route 
                      key={key}
                      path={key} 
                      element={
                        <ComingSoonPlaceholder
                          title={data.title}
                          description={data.description}
                          icon={data.icon}
                          expectedDate={data.expectedDate}
                          actionLabel="Notify me when available"
                          onAction={() => {
                            // Navigate back to workflow dashboard
                            return <Navigate to="/settings/workflow" />;
                          }}
                        />
                      } 
                    />
                  );
                }
                return null;
              })}
              
              <Route path="*" element={<WorkflowFeatureNotFound />} />
            </Routes>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WorkflowSettings;
