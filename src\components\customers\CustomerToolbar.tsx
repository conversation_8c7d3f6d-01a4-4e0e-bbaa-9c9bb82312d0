
import React from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

export interface CustomerToolbarProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  totalCustomers: number;
}

const CustomerToolbar = ({ 
  searchQuery, 
  onSearchChange, 
  totalCustomers 
}: CustomerToolbarProps) => {
  return (
    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">Customers</h1>
        <p className="text-muted-foreground mt-1">
          Manage your customer relationships ({totalCustomers} total)
        </p>
      </div>
      
      <div className="relative w-full sm:w-auto mt-4 sm:mt-0">
        <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search customers..."
          className="pl-8 w-full sm:w-64"
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
        />
      </div>
    </div>
  );
};

export default CustomerToolbar;
