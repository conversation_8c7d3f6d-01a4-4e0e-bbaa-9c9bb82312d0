
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface EmptyProductStateProps {
  hasStore: boolean;
}

const EmptyProductState: React.FC<EmptyProductStateProps> = ({ hasStore }) => {
  const navigate = useNavigate();

  if (!hasStore) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>No Store Selected</CardTitle>
          <CardDescription>
            Please select a store to manage its products
          </CardDescription>
        </CardHeader>
        <CardFooter>
          <Button onClick={() => navigate('/dashboard')}>
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>No Products Found</CardTitle>
        <CardDescription>
          You don't have any products in your store yet. Start by adding one!
        </CardDescription>
      </CardHeader>
      <CardFooter>
        <Button onClick={() => navigate('/create-product')}>
          Create First Product
        </Button>
      </CardFooter>
    </Card>
  );
};

export default EmptyProductState;
