
import React from 'react';
import { Truck, CheckCircle } from 'lucide-react';

const ProductShippingInfo: React.FC = () => {
  return (
    <div className="border-t pt-6 space-y-4">
      <div className="flex items-start gap-3">
        <Truck className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="font-medium">Shipping</h4>
          <p className="text-sm text-muted-foreground">
            Free shipping on orders over $50. Delivery within 3-5 business days.
          </p>
        </div>
      </div>
      
      <div className="flex items-start gap-3">
        <CheckCircle className="h-5 w-5 text-muted-foreground flex-shrink-0 mt-0.5" />
        <div>
          <h4 className="font-medium">Returns</h4>
          <p className="text-sm text-muted-foreground">
            30-day easy return policy. Read our <a href="/shop/returns" className="underline">return policy</a>.
          </p>
        </div>
      </div>
    </div>
  );
};

export default ProductShippingInfo;
