
import React from 'react';
import { FormDescription } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import DeliveryZonesEmptyState from './DeliveryZonesEmptyState';
import DeliveryZoneRow from './DeliveryZoneRow';

// Removed imports for allAfricanRegions, getCountryCode, isAfricanCountry 
// as this data is now passed down or handled higher up.

interface DeliveryZone {
  id: string;
  name: string;
  rate: string;
  region: string;
}

interface DeliveryZonesSectionProps {
  form: any;
  country: string;
  availableRegions: string[]; // Added prop for available regions
}

const DeliveryZonesSection: React.FC<DeliveryZonesSectionProps> = ({ 
  form, 
  country, 
  availableRegions // Use passed-in regions
}) => {
  const watchDeliveryZones = form.watch("deliveryZones") || [];
  
  // Regions are now provided via props
  const regions = availableRegions || []; 
  
  const addDeliveryZone = () => {
    const currentZones = form.getValues("deliveryZones") || [];
    form.setValue("deliveryZones", [
      ...currentZones, 
      { 
        id: Date.now().toString(), 
        name: "", 
        rate: "", 
        // Default to the first available region, or empty if none
        region: regions.length > 0 ? regions[0] : "" 
      }
    ]);
  };

  const removeDeliveryZone = (index: number) => {
    const currentZones = form.getValues("deliveryZones") || [];
    const updatedZones = currentZones.filter((_: any, i: number) => i !== index);
    form.setValue("deliveryZones", updatedZones);
  };

  // The check for isAfricanCountry is now handled in the parent component (DomesticShippingCard)
  // So, if this component renders, we assume it's for an African country.

  return (
    <div className="space-y-4 mt-6 border rounded-md p-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Delivery Zones</h3>
        <Button 
          type="button" 
          variant="outline" 
          size="sm" 
          onClick={addDeliveryZone}
          disabled={regions.length === 0} // Disable add if no regions loaded/available
        >
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Zone
        </Button>
      </div>
      
      {regions.length === 0 && watchDeliveryZones.length === 0 && (
        <p className="text-sm text-muted-foreground">No regions available for {country}.</p>
      )}

      {watchDeliveryZones.length === 0 && regions.length > 0 ? (
        <DeliveryZonesEmptyState country={country} />
      ) : (
        watchDeliveryZones.map((zone: DeliveryZone, index: number) => (
          <DeliveryZoneRow 
            key={zone.id}
            index={index}
            regions={regions} // Pass the available regions to the row
            form={form}
            onRemove={removeDeliveryZone}
          />
        ))
      )}
      
      {watchDeliveryZones.length > 0 && (
        <FormDescription className="mt-2">
          Set specific delivery rates for different regions in {country}.
        </FormDescription>
      )}
    </div>
  );
};

export default DeliveryZonesSection;
