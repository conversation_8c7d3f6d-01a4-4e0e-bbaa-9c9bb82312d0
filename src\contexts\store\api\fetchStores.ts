import { supabase } from '@/integrations/supabase/client';
import { Store } from '@/types/store';
import { toast } from 'sonner';
import { MOCK_STORES, MOCK_STORES_FOR_DEVELOPMENT } from '../mockData';
import { formatStoreFromDb } from '../utils/formatters';

export const fetchUserStores = async (userId: string): Promise<Store[]> => {
  try {
    const { data, error } = await supabase
      .from('stores')
      .select('*')
      .eq('owner_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(formatStoreFromDb);
  } catch (error) {
    console.error('Failed to load stores', error);
    toast.error('Failed to load stores');
    return [];
  }
};
