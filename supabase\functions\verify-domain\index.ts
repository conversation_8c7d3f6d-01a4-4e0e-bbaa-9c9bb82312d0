
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );
    
    const { domain, storeId } = await req.json();
    
    if (!domain) {
      return new Response(
        JSON.stringify({ error: 'Domain parameter is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      );
    }
    
    // Generate a random verification code
    const verificationCode = generateVerificationCode();
    
    // Check if we're using a mock store ID
    const isMockStore = storeId === 'mock-store-1' || storeId?.startsWith('mock-');
    
    if (isMockStore) {
      // For mock/development stores, just return success without DB operations
      console.log('Using mock store, skipping database operations');
      return new Response(
        JSON.stringify({ 
          success: true, 
          verificationCode,
          instructions: `To verify your domain, add a TXT record with the name "_m-duka-verify" and the value "${verificationCode}"`
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    // For real stores, continue with normal database operations
    if (!storeId) {
      return new Response(
        JSON.stringify({ error: 'StoreId parameter is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      );
    }
    
    // Check if the domain already exists in the database
    const { data: existingDomain, error: checkError } = await supabaseClient
      .from('store_domains')
      .select('*')
      .eq('domain', domain)
      .maybeSingle();
    
    if (checkError) {
      console.error('Error checking domain:', checkError);
      return new Response(
        JSON.stringify({ error: 'Failed to check domain' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      );
    }
    
    if (existingDomain && existingDomain.store_id !== storeId) {
      return new Response(
        JSON.stringify({ error: 'Domain is already being used by another store' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      );
    }
    
    // If the domain already exists for this store, update the verification code
    if (existingDomain) {
      const { error: updateError } = await supabaseClient
        .from('store_domains')
        .update({ verification_code: verificationCode, verified: false })
        .eq('id', existingDomain.id);
      
      if (updateError) {
        console.error('Error updating domain:', updateError);
        return new Response(
          JSON.stringify({ error: 'Failed to update domain verification' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        );
      }
    } else {
      // Otherwise, insert a new domain record
      const { error: insertError } = await supabaseClient
        .from('store_domains')
        .insert({
          store_id: storeId,
          domain,
          verification_code: verificationCode,
          verified: false
        });
      
      if (insertError) {
        console.error('Error inserting domain:', insertError);
        return new Response(
          JSON.stringify({ error: 'Failed to create domain verification' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        );
      }
    }
    
    return new Response(
      JSON.stringify({ 
        success: true, 
        verificationCode,
        instructions: `To verify your domain, add a TXT record with the name "_m-duka-verify" and the value "${verificationCode}"`
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});

// Utility function to generate a verification code
function generateVerificationCode(): string {
  return 'mduka-' + 
    Array.from(crypto.getRandomValues(new Uint8Array(16)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('');
}
