
import { motion } from "framer-motion";

interface PhoneFrameProps {
  children: React.ReactNode;
  className?: string;
  initial?: any;
  animate?: any;
}

const PhoneFrame = ({ children, className = "", initial, animate }: PhoneFrameProps) => {
  return (
    <motion.div 
      initial={initial}
      animate={animate}
      transition={{ duration: 0.5, delay: 0.4 }}
      className={`w-[280px] h-[580px] bg-white rounded-[36px] shadow-xl border-8 border-gray-800 relative z-10 overflow-hidden ${className}`}
    >
      {/* Phone Status Bar */}
      <div className="absolute top-0 w-full h-6 bg-gray-800 rounded-t-[28px] flex items-center justify-between px-6">
        <div className="h-1 w-16 bg-gray-600 rounded-full"></div>
        <div className="h-2 w-2 bg-gray-600 rounded-full"></div>
      </div>
      
      {/* Phone Notch */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-5 bg-gray-800 rounded-b-xl"></div>
      
      {/* Phone Frame Reflections */}
      <div className="absolute top-1/3 right-0 w-1 h-32 bg-white/10 rounded-l-full"></div>
      <div className="absolute bottom-1/3 left-0 w-1 h-32 bg-white/10 rounded-r-full"></div>
      
      {/* Content */}
      <div className="h-full pt-6 px-3 overflow-hidden">
        {children}
      </div>
      
      {/* Home Indicator */}
      <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-gray-600 rounded-full"></div>
    </motion.div>
  );
};

export default PhoneFrame;
