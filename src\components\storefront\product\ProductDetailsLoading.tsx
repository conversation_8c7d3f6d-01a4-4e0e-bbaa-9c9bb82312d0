
import React from 'react';
import { Container } from '@/components/ui/container';

const ProductDetailsLoading: React.FC = () => {
  return (
    <Container className="py-12">
      <div className="flex flex-col space-y-4 animate-pulse">
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="h-[400px] bg-gray-200 rounded"></div>
          <div className="space-y-4">
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
            <div className="h-6 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
            <div className="h-4 bg-gray-200 rounded w-full"></div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default ProductDetailsLoading;
