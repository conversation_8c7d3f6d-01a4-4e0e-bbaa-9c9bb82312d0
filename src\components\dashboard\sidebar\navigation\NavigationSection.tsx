
import React from 'react';
import SidebarItem from '../SidebarItem';

export interface NavigationItemType {
  name: string;
  path: string;
  icon: React.ReactNode;
  end?: boolean;
}

interface NavigationSectionProps {
  items: NavigationItemType[];
  isActiveFunc: (path: string, end?: boolean) => boolean;
}

const NavigationSection: React.FC<NavigationSectionProps> = ({ 
  items, 
  isActiveFunc 
}) => {
  return (
    <div className="space-y-1">
      {items.map((item) => (
        <SidebarItem
          key={item.path}
          icon={item.icon}
          label={item.name}
          href={item.path}
          active={isActiveFunc(item.path, item.end)}
          end={item.end}
        />
      ))}
    </div>
  );
};

export default NavigationSection;
