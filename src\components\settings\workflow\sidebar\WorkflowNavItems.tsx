
import React from 'react';
import { 
  MessageCircle, 
  Users, 
  ShoppingCart, 
  Truck, 
  Package, 
  RefreshCw, 
  AlertTriangle,
  Bell,
  CheckSquare,
  BarChart3,
  Calendar,
  Store,
} from 'lucide-react';
import { useLocation } from 'react-router-dom';
import WorkflowNavItem from './WorkflowNavItem';

export interface WorkflowNavItemData {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
  status: 'available' | 'coming-soon' | 'beta';
  date?: string;
}

const WorkflowNavItems: React.FC = () => {
  const location = useLocation();
  const currentPath = location.pathname;
  
  const workflowItems: WorkflowNavItemData[] = [
    {
      icon: <MessageCircle className="h-4 w-4" />,
      title: "WhatsApp Automations",
      description: "Configure automatic WhatsApp messages",
      href: "/settings/workflow/whatsapp",
      status: 'available'
    },
    {
      icon: <ShoppingCart className="h-4 w-4" />,
      title: "Order Notifications",
      description: "Automate messages for order status changes",
      href: "/settings/workflow/orders",
      status: 'available'
    },
    {
      icon: <Users className="h-4 w-4" />,
      title: "Customer Journey",
      description: "Engagement messages throughout customer lifecycle",
      href: "/settings/workflow/customers",
      status: 'coming-soon',
      date: 'Q3 2023'
    },
    {
      icon: <Truck className="h-4 w-4" />,
      title: "Delivery Updates",
      description: "Shipping and delivery notifications",
      href: "/settings/workflow/delivery",
      status: 'coming-soon',
      date: 'Q3 2023'
    },
    {
      icon: <Package className="h-4 w-4" />,
      title: "Inventory Alerts",
      description: "Stock level and product notifications",
      href: "/settings/workflow/inventory",
      status: 'coming-soon',
      date: 'Q4 2023'
    },
    {
      icon: <RefreshCw className="h-4 w-4" />,
      title: "Returns & Refunds",
      description: "Return request and approval workflows",
      href: "/settings/workflow/returns",
      status: 'coming-soon',
      date: 'Q4 2023'
    },
    {
      icon: <AlertTriangle className="h-4 w-4" />,
      title: "Staff Alerts",
      description: "Internal team notifications and escalations",
      href: "/settings/workflow/staff",
      status: 'coming-soon',
      date: 'Q1 2024'
    },
    {
      icon: <Bell className="h-4 w-4" />,
      title: "Event Triggers",
      description: "Custom events and notification rules",
      href: "/settings/workflow/events",
      status: 'coming-soon',
      date: 'Q1 2024'
    },
    {
      icon: <CheckSquare className="h-4 w-4" />,
      title: "Approval Workflows",
      description: "Multi-step approval processes",
      href: "/settings/workflow/approvals",
      status: 'coming-soon',
      date: 'Q1 2024'
    },
    {
      icon: <BarChart3 className="h-4 w-4" />,
      title: "Performance Reports",
      description: "Scheduled reports and analytics",
      href: "/settings/workflow/reports",
      status: 'coming-soon',
      date: 'Q2 2024'
    },
    {
      icon: <Calendar className="h-4 w-4" />,
      title: "Scheduled Messages",
      description: "Timed and recurring communications",
      href: "/settings/workflow/scheduled",
      status: 'coming-soon',
      date: 'Q2 2024'
    },
    {
      icon: <Store className="h-4 w-4" />,
      title: "Store Events",
      description: "Sales, promotions and special events",
      href: "/settings/workflow/store-events",
      status: 'coming-soon',
      date: 'Q2 2024'
    }
  ];

  return (
    <div className="space-y-2 pt-4">
      {workflowItems.map((item, index) => (
        <WorkflowNavItem
          key={index}
          icon={item.icon}
          title={item.title}
          description={item.description}
          href={item.href}
          active={currentPath === item.href}
          status={item.status}
          date={item.date}
        />
      ))}
    </div>
  );
};

export default WorkflowNavItems;
