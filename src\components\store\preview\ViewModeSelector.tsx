
import React from 'react';
import { Monitor, Smartphone, Tablet } from 'lucide-react';
import { Toggle } from '@/components/ui/toggle';

interface ViewModeSelectorProps {
  viewMode: 'desktop' | 'tablet' | 'mobile';
  setViewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
}

const ViewModeSelector: React.FC<ViewModeSelectorProps> = ({ viewMode, setViewMode }) => {
  return (
    <div className="flex items-center gap-2">
      <Toggle
        pressed={viewMode === 'mobile'}
        onPressedChange={() => setViewMode('mobile')}
        aria-label="Mobile view"
        size="sm"
      >
        <Smartphone className="h-4 w-4" />
      </Toggle>
      <Toggle
        pressed={viewMode === 'tablet'}
        onPressedChange={() => setViewMode('tablet')}
        aria-label="Tablet view"
        size="sm"
      >
        <Tablet className="h-4 w-4" />
      </Toggle>
      <Toggle
        pressed={viewMode === 'desktop'}
        onPressedChange={() => setViewMode('desktop')}
        aria-label="Desktop view"
        size="sm"
      >
        <Monitor className="h-4 w-4" />
      </Toggle>
    </div>
  );
};

export default ViewModeSelector;
