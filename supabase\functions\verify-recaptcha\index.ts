
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";

// CORS headers for web requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    // Handle preflight requests for CORS
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Parse request to extract recaptcha token
    const { token } = await req.json();
    if (!token) {
      // Return error if token is missing
      return new Response(JSON.stringify({ success: false, error: "Missing token" }), { status: 400, headers: corsHeaders });
    }

    // Get the secret from Supabase secrets as configured in project
    const secret = Deno.env.get("RECAPTCHA_SECRET_KEY");
    if (!secret) {
      // Notify if the secret is not configured
      return new Response(JSON.stringify({ success: false, error: "Secret key not configured (RECAPTCHA_SECRET_KEY)" }), { status: 500, headers: corsHeaders });
    }

    // Call Google reCAPTCHA API for server-side verification
    const verifyRes = await fetch(
      `https://www.google.com/recaptcha/api/siteverify`,
      {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: `secret=${secret}&response=${token}`,
      }
    );

    const verifyData = await verifyRes.json();

    if (!verifyData.success) {
      // Return failure if token is invalid
      return new Response(JSON.stringify({ success: false, error: "Invalid reCAPTCHA", details: verifyData }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // Return success for valid token
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: corsHeaders,
    });
  } catch (error) {
    // Log error and return general error message
    console.error("verify-recaptcha error", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
});

// This Edge Function expects to find your secret in project settings as RECAPTCHA_SECRET_KEY
