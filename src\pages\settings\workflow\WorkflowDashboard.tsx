
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  ShoppingCart, 
  MessageCircle, 
  Package, 
  Truck, 
  AlertTriangle, 
  Users 
} from 'lucide-react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const WorkflowDashboard = ({ store }: { store: any }) => {
  const navigate = useNavigate();
  
  const activeWorkflows = [
    {
      id: 'wf1',
      name: 'Order Confirmation',
      type: 'WhatsApp',
      lastTriggered: '2 hours ago',
      status: 'active'
    },
    {
      id: 'wf2',
      name: 'Shipping Notification',
      type: 'WhatsApp',
      lastTriggered: '1 day ago',
      status: 'active'
    },
    {
      id: 'wf3',
      name: 'Delivery Confirmation',
      type: 'WhatsApp',
      lastTriggered: '3 days ago',
      status: 'paused'
    }
  ];

  const analyticsData = {
    totalWorkflows: 8,
    activeWorkflows: 3,
    messagesSent: 124,
    automationsTriggered: 37,
    templates: 5,
    successRate: 98.7
  };

  return (
    <div className="space-y-6">
      <div className="grid gap-6 grid-cols-1 md:grid-cols-3">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Active Workflows</CardTitle>
            <CardDescription>
              Automation status at a glance
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{analyticsData.activeWorkflows}</div>
            <p className="text-muted-foreground text-sm">
              Out of {analyticsData.totalWorkflows} total workflows
            </p>
            
            <div className="mt-4 pt-4 border-t space-y-2">
              {activeWorkflows.map(workflow => (
                <div key={workflow.id} className="flex justify-between items-center text-sm">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${workflow.status === 'active' ? 'bg-green-500' : 'bg-amber-500'}`}></div>
                    <span>{workflow.name}</span>
                  </div>
                  <span className="text-muted-foreground text-xs">{workflow.lastTriggered}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Messages</CardTitle>
            <CardDescription>
              Last 30 days activity
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold mb-2">{analyticsData.messagesSent}</div>
            <div className="text-muted-foreground text-sm mb-4">
              {analyticsData.automationsTriggered} automations triggered
            </div>
            
            <div className="space-y-3 pt-4 border-t">
              <div className="flex justify-between items-center text-sm">
                <span>Success rate</span>
                <span className="font-medium text-green-600">{analyticsData.successRate}%</span>
              </div>
              <div className="flex justify-between items-center text-sm">
                <span>WhatsApp templates</span>
                <span className="font-medium">{analyticsData.templates}</span>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg">Quick Setup</CardTitle>
            <CardDescription>
              Common automation flows to get started
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              <li 
                className="flex items-center gap-2 p-2 rounded border hover:bg-secondary/50 cursor-pointer"
                onClick={() => navigate('/settings/workflow/whatsapp')}
              >
                <MessageCircle className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">Order confirmation</p>
                  <p className="text-sm text-muted-foreground">Send WhatsApp message when order is placed</p>
                </div>
              </li>
              <li 
                className="flex items-center gap-2 p-2 rounded border hover:bg-secondary/50 cursor-pointer"
                onClick={() => navigate('/settings/workflow/orders')}
              >
                <Truck className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">Shipping updates</p>
                  <p className="text-sm text-muted-foreground">Notify when order status changes to shipped</p>
                </div>
              </li>
              <li 
                className="flex items-center gap-2 p-2 rounded border hover:bg-secondary/50 cursor-pointer"
                onClick={() => navigate('/settings/workflow/inventory')}
              >
                <Package className="h-5 w-5 text-primary" />
                <div>
                  <p className="font-medium">Inventory alerts</p>
                  <p className="text-sm text-muted-foreground">Low stock warnings and product notifications</p>
                </div>
              </li>
            </ul>
          </CardContent>
        </Card>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Recent Workflow Activity</CardTitle>
          <CardDescription>
            Latest automated actions performed by your workflows
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3">
              <div className="bg-green-100 p-2 rounded-full">
                <MessageCircle className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="font-medium">Order #1234 WhatsApp Notification</p>
                <p className="text-sm text-muted-foreground">Sent 2 hours ago to +254 712 345 678</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="bg-blue-100 p-2 rounded-full">
                <Truck className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="font-medium">Shipping Update for Order #1230</p>
                <p className="text-sm text-muted-foreground">Sent 5 hours ago to +254 798 765 432</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="bg-amber-100 p-2 rounded-full">
                <AlertTriangle className="h-4 w-4 text-amber-600" />
              </div>
              <div>
                <p className="font-medium">Low Stock Alert for Product #A102</p>
                <p className="text-sm text-muted-foreground">Notification sent to store admin 12 hours ago</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="bg-purple-100 p-2 rounded-full">
                <Users className="h-4 w-4 text-purple-600" />
              </div>
              <div>
                <p className="font-medium">New Customer Welcome Message</p>
                <p className="text-sm text-muted-foreground">Sent yesterday to +254 723 456 789</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowDashboard;
