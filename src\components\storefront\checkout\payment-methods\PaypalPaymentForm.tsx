
import React from 'react';
import { Button } from "@/components/ui/button";
import { Wallet, ArrowRight } from "lucide-react";

interface PaypalPaymentFormProps {
  amount: number;
  onPaymentComplete: (transactionId: string) => void;
}

const PaypalPaymentForm: React.FC<PaypalPaymentFormProps> = ({ amount, onPaymentComplete }) => {
  const handlePaypalCheckout = () => {
    // This would typically redirect to PayPal checkout
    // For demo purposes, we'll just simulate completion
    setTimeout(() => {
      const mockTransactionId = 'PAYPAL-' + Math.floor(Math.random() * 100000000);
      onPaymentComplete(mockTransactionId);
    }, 1000);
  };
  
  return (
    <div className="space-y-4">
      <div className="p-4 bg-muted rounded-md border text-sm mb-4">
        <p>You will be redirected to PayPal to complete your payment securely.</p>
      </div>
      
      <Button 
        className="w-full" 
        onClick={handlePaypalCheckout}
        style={{ backgroundColor: '#0070ba' }}
      >
        <Wallet className="mr-2 h-4 w-4" />
        Continue to PayPal
        <ArrowRight className="ml-2 h-4 w-4" />
      </Button>
      
      <div className="text-center text-xs text-muted-foreground mt-4">
        <p>By clicking above, you will be redirected to PayPal to complete your purchase securely.</p>
      </div>
    </div>
  );
};

export default PaypalPaymentForm;
