
import { supabase } from '@/integrations/supabase/client';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'sonner';

// Function to upload store logo to Supabase storage
export const uploadStoreLogoToStorage = async (file: File, storeId?: string): Promise<string | null> => {
  if (!file) return null;
  
  try {
    // Create a unique file name
    const fileExt = file.name.split('.').pop();
    const fileName = `store-logo/${storeId || 'new-store'}/${uuidv4()}.${fileExt}`;
    
    // Check if storage bucket exists, create if it doesn't
    const { data: buckets } = await supabase.storage.listBuckets();
    const storeBucket = buckets?.find(bucket => bucket.name === 'store-assets');
    
    if (!storeBucket) {
      const { error: bucketError } = await supabase.storage.createBucket('store-assets', {
        public: true
      });
      
      if (bucketError) {
        throw bucketError;
      }
    }
    
    // Upload the file
    const { error } = await supabase.storage
      .from('store-assets')
      .upload(fileName, file);
      
    if (error) throw error;
    
    // Get the public URL for the file
    const { data } = supabase.storage
      .from('store-assets')
      .getPublicUrl(fileName);
      
    return data.publicUrl;
  } catch (error: any) {
    console.error('Error uploading logo:', error);
    toast.error('Failed to upload logo: ' + error.message);
    return null;
  }
};

// Function to delete a file from storage
export const deleteFileFromStorage = async (fileUrl: string): Promise<boolean> => {
  if (!fileUrl) return false;
  
  try {
    // Extract the path from the URL
    const url = new URL(fileUrl);
    const pathWithBucket = url.pathname;
    
    // The pathname includes "/storage/v1/object/public/" and then the bucket name and file path
    // We need to extract just the file path without the bucket name
    const pathSegments = pathWithBucket.split('/');
    const bucketName = pathSegments[5]; // The bucket name is at index 5 in the path
    const filePath = pathSegments.slice(6).join('/'); // Everything after the bucket name is the file path
    
    // Delete the file
    const { error } = await supabase.storage
      .from(bucketName)
      .remove([filePath]);
      
    if (error) throw error;
    
    return true;
  } catch (error: any) {
    console.error('Error deleting file:', error);
    toast.error('Failed to delete file: ' + error.message);
    return false;
  }
};
