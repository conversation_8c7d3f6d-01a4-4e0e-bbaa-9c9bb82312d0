
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container } from '@/components/ui/container';
import { Button } from '@/components/ui/button';
import { 
  ArrowLeft, 
  ShoppingCart, 
  Minus, 
  Plus, 
  Check,
  AlertCircle 
} from 'lucide-react';
import { useProduct, useCart } from '@/contexts';
import { formatCurrency } from '@/utils/formatters';
import { Skeleton } from '@/components/ui/skeleton';

const LightweightProductDetails: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();
  const { getProduct, isLoading } = useProduct();
  const { addToCart } = useCart();
  const navigate = useNavigate();
  
  const [product, setProduct] = useState<any>(null);
  const [quantity, setQuantity] = useState(1);
  const [addedToCart, setAddedToCart] = useState(false);
  const [mainImage, setMainImage] = useState('');
  
  useEffect(() => {
    if (productId) {
      const fetchProduct = async () => {
        const productData = await getProduct(productId);
        if (productData) {
          setProduct(productData);
          if (productData.images && productData.images.length > 0) {
            setMainImage(productData.images[0]);
          }
        }
      };
      
      fetchProduct();
    }
  }, [productId, getProduct]);
  
  const handleQuantityChange = (value: number) => {
    if (value >= 1 && value <= (product?.stock_quantity || 10)) {
      setQuantity(value);
    }
  };
  
  const handleAddToCart = () => {
    if (product) {
      addToCart(product, quantity);
      setAddedToCart(true);
      setTimeout(() => {
        setAddedToCart(false);
      }, 2000);
    }
  };
  
  // Show loading state
  if (isLoading) {
    return (
      <Container className="py-8">
        <Button 
          variant="ghost" 
          className="mb-4"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Image Skeleton */}
          <Skeleton className="aspect-square w-full rounded-md" />
          
          {/* Content Skeletons */}
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4" />
            <Skeleton className="h-6 w-1/3" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-10 w-32" />
            <div className="pt-4">
              <Skeleton className="h-12 w-full" />
            </div>
          </div>
        </div>
      </Container>
    );
  }
  
  // Show error state if product not found
  if (!product) {
    return (
      <Container className="py-8">
        <Button 
          variant="ghost" 
          className="mb-4"
          onClick={() => navigate(-1)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Shop
        </Button>
        
        <div className="text-center py-12 border rounded-md">
          <AlertCircle className="h-12 w-12 mx-auto text-gray-300 mb-4" />
          <h2 className="text-xl font-medium mb-2">Product Not Found</h2>
          <p className="text-gray-500 mb-6">
            The product you're looking for doesn't exist or has been removed.
          </p>
          <Button onClick={() => navigate('/shop')}>
            Browse Products
          </Button>
        </div>
      </Container>
    );
  }
  
  return (
    <Container className="py-8">
      <Button 
        variant="ghost" 
        className="mb-4"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {/* Product Images */}
        <div className="space-y-4">
          {/* Main Image */}
          <div className="aspect-square bg-gray-100 rounded-md overflow-hidden">
            {mainImage ? (
              <img 
                src={mainImage} 
                alt={product.name} 
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center text-gray-400">
                No Image Available
              </div>
            )}
          </div>
          
          {/* Thumbnail Images - only if there are multiple images */}
          {product.images && product.images.length > 1 && (
            <div className="flex gap-2 overflow-x-auto pb-2">
              {product.images.map((image: string, index: number) => (
                <div 
                  key={index}
                  className={`w-16 h-16 flex-shrink-0 rounded-md overflow-hidden border-2 cursor-pointer 
                    ${mainImage === image ? 'border-primary' : 'border-transparent'}`}
                  onClick={() => setMainImage(image)}
                >
                  <img 
                    src={image} 
                    alt={`${product.name} - view ${index + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* Product Info */}
        <div className="space-y-4">
          <h1 className="text-2xl font-bold">{product.name}</h1>
          
          <p className="text-xl font-semibold text-primary">
            {formatCurrency(product.price, product.currency)}
          </p>
          
          {product.description && (
            <p className="text-gray-600">{product.description}</p>
          )}
          
          {/* Product Meta */}
          <div className="space-y-2 text-sm text-gray-500">
            {product.sku && (
              <p>SKU: {product.sku}</p>
            )}
            
            {product.category && (
              <p>Category: {product.category}</p>
            )}
            
            <p className={product.stock_quantity > 5 ? 'text-green-600' : 'text-amber-600'}>
              {product.stock_quantity > 0 
                ? `In Stock (${product.stock_quantity} available)` 
                : 'Out of Stock'}
            </p>
          </div>
          
          {/* Quantity Selector */}
          <div className="pt-4">
            <p className="text-sm font-medium mb-2">Quantity</p>
            <div className="flex items-center">
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 rounded-r-none"
                onClick={() => handleQuantityChange(quantity - 1)}
                disabled={quantity <= 1}
              >
                <Minus className="h-4 w-4" />
              </Button>
              <div className="h-10 w-14 flex items-center justify-center border-y border-input">
                {quantity}
              </div>
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 rounded-l-none"
                onClick={() => handleQuantityChange(quantity + 1)}
                disabled={quantity >= product.stock_quantity}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Add to Cart Button */}
          <Button 
            className="w-full mt-4"
            onClick={handleAddToCart}
            disabled={product.stock_quantity <= 0 || addedToCart}
          >
            {addedToCart ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                Added to Cart
              </>
            ) : (
              <>
                <ShoppingCart className="h-4 w-4 mr-2" />
                Add to Cart
              </>
            )}
          </Button>
        </div>
      </div>
    </Container>
  );
};

export default LightweightProductDetails;
