
import React, { useState, useEffect } from 'react';
import { planCardData } from '@/components/settings/plans/plansData';
import PlanCard from '@/components/settings/plans/PlanCard';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Calendar, Clock } from 'lucide-react';
import { loadPabblyScript } from '@/utils/pabblyCheckout';
import { useToast } from '@/hooks/use-toast';

const Plans = () => {
  const [isYearly, setIsYearly] = useState(false);
  const [pabblyLoaded, setPabblyLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    // Load Pabbly script and handle any errors
    const loadScript = async () => {
      try {
        await loadPabblyScript();
        setPabblyLoaded(true);
      } catch (error) {
        console.error('Failed to load Pabbly checkout:', error);
        toast({
          title: "Payment system initialization failed",
          description: "We're having trouble loading our payment system. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadScript();
  }, [toast]);

  const togglePricing = (value: string) => {
    setIsYearly(value === 'yearly');
  };

  // Sort plans to ensure Basic, Premium, Business order
  const planOrder = { "Basic": 0, "Premium": 1, "Business": 2 };
  const sortedPlanCardData = [...planCardData].sort((a, b) => {
    return planOrder[a.title as keyof typeof planOrder] - planOrder[b.title as keyof typeof planOrder];
  });

  return (
    <div className="max-w-6xl">
      <div className="mb-8">
        <p className="text-lg text-muted-foreground mb-6">
          Choose the plan that's right for your business. All plans come with a 14-day free trial.
        </p>

        <div className="flex justify-start mb-8">
          <div className="bg-muted p-1 rounded-lg inline-flex items-center">
            <ToggleGroup type="single" value={isYearly ? 'yearly' : 'monthly'} onValueChange={togglePricing}>
              <ToggleGroupItem 
                value="monthly" 
                className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white"
              >
                <Clock className="h-3.5 w-3.5" />
                Monthly
              </ToggleGroupItem>
              <ToggleGroupItem 
                value="yearly" 
                className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white"
              >
                <Calendar className="h-3.5 w-3.5" />
                Yearly
              </ToggleGroupItem>
            </ToggleGroup>
          </div>
        </div>
      </div>

      <div className="grid gap-8 md:grid-cols-1 lg:grid-cols-3">
        {sortedPlanCardData.map((plan, index) => {
          const pabblyPlanId = plan.title === 'Premium' 
            ? '680bad1766b57e9593152756'
            : plan.title === 'Business' 
              ? '680bae0420175f950f72276b'
              : undefined;

          return (
            <PlanCard 
              key={index} 
              {...plan} 
              isYearly={isYearly} 
              pabblyPlanId={pabblyPlanId}
            />
          );
        })}
      </div>
      
      {isLoading && (
        <div className="mt-4 text-center text-sm text-muted-foreground">
          <p>Loading payment system...</p>
        </div>
      )}
      
      {!isLoading && !pabblyLoaded && (
        <div className="mt-4 text-center text-sm text-amber-600">
          <p>Payment system is currently unavailable. You may need to refresh the page or try again later.</p>
        </div>
      )}
    </div>
  );
};

export default Plans;
