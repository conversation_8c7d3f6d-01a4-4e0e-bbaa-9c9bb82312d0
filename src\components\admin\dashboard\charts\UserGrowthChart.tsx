
import React from 'react';
import { Card, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { LineChart } from '@/components/ui/chart';
import { Users } from 'lucide-react';

interface UserGrowthChartProps {
  data: { name: string; users: number }[];
}

const UserGrowthChart: React.FC<UserGrowthChartProps> = ({ data }) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base flex items-center">
          <Users className="h-4 w-4 mr-2 text-blue-500" />
          User Growth
        </CardTitle>
        <CardDescription>New users per month</CardDescription>
      </CardHeader>
      <CardContent>
        <LineChart 
          data={data}
          index="name"
          categories={["users"]}
          colors={["blue"]}
          valueFormatter={(value) => `${value} users`}
          className="h-[300px]"
        />
      </CardContent>
    </Card>
  );
};

export default UserGrowthChart;
