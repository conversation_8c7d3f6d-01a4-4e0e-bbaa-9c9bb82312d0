import React, { createContext, useContext, ReactNode } from 'react';
import { AuthContextType } from './types';
import { useAuthProvider } from './hooks/useAuthProvider';

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

// DEVELOPMENT ONLY: Mock auth for testing
const MOCK_AUTH_FOR_DEV = false;

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const authValues = useAuthProvider();

  // Development mode: provide mock auth data
  if (MOCK_AUTH_FOR_DEV) {
    const mockAuthValues: AuthContextType = {
      ...authValues,
      isAuthenticated: true,
      isLoading: false,
      user: {
        id: 'dev-user-123',
        email: '<EMAIL>',
        role: 'StoreOwner', // Can be changed to 'Admin' or 'Customer' as needed
        name: 'Mock Store Owner',
        avatar_url: null,
      },

    };
    
    return (
      <AuthContext.Provider value={mockAuthValues}>
        {children}
      </AuthContext.Provider>
    );
  }

  return (
    <AuthContext.Provider value={authValues}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
