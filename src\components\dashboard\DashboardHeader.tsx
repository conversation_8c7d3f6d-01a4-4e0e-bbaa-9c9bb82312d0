
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Bell, Settings, LogOut } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth, useStore } from '@/contexts';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

const DashboardHeader: React.FC = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { currentStore } = useStore();
  
  const handleSignOut = async () => {
    await logout();
    navigate('/signin');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  // Get user display name from either name property or email
  const userDisplayName = user?.name || 'User';
  
  // Get avatar initials based on name or email
  const userInitials = user?.email ? getInitials(user.name || user.email) : 'U';

  return (
    <header className="border-b sticky top-0 z-30 bg-background">
      <div className="flex h-16 items-center px-4 md:px-6">
        <div className="flex items-center gap-2">
          {/* Logo/Brand */}
          <h2 className="text-xl font-bold hidden md:flex">
            m-duka
          </h2>
        </div>

        <div className="ml-auto flex items-center gap-4">
          {/* Notification bell */}
          <Button variant="ghost" size="icon" className="rounded-full" asChild>
            <div className="relative">
              <Bell className="h-5 w-5" />
              <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-primary"></span>
            </div>
          </Button>

          {/* User dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="ghost" 
                className="rounded-full h-8 w-8 p-0 overflow-hidden"
              >
                <Avatar>
                  <AvatarImage src="" />
                  <AvatarFallback>
                    {userInitials}
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <div className="px-2 py-1.5 text-sm">
                <div className="font-medium">{userDisplayName}</div>
                <div className="text-xs text-muted-foreground truncate max-w-[200px]">
                  {user?.email}
                </div>
              </div>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => navigate('/profile')}>
                <Settings className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => navigate('/settings')}>
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;
