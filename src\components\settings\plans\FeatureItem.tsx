
import React from 'react';
import { Check, X, AlertCircle, HelpCircle } from 'lucide-react';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface FeatureItemProps {
  children: React.ReactNode;
  included?: boolean;
  limited?: boolean;
  info?: string;
}

const FeatureItem = ({ 
  children, 
  included = true, 
  limited = false,
  info
}: FeatureItemProps) => (
  <li className="flex items-start gap-2 text-sm py-1">
    <Check className="h-4 w-4 text-green-500 flex-shrink-0 mt-0.5" />
    
    <span className={!included && !limited ? "text-muted-foreground" : ""}>
      {children}
    </span>
    
    {info && (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <HelpCircle className="h-3 w-3 text-muted-foreground cursor-help" />
          </TooltipTrigger>
          <TooltipContent>
            <p className="max-w-xs text-xs">{info}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    )}
  </li>
);

export default FeatureItem;
