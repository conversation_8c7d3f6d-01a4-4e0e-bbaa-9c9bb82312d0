import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { ProductFilters as IProductFilters } from '@/contexts/product/types';
import { formatCurrency } from '@/utils/formatters';

interface ProductFiltersProps {
  filters: IProductFilters;
  categories: string[];
  onFiltersChange: (filters: IProductFilters) => void;
  onClearFilters: () => void;
  minPrice?: number;
  maxPrice?: number;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  categories,
  onFiltersChange,
  onClearFilters,
  minPrice = 0,
  maxPrice = 10000
}) => {
  const [priceRange, setPriceRange] = useState<[number, number]>([
    filters.minPrice || minPrice,
    filters.maxPrice || maxPrice
  ]);

  const handleCategoryChange = (category: string) => {
    onFiltersChange({
      ...filters,
      category: category === 'all' ? undefined : category
    });
  };

  const handlePriceChange = (values: number[]) => {
    setPriceRange([values[0], values[1]]);
    onFiltersChange({
      ...filters,
      minPrice: values[0],
      maxPrice: values[1]
    });
  };

  const handleInStockChange = (checked: boolean) => {
    onFiltersChange({
      ...filters,
      inStock: checked ? true : undefined
    });
  };

  const hasActiveFilters = Boolean(
    filters.category || 
    filters.minPrice !== undefined || 
    filters.maxPrice !== undefined || 
    filters.inStock
  );

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Filters</CardTitle>
          {hasActiveFilters && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={onClearFilters}
              className="text-xs"
            >
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Category Filter */}
        {categories.length > 0 && (
          <div className="space-y-2">
            <Label className="text-sm font-medium">Category</Label>
            <Select 
              value={filters.category || 'all'} 
              onValueChange={handleCategoryChange}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="All categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {/* Price Range Filter */}
        <div className="space-y-3">
          <Label className="text-sm font-medium">Price Range</Label>
          <div className="px-2">
            <Slider
              value={priceRange}
              onValueChange={handlePriceChange}
              max={maxPrice}
              min={minPrice}
              step={50}
              className="w-full"
            />
          </div>
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{formatCurrency(priceRange[0], 'KES')}</span>
            <span>{formatCurrency(priceRange[1], 'KES')}</span>
          </div>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Label htmlFor="minPrice" className="text-xs text-gray-500">Min</Label>
              <Input
                id="minPrice"
                type="number"
                value={priceRange[0]}
                onChange={(e) => {
                  const value = Number(e.target.value);
                  const newRange: [number, number] = [value, priceRange[1]];
                  setPriceRange(newRange);
                  handlePriceChange(newRange);
                }}
                className="text-sm"
                min={minPrice}
                max={priceRange[1]}
              />
            </div>
            <div>
              <Label htmlFor="maxPrice" className="text-xs text-gray-500">Max</Label>
              <Input
                id="maxPrice"
                type="number"
                value={priceRange[1]}
                onChange={(e) => {
                  const value = Number(e.target.value);
                  const newRange: [number, number] = [priceRange[0], value];
                  setPriceRange(newRange);
                  handlePriceChange(newRange);
                }}
                className="text-sm"
                min={priceRange[0]}
                max={maxPrice}
              />
            </div>
          </div>
        </div>

        {/* Availability Filter */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Availability</Label>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="inStock"
              checked={filters.inStock || false}
              onCheckedChange={handleInStockChange}
            />
            <Label htmlFor="inStock" className="text-sm">In stock only</Label>
          </div>
        </div>

        {/* Summary */}
        {hasActiveFilters && (
          <div className="pt-3 border-t">
            <Label className="text-sm font-medium text-gray-600">Active Filters:</Label>
            <div className="mt-2 space-y-1">
              {filters.category && (
                <div className="flex items-center justify-between text-xs">
                  <span>Category: {filters.category}</span>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-auto p-0 text-xs text-red-600"
                    onClick={() => handleCategoryChange('all')}
                  >
                    ✕
                  </Button>
                </div>
              )}
              {(filters.minPrice !== undefined || filters.maxPrice !== undefined) && (
                <div className="flex items-center justify-between text-xs">
                  <span>
                    Price: {formatCurrency(filters.minPrice || minPrice, 'KES')} - {formatCurrency(filters.maxPrice || maxPrice, 'KES')}
                  </span>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-auto p-0 text-xs text-red-600"
                    onClick={() => onFiltersChange({ ...filters, minPrice: undefined, maxPrice: undefined })}
                  >
                    ✕
                  </Button>
                </div>
              )}
              {filters.inStock && (
                <div className="flex items-center justify-between text-xs">
                  <span>In stock only</span>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-auto p-0 text-xs text-red-600"
                    onClick={() => handleInStockChange(false)}
                  >
                    ✕
                  </Button>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ProductFilters;
