
import React from "react";
import { useNavigate } from "react-router-dom";
import { LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { useAuth } from "@/contexts";

const UserProfileSection = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    setIsLoggingOut(true);
    try {
      const toastId = toast.loading("Logging out...");
      await logout();
      toast.dismiss(toastId);
      toast.success("Logged out successfully");
      navigate("/");
    } catch (error) {
      console.error("Error logging out:", error);
      toast.error("Error logging out. Please try again.");
    } finally {
      setIsLoggingOut(false);
    }
  };

  return (
    <div className="mt-auto pt-4 border-t">
      <div className="px-4 py-2 mb-4">
        <p className="text-sm text-gray-500">Signed in as:</p>
        <p className="font-medium">{user?.name}</p>
        <p className="text-sm text-gray-500 truncate">{user?.email}</p>
      </div>
      
      <Button 
        variant="ghost" 
        className="w-full justify-start text-red-500 hover:text-red-600 hover:bg-red-50"
        onClick={handleLogout}
        disabled={isLoggingOut}
      >
        <LogOut className="w-5 h-5 mr-2" />
        Sign Out
      </Button>
    </div>
  );
};

export default UserProfileSection;
