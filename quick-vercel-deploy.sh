#!/bin/bash

# Quick Vercel deployment script for M-Duka app

set -e

echo "🚀 M-Duka App - Quick Vercel Deploy"
echo "=================================="

# Check if logged in
echo "📝 Checking Vercel authentication..."
if ! vercel whoami >/dev/null 2>&1; then
    echo "❌ Not logged in to Vercel. Please run:"
    echo "   vercel login"
    exit 1
fi

echo "✅ Logged in to Vercel"

# Check if environment variables are set
echo "🔧 Checking environment variables..."
ENV_CHECK=$(vercel env ls 2>/dev/null || echo "")
if [[ ! $ENV_CHECK == *"VITE_PUBLIC_SUPABASE_URL"* ]]; then
    echo "⚠️  Environment variables not set. Running setup script first..."
    ./vercel-env-setup.sh
fi

# Build the project
echo "🔨 Building the project..."
npm run build

# Link or deploy
echo "🌐 Deploying to Vercel..."
if [ ! -f ".vercel/project.json" ]; then
    echo "🔗 Linking project to Vercel..."
    vercel link --confirm
fi

# Deploy to production
echo "🚀 Deploying to production..."
vercel --prod

echo "✅ Deployment complete!"
echo "🌐 Your app should be available at the URL shown above"
echo ""
echo "💡 If you see a blank page:"
echo "   1. Check that environment variables are set: vercel env ls"
echo "   2. Check Supabase Auth URL configuration"