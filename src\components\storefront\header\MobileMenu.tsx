
import React from "react";
import { Link } from "react-router-dom";
import SearchForm from "./SearchForm";
import CartButton from "./CartButton";
import UserMenuButton from "./UserMenuButton";

interface MobileMenuProps {
  isOpen: boolean;
  onItemClick: () => void;
}

const MobileMenu: React.FC<MobileMenuProps> = ({ isOpen, onItemClick }) => {
  if (!isOpen) return null;

  return (
    <div className="mt-4 md:hidden">
      <nav className="py-2">
        <ul className="space-y-2">
          <li>
            <Link 
              to="/shop" 
              className="block py-2 hover:text-primary"
              onClick={onItemClick}
            >
              Home
            </Link>
          </li>
          <li>
            <Link 
              to="/shop/categories" 
              className="block py-2 hover:text-primary"
              onClick={onItemClick}
            >
              Categories
            </Link>
          </li>
          <li>
            <Link 
              to="/shop/products" 
              className="block py-2 hover:text-primary"
              onClick={onItemClick}
            >
              All Products
            </Link>
          </li>
          <li>
            <Link 
              to="/shop/search?sort=newest" 
              className="block py-2 hover:text-primary"
              onClick={onItemClick}
            >
              New Arrivals
            </Link>
          </li>
        </ul>
      </nav>
      
      <SearchForm className="mt-2" />
      
      <div className="flex items-center justify-between mt-4">
        <CartButton variant="full" onClick={onItemClick} />
        <UserMenuButton variant="full" onClick={onItemClick} />
      </div>
    </div>
  );
};

export default MobileMenu;
