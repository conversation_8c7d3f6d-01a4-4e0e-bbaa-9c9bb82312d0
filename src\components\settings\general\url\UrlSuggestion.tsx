
import React from 'react';

interface UrlSuggestionProps {
  urlSuggestion: string | null;
  onUseSuggestion: () => void;
}

const UrlSuggestion = ({ urlSuggestion, onUseSuggestion }: UrlSuggestionProps) => {
  if (!urlSuggestion) return null;
  
  return (
    <div className="mt-2 text-sm text-red-500">
      This URL is already taken. 
      <button 
        type="button"
        onClick={onUseSuggestion}
        className="ml-2 text-blue-500 hover:underline"
      >
        Use "m-duka.app/{urlSuggestion}" instead?
      </button>
    </div>
  );
};

export default UrlSuggestion;
