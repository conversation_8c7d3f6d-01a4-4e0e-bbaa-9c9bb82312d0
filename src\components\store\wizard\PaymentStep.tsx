
import React from 'react';
import { StoreFormData } from '@/types/store';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { CreditCard, Wallet, CreditCardIcon } from 'lucide-react';

interface PaymentStepProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

const paymentMethods = [
  { 
    id: 'credit-card', 
    name: 'Credit Card', 
    description: 'Accept Visa, Mastercard, Amex and other major cards',
    icon: CreditCard
  },
  { 
    id: 'paypal', 
    name: 'PayPal', 
    description: 'Allow customers to pay using their PayPal account',
    icon: Wallet
  },
  { 
    id: 'mobile-money', 
    name: 'Mobile Money', 
    description: 'Accept payments via M-Pesa, Airtel Money and other mobile wallets',
    icon: CreditCardIcon
  },
];

const PaymentStep: React.FC<PaymentStepProps> = ({ data, updateData }) => {
  const [selectedMethods, setSelectedMethods] = React.useState<string[]>(
    data.paymentMethods || ['credit-card']
  );

  const togglePaymentMethod = (methodId: string) => {
    let newMethods;
    if (selectedMethods.includes(methodId)) {
      // Don't allow removing the last payment method
      if (selectedMethods.length === 1) {
        return;
      }
      newMethods = selectedMethods.filter(id => id !== methodId);
    } else {
      newMethods = [...selectedMethods, methodId];
    }
    
    setSelectedMethods(newMethods);
    updateData({ paymentMethods: newMethods });
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Payment Methods</h2>
      <p className="text-muted-foreground mb-6">
        Select the payment methods you want to accept in your store.
      </p>

      <div className="space-y-4">
        {paymentMethods.map((method) => {
          const isSelected = selectedMethods.includes(method.id);
          const PaymentIcon = method.icon;
          
          return (
            <Card 
              key={method.id}
              className={isSelected ? "border-primary" : ""}
            >
              <CardHeader className="p-4 pb-2 flex flex-row items-center justify-between space-y-0">
                <div className="flex items-center">
                  <PaymentIcon className="h-5 w-5 mr-2" />
                  <CardTitle className="text-base">{method.name}</CardTitle>
                </div>
                <Switch 
                  checked={isSelected}
                  onCheckedChange={() => togglePaymentMethod(method.id)}
                />
              </CardHeader>
              <CardContent className="p-4 pt-1">
                <CardDescription>{method.description}</CardDescription>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="mt-8 bg-blue-50 p-4 rounded-md">
        <h3 className="text-sm font-semibold text-blue-700 mb-2">Note about payment methods</h3>
        <p className="text-sm text-blue-600">
          You'll be able to configure each payment method in detail after creating your store.
          For now, we're just collecting which payment options you want to offer.
        </p>
      </div>
    </div>
  );
};

export default PaymentStep;
