
import { motion } from "framer-motion";
import { MessageSquare } from "lucide-react";
import PhoneFrame from "./phone-mockups/PhoneFrame";
import StoreHeader from "./phone-mockups/StoreHeader";
import StoreAbout from "./phone-mockups/StoreAbout";
import ProductCard from "./phone-mockups/ProductCard";
import OrderHeader from "./phone-mockups/OrderHeader";
import CustomerDetails from "./phone-mockups/CustomerDetails";
import OrderSummary from "./phone-mockups/OrderSummary";
import PaymentInfo from "./phone-mockups/PaymentInfo";

const PhoneMockups = () => {
  const products = [
    {name: "Fresh Avocados", price: "TZS 5,000", image: "bg-green-100", emoji: "🥑"},
    {name: "Organic Bananas", price: "TZS 3,000", image: "bg-yellow-100", emoji: "🍌"},
    {name: "Farm Eggs (Dozen)", price: "TZS 6,500", image: "bg-amber-50", emoji: "🥚"}
  ];

  const orderItems = [
    {name: "Fresh Avocados", qty: 2, price: "TZS 10,000"},
    {name: "Organic Bananas", qty: 1, price: "TZS 3,000"},
    {name: "Farm Eggs (Dozen)", qty: 1, price: "TZS 6,500"}
  ];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.6, delay: 0.3 }}
      className="relative order-first lg:order-last"
    >
      <div className="relative flex justify-center items-center">
        {/* Phone 1 - Store Interface */}
        <PhoneFrame
          initial={{ x: -20, rotate: -6 }}
          animate={{ x: 0 }}
          className="absolute left-0 top-0 md:left-10 transform -rotate-6"
        >
          <StoreHeader />
          <StoreAbout />
          <div className="p-4">
            <div className="text-sm font-medium mb-3 flex justify-between items-center">
              <span>Featured Products</span>
              <span className="text-xs text-green-600">See All</span>
            </div>
            <div className="space-y-3">
              {products.map((product, index) => (
                <ProductCard key={index} {...product} index={index} />
              ))}
            </div>
            
            {/* WhatsApp Button */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.8 }}
              className="mt-4 bg-green-500 text-white p-2 rounded-lg flex items-center justify-center gap-2 shadow-md"
            >
              <MessageSquare className="h-4 w-4" />
              <span className="text-xs font-medium">Order via WhatsApp</span>
            </motion.div>
          </div>
        </PhoneFrame>
        
        {/* Phone 2 - Delivery Note Interface */}
        <PhoneFrame
          initial={{ x: 20, rotate: 6 }}
          animate={{ x: 0 }}
          className="absolute right-0 top-10 md:right-10 transform rotate-6"
        >
          <OrderHeader orderNumber="A1234" />
          <CustomerDetails />
          <OrderSummary items={orderItems} />
          <PaymentInfo />
        </PhoneFrame>
        
        {/* Decorative Elements */}
        <div className="hidden md:block absolute -bottom-10 -right-20 w-40 h-40 bg-green-200/30 rounded-full blur-2xl" />
        <div className="hidden md:block absolute -top-10 -left-20 w-40 h-40 bg-blue-200/30 rounded-full blur-2xl" />
        
        {/* Floating Elements */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 1, repeat: Infinity, repeatType: "reverse", repeatDelay: 5 }}
          className="absolute -bottom-16 left-1/2 transform -translate-x-1/2 bg-white rounded-xl shadow-lg p-3 z-30"
        >
          <div className="flex items-center gap-3">
            <div className="bg-green-100 rounded-full p-2">
              <MessageSquare className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-xs">
              <p className="font-medium">New Order via WhatsApp!</p>
              <p className="text-gray-500">John just ordered 3 items</p>
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default PhoneMockups;
