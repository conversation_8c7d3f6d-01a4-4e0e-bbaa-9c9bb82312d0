
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/utils/formatters';
import { Product } from '@/types/unified-product';

interface ProductInfoProps {
  product: Product;
}

const ProductInfo: React.FC<ProductInfoProps> = ({ product }) => {
  return (
    <div className="space-y-4">
      <div>
        <h1 className="text-3xl font-bold mb-2">{product.name}</h1>
        <p className="text-2xl font-semibold">
          {formatCurrency(product.price || 0, product.currency || 'USD')}
        </p>
      </div>
      
      {product.stock_quantity === 0 ? (
        <Badge variant="outline" className="text-red-500 border-red-200 bg-red-50">
          Out of Stock
        </Badge>
      ) : (
        <Badge variant="outline" className="text-green-500 border-green-200 bg-green-50">
          In Stock ({product.stock_quantity} available)
        </Badge>
      )}
      
      {product.description && (
        <div>
          <h3 className="text-lg font-medium mb-2">Description</h3>
          <p className="text-muted-foreground">{product.description}</p>
        </div>
      )}
    </div>
  );
};

export default ProductInfo;
