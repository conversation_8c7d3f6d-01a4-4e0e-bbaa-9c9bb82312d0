
import { supabase } from '@/integrations/supabase/client';
import { UserRole } from '@/constants/roles';

/**
 * Invite a user to join the application
 */
export const inviteUser = async (email: string, role: UserRole = UserRole.StoreOwner): Promise<{ success: boolean; error?: any }> => {
  try {
    const currentDomain = window.location.origin;
    console.log("Inviting user with email and role:", email, role);
    
    // Using signInWithOtp for invitation
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: `${currentDomain}/signup?invited=true&email=${encodeURIComponent(email)}&role=${role}`,
        data: {
          role,
        },
      },
    });
    
    if (error) {
      console.error("Invite error:", error.message);
      return { success: false, error };
    }
    
    console.log("Invitation sent successfully:", data);
    return { success: true };
  } catch (error: any) {
    console.error('Invitation error:', error);
    return { success: false, error };
  }
};
