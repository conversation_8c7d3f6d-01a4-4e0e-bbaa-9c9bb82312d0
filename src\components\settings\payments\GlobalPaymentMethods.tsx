
import React from 'react';
import PaymentMethodSection, { PaymentMethod } from './PaymentMethodSection';
import { CreditCard, Wallet, Globe } from 'lucide-react';

interface GlobalPaymentMethodsProps {
  onToggleMethod: (methodId: string, enabled: boolean) => void;
  isMethodEnabled: (methodId: string) => boolean;
}

const GlobalPaymentMethods: React.FC<GlobalPaymentMethodsProps> = ({
  onToggleMethod,
  isMethodEnabled
}) => {
  const paymentMethods: PaymentMethod[] = [
    {
      id: 'stripe',
      name: 'Stripe',
      description: 'Accept credit cards, Apple Pay, Google Pay and other global payment methods.',
      icon: <CreditCard className="h-5 w-5 text-purple-600" />,
      popular: true,
      fields: [
        {
          id: 'publishable-key',
          name: 'Publishable Key',
          placeholder: 'pk_test_...',
          type: 'text',
          required: true,
          tooltip: 'Your Stripe publishable key starts with pk_test_ or pk_live_'
        },
        {
          id: 'secret-key',
          name: 'Secret Key',
          placeholder: 'sk_test_...',
          type: 'password',
          required: true,
          tooltip: 'Your Stripe secret key starts with sk_test_ or sk_live_'
        }
      ],
      setupUrl: 'https://dashboard.stripe.com/register',
      testMode: true
    },
    {
      id: 'paypal',
      name: 'PayPal',
      description: 'Accept PayPal payments from customers worldwide.',
      icon: <Wallet className="h-5 w-5 text-blue-600" />,
      fields: [
        {
          id: 'client-id',
          name: 'Client ID',
          placeholder: 'Enter your PayPal client ID',
          type: 'text',
          required: true
        },
        {
          id: 'client-secret',
          name: 'Client Secret',
          placeholder: 'Enter your PayPal client secret',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://developer.paypal.com/dashboard/applications/live'
    },
    {
      id: 'pesapal',
      name: 'Pesapal',
      description: 'Integrated online payment gateway for Africa.',
      icon: <Globe className="h-5 w-5 text-green-600" />,
      fields: [
        {
          id: 'consumer-key',
          name: 'Consumer Key',
          placeholder: 'Enter your Pesapal consumer key',
          type: 'text',
          required: true
        },
        {
          id: 'consumer-secret',
          name: 'Consumer Secret',
          placeholder: 'Enter your Pesapal consumer secret',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://www.pesapal.com/sign-up'
    }
  ];

  return (
    <div className="space-y-5">
      {paymentMethods.map(method => (
        <PaymentMethodSection
          key={method.id}
          method={method}
          enabled={isMethodEnabled(method.id)}
          onToggle={onToggleMethod}
        />
      ))}
    </div>
  );
};

export default GlobalPaymentMethods;
