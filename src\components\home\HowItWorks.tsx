
import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Smartphone, Settings, Share2 } from "lucide-react";
import { Container } from "@/components/ui/container";

const HowItWorks = () => {
  const steps = [
    {
      icon: <Smartphone className="h-12 w-12 text-white" />,
      title: "Sign Up & Connect",
      description: "Create your account and connect your WhatsApp number in just a few clicks."
    },
    {
      icon: <Settings className="h-12 w-12 text-white" />,
      title: "Add Products & Customize",
      description: "Upload your products and customize your store with your branding."
    },
    {
      icon: <Share2 className="h-12 w-12 text-white" />,
      title: "Share & Start Selling",
      description: "Share your store link with customers and start receiving orders."
    }
  ];

  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  
  return (
    <section id="how-it-works" className="py-20" ref={sectionRef}>
      <Container>
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            Get started in just three simple steps
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 relative">
          {/* Connecting Line (Desktop Only) */}
          <div className="hidden md:block absolute top-28 left-[calc(16.67%+32px)] right-[calc(16.67%+32px)] h-1 bg-green-200 z-0">
            <div className="absolute left-0 top-1/2 w-3 h-3 bg-green-500 rounded-full -translate-y-1/2"></div>
            <div className="absolute left-1/2 top-1/2 w-3 h-3 bg-green-500 rounded-full -translate-y-1/2 -translate-x-1/2"></div>
            <div className="absolute right-0 top-1/2 w-3 h-3 bg-green-500 rounded-full -translate-y-1/2"></div>
          </div>
          
          {steps.map((step, index) => {
            const [ref, inView] = useInView({
              triggerOnce: true,
              threshold: 0.1
            });
            
            return (
              <motion.div
                key={index}
                ref={ref}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="flex flex-col items-center text-center z-10"
              >
                <div className="bg-gradient-to-br from-green-600 to-green-700 w-24 h-24 rounded-full flex items-center justify-center mb-6 shadow-md">
                  {step.icon}
                  <div className="absolute -top-3 -right-3 w-10 h-10 rounded-full bg-white shadow flex items-center justify-center text-lg font-bold text-green-600">
                    {index + 1}
                  </div>
                </div>
                <h3 className="text-xl font-bold mb-3">{step.title}</h3>
                <p className="text-muted-foreground max-w-xs mx-auto">{step.description}</p>
              </motion.div>
            );
          })}
        </div>
        
        <div className="text-center mt-12">
          <Button 
            size="lg"
            className="bg-green-600 hover:bg-green-700 text-white rounded-full h-12 px-8 text-base font-medium shadow-md hover:shadow-lg transition-all group"
            asChild
          >
            <a href="#benefits">
              Explore All Features
              <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
            </a>
          </Button>
        </div>
      </Container>
    </section>
  );
};

export default HowItWorks;
