
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Building2, Mail, MapPin, Phone, Users } from 'lucide-react';

interface OrganizationSettingsContentProps {
  companyName: string;
  setCompanyName: (value: string) => void;
  businessType: string;
  setBusinessType: (value: string) => void;
  companyEmail: string;
  setCompanyEmail: (value: string) => void;
  companyPhone: string;
  setCompanyPhone: (value: string) => void;
  companyAddress: string;
  setCompanyAddress: (value: string) => void;
  registrationNumber: string;
  setRegistrationNumber: (value: string) => void;
  taxIdentificationNumber: string;
  setTaxIdentificationNumber: (value: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
}

const OrganizationSettingsContent: React.FC<OrganizationSettingsContentProps> = ({
  companyName,
  setCompanyName,
  businessType,
  setBusinessType,
  companyEmail,
  setCompanyEmail,
  companyPhone,
  setCompanyPhone,
  companyAddress,
  setCompanyAddress,
  registrationNumber,
  setRegistrationNumber,
  taxIdentificationNumber,
  setTaxIdentificationNumber,
  loading,
  onSubmit
}) => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Information
          </CardTitle>
          <CardDescription>
            Update your organization's business information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit}>
            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="company-name">Company Name</Label>
                <Input
                  id="company-name"
                  placeholder="Enter your company name"
                  value={companyName}
                  onChange={(e) => setCompanyName(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="business-type">Business Type</Label>
                <Input
                  id="business-type"
                  placeholder="e.g. LLC, Corporation, Sole Proprietorship"
                  value={businessType}
                  onChange={(e) => setBusinessType(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="company-email">Email Address</Label>
                <Input
                  id="company-email"
                  type="email"
                  placeholder="Enter your company email"
                  value={companyEmail}
                  onChange={(e) => setCompanyEmail(e.target.value)}
                  required
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="company-phone">Phone Number</Label>
                <Input
                  id="company-phone"
                  placeholder="Enter your company phone number"
                  value={companyPhone}
                  onChange={(e) => setCompanyPhone(e.target.value)}
                />
              </div>
              
              <div className="space-y-2 sm:col-span-2">
                <Label htmlFor="company-address">Business Address</Label>
                <Textarea
                  id="company-address"
                  placeholder="Enter your business address"
                  rows={3}
                  value={companyAddress}
                  onChange={(e) => setCompanyAddress(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="registration-number">Registration Number</Label>
                <Input
                  id="registration-number"
                  placeholder="Enter business registration number"
                  value={registrationNumber}
                  onChange={(e) => setRegistrationNumber(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tax-id">Tax Identification Number</Label>
                <Input
                  id="tax-id"
                  placeholder="Enter tax identification number"
                  value={taxIdentificationNumber}
                  onChange={(e) => setTaxIdentificationNumber(e.target.value)}
                />
              </div>
            </div>
            
            <div className="mt-6">
              <Button type="submit" disabled={loading}>
                {loading ? 'Saving...' : 'Save Organization Information'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Members
          </CardTitle>
          <CardDescription>
            Manage team members and their access permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <div className="p-4">
              <p className="text-center text-sm text-muted-foreground">
                Team management is available on the Business and higher plans.
              </p>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button variant="outline">
            Upgrade to Business Plan
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default OrganizationSettingsContent;
