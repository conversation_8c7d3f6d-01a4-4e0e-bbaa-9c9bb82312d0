
import React, { createContext, useContext } from 'react';
import { useCartProvider } from './useCartProvider';
import { CartContextType } from './types';

// Create the context with an empty default value
const CartContext = createContext<CartContextType | undefined>(undefined);

// Provider component that wraps the application
export const CartProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const cartState = useCartProvider();
  
  return (
    <CartContext.Provider value={cartState}>
      {children}
    </CartContext.Provider>
  );
};

// Custom hook to use the cart context
export const useCart = (): CartContextType => {
  const context = useContext(CartContext);
  
  if (context === undefined) {
    throw new Error('useCart must be used within a CartProvider');
  }
  
  return context;
};
