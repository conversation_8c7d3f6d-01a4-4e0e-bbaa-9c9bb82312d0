
/**
 * Returns the maximum value of an array by a given iteratee.
 *
 * @param {Array} array - The array to iterate over
 * @param {Function|string} iteratee - The function/property invoked per iteration
 * @returns {*} Returns the maximum value
 */
function maxBy(array, iteratee) {
  if (!array || !array.length) return undefined;
  
  const isIterateeFunction = typeof iteratee === 'function';
  
  let maxValue = array[0];
  let maxComputed = isIterateeFunction 
    ? iteratee(maxValue) 
    : (maxValue != null && maxValue[iteratee] !== undefined ? maxValue[iteratee] : maxValue);
  
  for (let i = 1; i < array.length; i++) {
    const value = array[i];
    const computed = isIterateeFunction 
      ? iteratee(value) 
      : (value != null && value[iteratee] !== undefined ? value[iteratee] : value);
    
    if (computed > maxComputed) {
      maxValue = value;
      maxComputed = computed;
    }
  }
  
  return maxValue;
}

// Support both ESM and CJS
export default maxBy;
