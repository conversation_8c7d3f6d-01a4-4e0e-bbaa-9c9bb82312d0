
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nheycjpozywomwscplcz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  }
});
