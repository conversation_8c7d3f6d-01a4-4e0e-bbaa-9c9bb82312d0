
// Supabase client configuration with environment variable support
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

// Use environment variables with fallback to hardcoded values for development
const SUPABASE_URL = import.meta.env.VITE_PUBLIC_SUPABASE_URL || "https://nheycjpozywomwscplcz.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g";

// Validate configuration
if (!SUPABASE_URL || !SUPABASE_PUBLISHABLE_KEY) {
  console.error('Missing Supabase configuration. Please check your environment variables.');
}

// Log configuration in development
if (import.meta.env.DEV) {
  console.log('Supabase Configuration:', {
    url: SUPABASE_URL,
    hasKey: !!SUPABASE_PUBLISHABLE_KEY,
    keyPrefix: SUPABASE_PUBLISHABLE_KEY?.substring(0, 20) + '...'
  });
}

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: typeof window !== 'undefined' ? localStorage : undefined,
    persistSession: true,
    autoRefreshToken: true,
    detectSessionInUrl: true,
  }
});
