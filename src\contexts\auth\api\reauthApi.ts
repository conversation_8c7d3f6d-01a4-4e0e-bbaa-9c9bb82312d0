
import { supabase } from '@/integrations/supabase/client';

/**
 * Reauthenticate the user with their password
 */
export const reauthenticateUser = async (password: string): Promise<{ success: boolean; error?: any }> => {
  try {
    console.log("Attempting to reauthenticate user");
    
    // Get current user email
    const { data: { user } } = await supabase.auth.getUser();
    
    if (!user || !user.email) {
      return { success: false, error: { message: "No authenticated user found" } };
    }
    
    // Sign in again with the user's email and provided password
    const { data, error } = await supabase.auth.signInWithPassword({
      email: user.email,
      password,
    });
    
    if (error) {
      console.error("Reauthentication error:", error.message);
      return { success: false, error };
    }
    
    console.log("Reauthentication successful:", data);
    return { success: true };
  } catch (error: any) {
    console.error('Reauthentication error:', error);
    return { success: false, error };
  }
};
