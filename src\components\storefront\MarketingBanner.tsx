
import React, { useState } from "react";
import { Container } from "@/components/ui/container";
import { X } from "lucide-react";
import { cn } from "@/lib/utils";

interface MarketingBannerProps {
  text: string;
  link?: string;
  backgroundColor?: string;
  textColor?: string;
  isDismissible?: boolean;
  icon?: React.ReactNode;
  isFullWidth?: boolean;
}

const MarketingBanner: React.FC<MarketingBannerProps> = ({
  text,
  link,
  backgroundColor = "bg-green-500",
  textColor = "text-white",
  isDismissible = true,
  icon,
  isFullWidth = false,
}) => {
  const [isVisible, setIsVisible] = useState(true);

  if (!isVisible) return null;

  const BannerContent = () => (
    <div className="flex items-center justify-center gap-2 py-2 px-3 text-sm font-medium">
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span className="line-clamp-1 text-center">{text}</span>
    </div>
  );

  return (
    <div className={cn("w-full", backgroundColor, textColor)}>
      <div className={isFullWidth ? "w-full" : "container px-3"}>
        <div className="flex items-center justify-between">
          {link ? (
            <a 
              href={link} 
              className="flex-1 hover:underline"
              target="_blank" 
              rel="noopener noreferrer"
            >
              <BannerContent />
            </a>
          ) : (
            <div className="flex-1">
              <BannerContent />
            </div>
          )}
          
          {isDismissible && (
            <button 
              onClick={() => setIsVisible(false)} 
              className="p-1.5 hover:opacity-70 transition-opacity"
              aria-label="Dismiss"
            >
              <X className="h-3.5 w-3.5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default MarketingBanner;
