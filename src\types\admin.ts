export interface UserType {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  storeCount: number;
  createdAt: string;
}

export interface UserFilterState {
  search: string;
  role: string;
  status: string;
  sortBy: string;
  sortOrder: string;
}

// Add our new AdminStoreType
export interface AdminStoreType {
  id: string;
  name: string;
  owner: string;
  ownerId: string;
  url: string;
  status: string;
  productCount: number;
  orderCount: number;
  createdAt: string;
}
