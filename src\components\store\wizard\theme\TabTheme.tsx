
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

// Define the theme options interface (matches the one in StoreFormData)
interface ThemeOptions {
  darkMode?: boolean;
  customHeader?: boolean;
  customFooter?: boolean;
  colorScheme?: string;
  fontFamily?: string;
  cornerRadius?: number;
  buttonStyle?: string;
  layoutType?: string;
  productCardsPerRow?: number;
  showProductRatings?: boolean;
  showQuickAdd?: boolean;
  productImageStyle?: string;
  enableAnimations?: boolean;
  heroStyle?: string;
}

interface TabThemeProps {
  themeOptions: ThemeOptions;
  selectedTheme: string;
  handleUpdateThemeOptions: (updates: Partial<ThemeOptions>) => void;
  handleSelectTheme: (themeId: string) => void;
  themes: {
    id: string;
    name: string;
    image: string;
    description: string;
  }[];
}

const TabTheme: React.FC<TabThemeProps> = ({
  themeOptions,
  selectedTheme,
  handleUpdateThemeOptions,
  handleSelectTheme,
  themes
}) => {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {themes.map((theme) => (
          <Card 
            key={theme.id}
            className={cn(
              "cursor-pointer transition-all hover:border-primary",
              theme.id === selectedTheme && "border-2 border-green-500"
            )}
            onClick={() => handleSelectTheme(theme.id)}
          >
            <CardHeader className="p-4">
              <CardTitle className="text-base">{theme.name}</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <img 
                src={theme.image} 
                alt={theme.name} 
                className="w-full h-40 object-cover" 
              />
            </CardContent>
            <CardFooter className="p-4 pt-2">
              <CardDescription>{theme.description}</CardDescription>
            </CardFooter>
          </Card>
        ))}
      </div>

      <div className="space-y-4 pt-4">
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="dark-mode">Dark Mode Support</Label>
            <p className="text-sm text-muted-foreground">
              Allow customers to switch between light and dark mode
            </p>
          </div>
          <Switch 
            id="dark-mode" 
            checked={themeOptions.darkMode}
            onCheckedChange={(checked) => handleUpdateThemeOptions({ darkMode: checked })}
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div>
            <Label htmlFor="animations">Enable Animations</Label>
            <p className="text-sm text-muted-foreground">
              Add subtle animations to improve user experience
            </p>
          </div>
          <Switch 
            id="animations" 
            checked={themeOptions.enableAnimations}
            onCheckedChange={(checked) => handleUpdateThemeOptions({ enableAnimations: checked })}
          />
        </div>
      </div>
    </div>
  );
};

export default TabTheme;
