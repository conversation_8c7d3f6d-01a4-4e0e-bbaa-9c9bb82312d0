
import { supabase } from '@/integrations/supabase/client';
import { UserRole } from '@/constants/roles';
import { getRedirectUrl } from '@/utils/authRedirects';

/**
 * Handle user signup with name, email and password
 */
export const signupUser = async (name: string, email: string, password: string, role = UserRole.Customer): Promise<void> => {
  try {
    console.log("Attempting signup with name, email and role:", name, email, role);
    
    const redirectTo = getRedirectUrl();
    console.log("Signup redirect URL:", redirectTo);
    
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role,
        },
        emailRedirectTo: redirectTo,
      },
    });
    
    if (error) {
      console.error("Signup error:", error.message);
      throw error;
    }
    
    console.log("Signup successful, verification email sent. Data:", data);
  } catch (error: any) {
    console.error('Signup error:', error);
    throw error;
  }
};
