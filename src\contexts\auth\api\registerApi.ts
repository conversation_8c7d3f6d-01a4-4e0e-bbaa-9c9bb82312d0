
import { supabase } from '@/integrations/supabase/client';
import { UserRole } from '@/constants/roles';
import { getRedirectUrl } from '@/utils/authRedirects';

/**
 * Handle user signup with name, email and password
 */
export const signupUser = async (name: string, email: string, password: string, role = UserRole.Customer): Promise<void> => {
  try {
    console.log("Attempting signup with name, email and role:", name, email, role);

    const redirectTo = getRedirectUrl();
    console.log("Signup redirect URL:", redirectTo);

    // Check if we're in development mode
    const isDevelopment = import.meta.env.DEV || window.location.hostname === 'localhost';

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          name,
          role,
        },
        emailRedirectTo: redirectTo,
      },
    });

    if (error) {
      console.error("Signup error:", error.message);
      throw error;
    }

    console.log("Signup successful, verification email sent. Data:", data);

    // Development mode: Show helpful message about email confirmation
    if (isDevelopment) {
      console.log("🔧 DEVELOPMENT MODE: If you're not receiving emails, you can:");
      console.log("1. Disable email confirmation in Supabase Dashboard");
      console.log("2. Check the EMAIL_CONFIRMATION_FIX.md file for solutions");
      console.log("3. Configure custom SMTP in Supabase settings");
    }
  } catch (error: any) {
    console.error('Signup error:', error);
    throw error;
  }
};
