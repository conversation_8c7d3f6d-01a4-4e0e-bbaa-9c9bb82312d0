
/**
 * Creates an array of numbers progressing from start up to, but not including, end.
 *
 * @param {number} start - The start of the range
 * @param {number} end - The end of the range
 * @param {number} [step=1] - The value to increment by
 * @returns {Array} Returns the range of numbers
 */
function range(start, end, step = 1) {
  // Handle single argument case (e.g., range(4) produces [0, 1, 2, 3])
  if (end === undefined) {
    end = start;
    start = 0;
  }
  
  const result = [];
  
  if (step === 0) {
    return Array(Math.ceil(Math.abs(end - start))).fill(start);
  }
  
  if ((end > start && step < 0) || (end < start && step > 0)) {
    return [];
  }
  
  for (let i = start; end > start ? i < end : i > end; i += step) {
    result.push(i);
  }
  
  return result;
}

// Support both ESM and CJS
export default range;
