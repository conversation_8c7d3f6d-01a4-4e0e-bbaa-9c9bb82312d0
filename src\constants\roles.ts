/**
 * User roles for the application
 */
export enum UserRole {
  Admin = "admin",
  StoreOwner = "store_owner",
  Customer = "customer",
  Staff = "staff"
}

/**
 * Helper function to convert from string role to UserRole enum
 */
export const stringToUserRole = (role: string): UserRole => {
  switch (role) {
    case 'admin':
      return UserRole.Admin;
    case 'store_owner':
      return UserRole.StoreOwner;
    case 'customer':
      return UserRole.Customer;
    case 'staff':
      return UserRole.Staff;
    default:
      return UserRole.StoreOwner; // Default role changed to store_owner
  }
};
