
import React from "react";
import { Button } from "@/components/ui/button";

// Usage: <FeatureGate required="premium"><span>Premium Feature</span></FeatureGate>
export const FeatureGate: React.FC<{ required: "basic" | "premium" | "business"; userPlan?: string; children: React.ReactNode }> = ({
  required,
  userPlan = "basic",
  children
}) => {
  const planRank = { basic: 1, premium: 2, business: 3 };
  // Use the prop or hook for user's tier
  if (planRank[userPlan] >= planRank[required]) {
    return <>{children}</>;
  }
  return (
    <div className="bg-red-50 px-4 py-3 border-l-4 border-red-500 rounded mt-4 flex items-center justify-between">
      <div>
        <strong className="text-red-700">Feature locked</strong>
        <div className="text-sm text-red-800">Upgrade to {required.charAt(0).toUpperCase() + required.slice(1)} to unlock this feature.</div>
      </div>
      <Button size="sm" variant="default" onClick={() => window.location.href = "/settings/billing"}>
        Upgrade
      </Button>
    </div>
  );
};

export default FeatureGate;

