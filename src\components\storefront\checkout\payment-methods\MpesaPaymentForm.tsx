
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2, Phone } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface MpesaPaymentFormProps {
  amount: number;
  onPaymentComplete: (transactionId: string) => void;
}

const MpesaPaymentForm: React.FC<MpesaPaymentFormProps> = ({ amount, onPaymentComplete }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [processing, setProcessing] = useState(false);
  const { toast } = useToast();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phoneNumber || phoneNumber.length < 10) {
      toast({
        title: "Invalid phone number",
        description: "Please enter a valid phone number",
        variant: "destructive",
      });
      return;
    }
    
    // For demo, we simulate a payment process
    setProcessing(true);
    
    // In a real implementation, this would call an API endpoint
    setTimeout(() => {
      const mockTransactionId = 'MPESA-' + Math.floor(Math.random() * 100000000);
      onPaymentComplete(mockTransactionId);
      setProcessing(false);
      
      toast({
        title: "M-Pesa push sent",
        description: "Check your phone for the M-Pesa payment request.",
      });
    }, 2000);
  };
  
  const formatPhoneNumber = (value: string) => {
    // Only allow digits
    const digits = value.replace(/\D/g, '');
    // Format as needed
    return digits;
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="p-4 bg-muted rounded-md border text-sm mb-4">
        <p>Use M-Pesa to make your payment. Enter your phone number and we'll send you a payment request.</p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="phone">Phone Number</Label>
        <div className="flex">
          <span className="inline-flex items-center px-3 border border-r-0 border-input rounded-l-md bg-muted text-muted-foreground">
            +254
          </span>
          <Input
            id="phone"
            type="tel"
            placeholder="712 345 678"
            value={phoneNumber}
            onChange={(e) => setPhoneNumber(formatPhoneNumber(e.target.value))}
            className="rounded-l-none"
            required
            maxLength={10}
          />
        </div>
      </div>
      
      <Button 
        type="submit" 
        className="w-full mt-4"
        disabled={processing || phoneNumber.length < 9}
      >
        {processing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Sending Request...
          </>
        ) : (
          <>
            <Phone className="mr-2 h-4 w-4" />
            Send M-Pesa Request
          </>
        )}
      </Button>
    </form>
  );
};

export default MpesaPaymentForm;
