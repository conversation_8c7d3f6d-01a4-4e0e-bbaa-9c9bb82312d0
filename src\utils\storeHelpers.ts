
import { supabase } from '@/integrations/supabase/client';

/**
 * Converts a store name to a URL-friendly string
 * - Converts to lowercase
 * - Replaces spaces with hyphens
 * - Removes special characters
 * - Replaces multiple hyphens with a single hyphen
 */
export const generateUrlFromName = (name: string): string => {
  return name
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '') // Remove special characters except hyphens
    .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen
};

/**
 * Checks if a store URL is available
 * @param url The URL to check
 * @returns An object with available status and optional suggestion
 */
export const checkStoreUrlAvailability = async (url: string): Promise<{
  available: boolean;
  suggestion?: string;
  sanitized?: string;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.functions.invoke('check-store-url', {
      body: { url }
    });

    if (error) {
      console.error('Error checking URL availability:', error);
      return { available: false, error: 'Failed to check availability' };
    }

    return data;
  } catch (error) {
    console.error('Error in checkStoreUrlAvailability:', error);
    return { available: false, error: 'An unexpected error occurred' };
  }
};

/**
 * Request verification for a custom domain
 * @param domain The domain to verify
 * @param storeId The ID of the store
 * @returns Verification code and instructions
 */
export const requestDomainVerification = async (domain: string, storeId: string): Promise<{
  success?: boolean;
  verificationCode?: string;
  instructions?: string;
  error?: string;
}> => {
  try {
    const { data, error } = await supabase.functions.invoke('verify-domain', {
      body: { domain, storeId }
    });

    if (error) {
      console.error('Error requesting domain verification:', error);
      // Special handling for mock store IDs
      if (storeId.includes('mock')) {
        // Generate a mock verification code for development
        const mockVerificationCode = `mduka-mock-${Math.random().toString(16).substring(2, 10)}`;
        return {
          success: true,
          verificationCode: mockVerificationCode,
          instructions: `[DEV MODE] To verify your domain, add a TXT record with the name "_m-duka-verify" and the value "${mockVerificationCode}"`
        };
      }
      return { error: 'Failed to request verification' };
    }

    return data;
  } catch (error) {
    console.error('Error in requestDomainVerification:', error);
    return { error: 'An unexpected error occurred' };
  }
};

/**
 * Fetch domains for a store
 * @param storeId The ID of the store
 * @returns Array of domains
 */
export const getStoreDomains = async (storeId: string) => {
  try {
    // For mock/development stores, return an empty array to prevent errors
    if (storeId.includes('mock')) {
      console.log('Using mock store, returning empty domains array');
      return [];
    }
    
    const { data, error } = await supabase
      .from('store_domains')
      .select('*')
      .eq('store_id', storeId);

    if (error) {
      console.error('Error fetching store domains:', error);
      return [];
    }

    return data || [];
  } catch (error) {
    console.error('Error in getStoreDomains:', error);
    return [];
  }
};
