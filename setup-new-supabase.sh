#!/bin/bash

# Setup script for new Supabase project
# Run this after creating a new Supabase project

echo "M-duka Supabase Setup Script"
echo "============================"
echo ""

# Check if user provided project details
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <project_id> <project_url> <anon_key>"
    echo ""
    echo "Example:"
    echo "$0 abcdefghijklmnop https://abcdefghijklmnop.supabase.co eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    echo ""
    echo "Get these values from your Supabase dashboard:"
    echo "1. Go to https://supabase.com/dashboard"
    echo "2. Select your project"
    echo "3. Go to Settings > API"
    echo "4. Copy Project URL and anon public key"
    echo "5. Project ID is the subdomain of your URL"
    exit 1
fi

PROJECT_ID=$1
PROJECT_URL=$2
ANON_KEY=$3

echo "Setting up Supabase project:"
echo "Project ID: $PROJECT_ID"
echo "Project URL: $PROJECT_URL"
echo ""

# Update config.toml
echo "Updating supabase/config.toml..."
sed -i "s/nheycjpozywomwscplcz/$PROJECT_ID/g" supabase/config.toml
echo "✓ Updated config.toml"

# Update supabase.ts
echo "Updating src/lib/supabase.ts..."
sed -i "s|https://nheycjpozywomwscplcz.supabase.co|$PROJECT_URL|g" src/lib/supabase.ts
sed -i "s/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g/$ANON_KEY/g" src/lib/supabase.ts
echo "✓ Updated supabase.ts"

# Update setup scripts
echo "Updating setup scripts..."
sed -i "s/https://nheycjpozywomwscplcz.supabase.co/$PROJECT_URL/g" set-supabase-secrets.sh
sed -i "s/eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g/$ANON_KEY/g" set-supabase-secrets.sh
echo "✓ Updated setup scripts"

echo ""
echo "Configuration updated successfully!"
echo ""
echo "Next steps:"
echo "1. Run: supabase link --project-ref $PROJECT_ID"
echo "2. Run: supabase db push"
echo "3. Or manually run the migrations from supabase/migrations/ in your Supabase dashboard"
echo "4. Restart your development server: npm run dev"
echo ""
echo "For Lovable.dev deployment, set these environment variables:"
echo "VITE_PUBLIC_SUPABASE_URL=$PROJECT_URL"
echo "VITE_PUBLIC_SUPABASE_ANON_KEY=$ANON_KEY"
echo ""
echo "For Vercel deployment, run: ./vercel-env-setup.sh" 