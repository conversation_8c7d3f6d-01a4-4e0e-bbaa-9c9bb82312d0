
import React from 'react';
import { Button } from '@/components/ui/button';

interface ProductFormActionsProps {
  isSubmitting: boolean;
  isEditing: boolean;
}

const ProductFormActions: React.FC<ProductFormActionsProps> = ({ 
  isSubmitting,
  isEditing,
}) => {
  return (
    <div className="flex justify-end space-x-4">
      <Button type="button" variant="outline" onClick={() => window.history.back()}>
        Cancel
      </Button>
      <Button type="submit" disabled={isSubmitting}>
        {isSubmitting ? 'Saving...' : isEditing ? 'Update Product' : 'Create Product'}
      </Button>
    </div>
  );
};

export default ProductFormActions;
