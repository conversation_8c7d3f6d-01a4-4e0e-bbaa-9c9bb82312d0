
import React, { useState } from 'react';

interface ProductImageGalleryProps {
  images: string[];
  productName: string;
}

const ProductImageGallery: React.FC<ProductImageGalleryProps> = ({ 
  images, 
  productName 
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  // Use provided images or default to placeholder
  const productImages = images && images.length > 0 
    ? images 
    : ['/placeholder.svg'];

  return (
    <div className="space-y-4">
      <div className="aspect-square overflow-hidden rounded-lg border">
        <img 
          src={productImages[currentImageIndex]} 
          alt={productName} 
          className="w-full h-full object-cover"
        />
      </div>
      
      {productImages.length > 1 && (
        <div className="flex gap-2 overflow-x-auto pb-2">
          {productImages.map((image: string, index: number) => (
            <button 
              key={index}
              className={`relative border rounded-md overflow-hidden flex-shrink-0 w-20 h-20 ${
                currentImageIndex === index ? 'ring-2 ring-primary' : ''
              }`}
              onClick={() => setCurrentImageIndex(index)}
            >
              <img 
                src={image}
                alt={`Product view ${index + 1}`}
                className="w-full h-full object-cover"
              />
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default ProductImageGallery;
