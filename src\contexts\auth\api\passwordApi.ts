
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

/**
 * Send password reset email
 */
export const resetUserPassword = async (email: string): Promise<void> => {
  try {
    // Get the current domain from the browser window
    const currentDomain = window.location.origin;
    console.log("Using domain for password reset:", currentDomain);
    
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${currentDomain}/reset-password`,
    });
    
    if (error) throw error;
    
    toast.success('Password reset instructions sent to your email');
  } catch (error: any) {
    console.error('Reset password error:', error);
    toast.error(error.message || 'Failed to send reset instructions');
    throw error;
  }
};

/**
 * Update user password
 */
export const updateUserPassword = async (password: string, token?: string): Promise<void> => {
  try {
    const { error } = await supabase.auth.updateUser({
      password,
    });
    
    if (error) throw error;
    
    toast.success('Password updated successfully');
  } catch (error: any) {
    console.error('Update password error:', error);
    toast.error(error.message || 'Failed to update password');
    throw error;
  }
};
