
import React from 'react';
import { cn } from '@/lib/utils';

interface CategoriesSectionProps {
  categories: string[];
  fontFamily: string;
  cornerRadius: number;
  secondaryColor: string;
  showProductCount?: boolean;
}

const CategoriesSection: React.FC<CategoriesSectionProps> = ({
  categories,
  fontFamily,
  cornerRadius,
  secondaryColor,
  showProductCount = true,
}) => {
  return (
    <section className="py-6">
      <h2 className={cn(
        "text-2xl font-bold mb-4",
        `font-${fontFamily}`
      )}>
        Shop by Category
      </h2>
      
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {categories.map((category, index) => (
          <div
            key={index}
            className={cn(
              "p-4 text-center",
              secondaryColor,
              `rounded-[${cornerRadius}px]`
            )}
          >
            <p className={cn(
              "font-medium",
              `font-${fontFamily}`
            )}>
              {category}
              {showProductCount && (
                <span className="block text-sm text-gray-500 mt-1">
                  {/* Mock product count for preview */}
                  {Math.floor(Math.random() * 50) + 5} products
                </span>
              )}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
};

export default CategoriesSection;
