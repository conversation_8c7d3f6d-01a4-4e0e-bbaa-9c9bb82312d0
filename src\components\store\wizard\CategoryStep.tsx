
import React from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { StoreFormData } from '@/types/store';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

const formSchema = z.object({
  category: z.string().min(1, { message: 'Please select a category.' }),
  storeType: z.enum(['physical', 'digital', 'service', 'hybrid'], { 
    required_error: 'Please select the type of products you sell.' 
  }),
});

interface CategoryStepProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

const storeCategories = [
  { value: 'fashion', label: 'Fashion & Apparel' },
  { value: 'electronics', label: 'Electronics & Gadgets' },
  { value: 'home', label: 'Home & Furniture' },
  { value: 'beauty', label: 'Beauty & Personal Care' },
  { value: 'food', label: 'Food & Beverages' },
  { value: 'health', label: 'Health & Wellness' },
  { value: 'books', label: 'Books & Stationery' },
  { value: 'toys', label: 'Toys & Games' },
  { value: 'sports', label: 'Sports & Outdoors' },
  { value: 'art', label: 'Art & Crafts' },
  { value: 'digital', label: 'Digital Products' },
  { value: 'services', label: 'Services' },
  { value: 'other', label: 'Other' },
];

const CategoryStep: React.FC<CategoryStepProps> = ({ data, updateData }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: data.category || '',
      storeType: (data.storeType as any) || 'physical',
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    updateData(values);
  };

  // Auto-save as user types
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      if (value.category) {
        onSubmit(value as z.infer<typeof formSchema>);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Store Category</h2>
      <p className="text-muted-foreground mb-6">
        Help customers find your store by selecting the most relevant category.
      </p>

      <Form {...form}>
        <form onChange={form.handleSubmit(onSubmit)} className="space-y-8">
          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Store Category</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a category" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {storeCategories.map((category) => (
                      <SelectItem key={category.value} value={category.value}>
                        {category.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormDescription>
                  Choose the category that best describes your store.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="storeType"
            render={({ field }) => (
              <FormItem className="space-y-3">
                <FormLabel>What type of products do you sell?</FormLabel>
                <FormControl>
                  <RadioGroup
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    className="flex flex-col space-y-3"
                  >
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="physical" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Physical products (require shipping)
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="digital" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Digital products (downloads, subscriptions)
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="service" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Services (bookings, appointments)
                      </FormLabel>
                    </FormItem>
                    <FormItem className="flex items-center space-x-3 space-y-0">
                      <FormControl>
                        <RadioGroupItem value="hybrid" />
                      </FormControl>
                      <FormLabel className="font-normal">
                        Both physical and digital products
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
};

export default CategoryStep;
