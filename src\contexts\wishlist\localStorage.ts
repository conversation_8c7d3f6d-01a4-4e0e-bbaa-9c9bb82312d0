
import { WishlistItem } from './types';

const WISHLIST_STORAGE_KEY = 'wishlist';

/**
 * Loads wishlist data from localStorage for non-authenticated users
 */
export const loadWishlistFromLocalStorage = (): WishlistItem[] => {
  try {
    const savedWishlist = localStorage.getItem(WISHLIST_STORAGE_KEY);
    if (savedWishlist) {
      return JSON.parse(savedWishlist);
    }
  } catch (error) {
    console.error('Failed to parse saved wishlist:', error);
  }
  return [];
};

/**
 * Saves wishlist data to localStorage for non-authenticated users
 */
export const saveWishlistToLocalStorage = (items: WishlistItem[]): void => {
  localStorage.setItem(WISHLIST_STORAGE_KEY, JSON.stringify(items));
};
