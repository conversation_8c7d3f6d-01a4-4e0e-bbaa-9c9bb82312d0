
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Bell, Search, Moon, Sun } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface HeaderActionsProps {
  notificationCount: number;
}

const HeaderActions: React.FC<HeaderActionsProps> = ({ notificationCount }) => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  const toggleDarkMode = () => {
    setIsDarkMode(!isDarkMode);
    // Implementation would go here to actually toggle dark mode
  };

  return (
    <>
      <Button 
        variant="ghost" 
        size="icon" 
        onClick={() => setIsSearchOpen(!isSearchOpen)}
        aria-label="Search"
      >
        <Search className="h-5 w-5 text-muted-foreground" />
      </Button>
      
      {isSearchOpen && (
        <div className="absolute inset-x-0 top-16 p-4 border-b bg-background z-20">
          <div className="container mx-auto">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search for users, stores, or orders..." 
                className="w-full pl-10 h-10"
                autoFocus
              />
            </div>
          </div>
        </div>
      )}

      <Button 
        variant="ghost" 
        size="icon" 
        className="text-muted-foreground relative"
        aria-label="Notifications"
      >
        <Bell className="h-5 w-5" />
        {notificationCount > 0 && (
          <Badge className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 flex items-center justify-center bg-green-500 text-white">
            {notificationCount}
          </Badge>
        )}
      </Button>

      <Button
        variant="ghost"
        size="icon"
        onClick={toggleDarkMode}
        aria-label={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
      >
        {isDarkMode ? (
          <Sun className="h-5 w-5 text-muted-foreground" />
        ) : (
          <Moon className="h-5 w-5 text-muted-foreground" />
        )}
      </Button>
    </>
  );
};

export default HeaderActions;
