
import { supabase } from '@/integrations/supabase/client';
import { WishlistItem } from './types';
import { transformSupabaseProduct } from '@/contexts/cart/utils';

/**
 * Fetches wishlist items for the authenticated user from Supabase
 */
export const fetchWishlistItems = async (userId: string): Promise<WishlistItem[]> => {
  try {
    // For development user ID, return empty array to avoid DB errors
    if (userId === 'dev-user-id') {
      console.log('Using mock wishlist for development user');
      return [];
    }

    const { data, error } = await supabase
      .from('wishlists')
      .select(`
        id,
        products:product_id (*)
      `)
      .eq('customer_id', userId);
    
    if (error) {
      console.error('Error fetching wishlist items:', error);
      throw error;
    }
    
    if (!data) return [];
    
    // Transform the data to match our WishlistItem structure
    return data.map(item => ({
      id: item.id,
      product: transformSupabaseProduct(item.products),
      user_id: userId,
      product_id: item.products.id
    }));
  } catch (error) {
    console.error('Failed to load wishlist from Supabase:', error);
    throw error;
  }
};

/**
 * Adds a product to the wishlist in Supabase
 */
export const addWishlistItem = async (userId: string, productId: string): Promise<string> => {
  // For development user ID, return mock ID to avoid DB errors
  if (userId === 'dev-user-id') {
    console.log('Using mock wishlist add for development user');
    return 'mock-wishlist-item-id';
  }

  const { data, error } = await supabase
    .from('wishlists')
    .insert({
      customer_id: userId,
      product_id: productId
    })
    .select('id')
    .single();
  
  if (error) throw error;
  return data.id;
};

/**
 * Removes a product from the wishlist in Supabase
 */
export const removeWishlistItem = async (itemId: string): Promise<void> => {
  // For development user ID items, just log
  if (itemId.startsWith('mock-')) {
    console.log('Using mock wishlist remove for development user');
    return;
  }

  const { error } = await supabase
    .from('wishlists')
    .delete()
    .eq('id', itemId);
  
  if (error) throw error;
};

/**
 * Clears all wishlist items for a user from Supabase
 */
export const clearUserWishlist = async (userId: string): Promise<void> => {
  // For development user ID, just log
  if (userId === 'dev-user-id') {
    console.log('Using mock wishlist clear for development user');
    return;
  }

  const { error } = await supabase
    .from('wishlists')
    .delete()
    .eq('customer_id', userId);
  
  if (error) throw error;
};

/**
 * Checks if a product is in the wishlist
 */
export const isProductInWishlist = async (userId: string, productId: string): Promise<boolean> => {
  // For development user ID, return false
  if (userId === 'dev-user-id') {
    return false;
  }

  const { data, error } = await supabase
    .from('wishlists')
    .select('id')
    .eq('customer_id', userId)
    .eq('product_id', productId)
    .maybeSingle();
  
  if (error) {
    console.error('Error checking wishlist:', error);
    return false;
  }
  
  return !!data;
};
