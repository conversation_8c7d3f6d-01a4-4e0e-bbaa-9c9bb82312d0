
import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.36.0';

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Pesapal API credentials
const consumerKey = Deno.env.get('PESAPAL_CONSUMER_KEY') || '';
const consumerSecret = Deno.env.get('PESAPAL_CONSUMER_SECRET') || '';
const callbackUrl = Deno.env.get('PESAPAL_CALLBACK_URL') || '';
const pesapalApiUrl = Deno.env.get('PESAPAL_API_URL') || 'https://pay.pesapal.com/v3';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Function to get Pesapal auth token
async function getPesapalAuthToken(consumerKey: string, consumerSecret: string) {
  if (!consumerKey || !consumerSecret) {
    throw new Error('Pesapal API credentials not configured');
  }
  
  try {
    console.log('Getting Pesapal auth token');
    
    // In a real implementation, we would make an actual API call to Pesapal
    // The URL would be something like: ${pesapalApiUrl}/api/Auth/RequestToken
    
    // For now, we'll simulate the response
    // In production, this would be a real API call:
    /*
    const response = await fetch(`${pesapalApiUrl}/api/Auth/RequestToken`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        consumer_key: consumerKey,
        consumer_secret: consumerSecret
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to get auth token: ${response.status} ${errorText}`);
    }
    
    const data = await response.json();
    return data.token;
    */
    
    // Simulated response for development
    console.log('Auth token retrieved successfully (simulated)');
    return 'simulated-auth-token-' + Date.now();
  } catch (error) {
    console.error('Error getting Pesapal auth token:', error);
    throw error;
  }
}

// Function to submit order to Pesapal
async function submitOrderToPesapal(authToken: string, orderData: {
  email: string;
  phoneNumber: string;
  amount: number;
  orderId: string;
  callbackUrl: string;
}) {
  try {
    console.log('Submitting order to Pesapal:', orderData);
    
    // In a real implementation, we would make an actual API call to Pesapal
    // The URL would be something like: ${pesapalApiUrl}/api/Transactions/SubmitOrderRequest
    
    // For now, we'll simulate the response
    // In production, this would be a real API call:
    /*
    const response = await fetch(`${pesapalApiUrl}/api/Transactions/SubmitOrderRequest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${authToken}`
      },
      body: JSON.stringify({
        id: orderData.orderId,
        currency: 'USD',
        amount: orderData.amount,
        description: `Payment for order ${orderData.orderId}`,
        callback_url: orderData.callbackUrl,
        notification_id: 'your-notification-id',
        billing_address: {
          email_address: orderData.email,
          phone_number: orderData.phoneNumber,
        }
      })
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Failed to submit order: ${response.status} ${errorText}`);
    }
    
    const data = await response.json();
    return {
      success: true,
      order_tracking_id: data.order_tracking_id,
      redirect_url: data.redirect_url,
      order_id: orderData.orderId
    };
    */
    
    // Generate a fake tracking ID and redirect URL
    const trackingId = 'PESAPAL-' + Math.random().toString(36).substring(2, 15);
    const redirectUrl = `https://pay.pesapal.com/iframe?OrderTrackingId=${trackingId}`;
    
    // Simulated response for development
    console.log(`Order submitted successfully (simulated). Tracking ID: ${trackingId}`);
    return {
      success: true,
      order_tracking_id: trackingId,
      redirect_url: redirectUrl,
      order_id: orderData.orderId
    };
  } catch (error) {
    console.error('Error submitting order to Pesapal:', error);
    throw error;
  }
}

// Handle requests
serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Validate content type for non-OPTIONS requests
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return new Response(
        JSON.stringify({
          error: 'Invalid content type. Expected application/json',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    const { email, phoneNumber, amount, orderId, userId } = await req.json();
    
    // Validate input
    if (!email || !phoneNumber || !amount || !orderId || !userId) {
      const missingParams = [];
      if (!email) missingParams.push('email');
      if (!phoneNumber) missingParams.push('phoneNumber');
      if (!amount) missingParams.push('amount');
      if (!orderId) missingParams.push('orderId');
      if (!userId) missingParams.push('userId');
      
      console.error('Missing required parameters:', missingParams);
      
      return new Response(
        JSON.stringify({
          error: 'Missing required parameters',
          details: `Required parameters: ${missingParams.join(', ')}`,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return new Response(
        JSON.stringify({
          error: 'Invalid email format',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }
    
    // Validate Pesapal credentials
    if (!consumerKey || !consumerSecret || !callbackUrl) {
      console.error('Pesapal configuration incomplete');
      return new Response(
        JSON.stringify({
          error: 'Payment gateway configuration incomplete',
          details: 'The Pesapal payment gateway is not properly configured',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      );
    }
    
    console.log('Processing payment for order:', orderId);
    console.log('Payment details:', { email, phoneNumber, amount });
    
    // Get Pesapal auth token
    const authToken = await getPesapalAuthToken(consumerKey, consumerSecret);
    
    if (!authToken) {
      throw new Error('Failed to get Pesapal authentication token');
    }
    
    // Submit order to Pesapal
    const pesapalResponse = await submitOrderToPesapal(authToken, {
      email,
      phoneNumber,
      amount,
      orderId,
      callbackUrl
    });
    
    // Update transaction in database
    if (pesapalResponse && pesapalResponse.order_tracking_id) {
      // Find the transaction for this order
      const { data: transactions, error: txError } = await supabase
        .from('payment_transactions')
        .select('*')
        .eq('order_id', orderId)
        .limit(1);
      
      if (txError) {
        console.error('Error fetching transaction from database:', txError);
        throw txError;
      }
      
      if (transactions && transactions.length > 0) {
        const transaction = transactions[0];
        
        // Update transaction with Pesapal details
        const { error: updateError } = await supabase
          .from('payment_transactions')
          .update({
            status: 'processing',
            transaction_id: pesapalResponse.order_tracking_id,
            payment_data: {
              ...transaction.payment_data,
              pesapal_response: pesapalResponse,
              redirect_url: pesapalResponse.redirect_url,
              phone_number: phoneNumber,
              email: email,
              initiated_at: new Date().toISOString()
            }
          })
          .eq('id', transaction.id);
        
        if (updateError) {
          console.error('Error updating transaction in database:', updateError);
          throw updateError;
        }
        
        console.log(`Successfully updated transaction for order ${orderId}`);
      } else {
        console.log(`No transaction found for order ${orderId}, creating new one`);
        
        // Create a new transaction if one doesn't exist
        const { error: insertError } = await supabase
          .from('payment_transactions')
          .insert({
            order_id: orderId,
            user_id: userId,
            amount: amount,
            currency: 'USD',
            payment_method: 'pesapal',
            status: 'processing',
            transaction_id: pesapalResponse.order_tracking_id,
            payment_data: {
              pesapal_response: pesapalResponse,
              redirect_url: pesapalResponse.redirect_url,
              phone_number: phoneNumber,
              email: email,
              initiated_at: new Date().toISOString()
            }
          });
        
        if (insertError) {
          console.error('Error creating new transaction in database:', insertError);
          throw insertError;
        }
      }
    }
    
    return new Response(
      JSON.stringify({
        success: true,
        redirect_url: pesapalResponse.redirect_url,
        order_tracking_id: pesapalResponse.order_tracking_id
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error processing Pesapal payment:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
