
import { motion } from "framer-motion";
import { MessageSquare } from "lucide-react";

const PaymentInfo = () => {
  return (
    <>
      <div className="p-4">
        <div className="text-sm font-medium mb-2">Payment Information</div>
        <div className="bg-green-50 p-2 rounded-md">
          <div className="flex justify-between text-xs">
            <span>Total:</span>
            <span className="font-bold">TZS 19,500</span>
          </div>
          <div className="flex justify-between text-xs mt-1">
            <span>Payment Method:</span>
            <span>M-Pesa</span>
          </div>
          <div className="flex justify-between text-xs mt-1">
            <span>Status:</span>
            <span className="text-green-600 font-medium">Paid</span>
          </div>
        </div>
        
        {/* Delivery Status */}
        <div className="mt-4">
          <div className="text-sm font-medium mb-2">Delivery Status</div>
          <div className="flex items-center justify-between text-xs bg-blue-50 p-2 rounded-md">
            <span>Estimated Delivery:</span>
            <span className="font-medium">Today, 2:45 PM</span>
          </div>
          
          {/* WhatsApp Button */}
          <div className="mt-3 bg-green-500 text-white p-2 rounded-lg flex items-center justify-center gap-2 shadow-md">
            <MessageSquare className="h-4 w-4" />
            <span className="text-xs font-medium">Chat with Seller</span>
          </div>
        </div>
      </div>
    </>
  );
};

export default PaymentInfo;
