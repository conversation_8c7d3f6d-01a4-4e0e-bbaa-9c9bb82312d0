
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStore } from '../StoreContext';
import { useAuth } from '@/contexts/auth/AuthContext';
import { useToast } from '@/hooks/use-toast';
import type { StoreFormData } from '@/types/store';
import { Store, InsertStore } from '@/types/database';

export const useStoreCreate = () => {
  const navigate = useNavigate();
  const { createStore } = useStore();
  const { user } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (data: StoreFormData) => {
    if (!user?.id) {
      toast({
        title: 'Error',
        description: 'You must be logged in to create a store',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Create store in database
      const storeData: InsertStore = {
        name: data.name,
        description: data.description,
        store_url: data.storeUrl,
        owner_id: user.id,
        store_type: data.storeType,
        category: data.category,
        theme: {
          primaryColor: data.themeId === 'dark' ? '#000000' : '#ffffff',
          secondaryColor: data.themeId === 'dark' ? '#ffffff' : '#000000'
        },
        payment_methods: data.paymentMethods,
        notifications: {
          email: true, // Required field
          sms: false, // Required field
          orderNotifications: data.notifications.orderNotifications,
          stockNotifications: data.notifications.stockNotifications,
          marketingNotifications: data.notifications.marketingNotifications
        },
        logo_url: null
      };

      await createStore(storeData);

      toast({
        title: 'Store created successfully',
        description: 'Your store has been created and is ready to use.',
      });

      // Navigate to store dashboard
      navigate(`/shop/${data.storeUrl}/dashboard`);
    } catch (error) {
      console.error('Error creating store:', error);
      toast({
        title: 'Error creating store',
        description: error instanceof Error ? error.message : 'Failed to create store',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    handleSubmit,
    isSubmitting,
  };
};
