
import React from 'react';
import { cn } from '@/lib/utils';

interface HeadingProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

export const Heading: React.FC<HeadingProps> = ({ 
  title, 
  description, 
  icon,
  className
}) => {
  return (
    <div className={cn("flex items-center gap-2 mb-2", className)}>
      {icon && <div className="flex-shrink-0">{icon}</div>}
      <div className="flex flex-col space-y-1">
        <h2 className="text-xl font-semibold tracking-tight">{title}</h2>
        {description && (
          <p className="text-sm text-muted-foreground">{description}</p>
        )}
      </div>
    </div>
  );
};
