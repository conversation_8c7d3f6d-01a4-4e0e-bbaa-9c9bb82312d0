
import { supabase } from "@/integrations/supabase/client";
import { UserRole } from "@/constants/roles"; // Added import

/**
 * Assign a role to a user (admin only).
 * @param userId - the user to give the role to
 * @param role - must be a value from UserRole
 * @param byAdminId - who is granting the role
 */
export async function assignUserRole(
  userId: string,
  role: UserRole, // Type enforced here
  byAdminId: string
) {
  // Write an audit entry after successful role assignment (future-proofing)
  const { error: roleError } = await supabase
    .from("user_roles")
    .insert([
      {
        user_id: userId,
        role, // This is now UserRole type
        granted_by: byAdminId,
      },
    ]);
  return { error: roleError };
}

/**
 * Remove a role from a user (admin only).
 * @param userId - user ID
 * @param role - must be a valid UserRole, not a general string
 */
export async function removeUserRole(
  userId: string,
  role: UserRole // Type enforced here
) {
  const { error } = await supabase
    .from("user_roles")
    .delete()
    .eq("user_id", userId)
    .eq("role", role); // Now expects correct type

  return { error };
}

/**
 * Fetch all roles for all users (for admin user table).
 */
export async function fetchAllUserRoles() {
  const { data, error } = await supabase
    .from("user_roles")
    .select("*");

  return { data, error };
}

// -- CHANGELOG --
// - Enforced correct UserRole typing for role assignment/removal to match Supabase ENUM.
// - Prevented accidental use of arbitrary strings for roles.
