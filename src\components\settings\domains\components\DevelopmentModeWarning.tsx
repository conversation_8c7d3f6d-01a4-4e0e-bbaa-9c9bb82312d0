
import React from 'react';
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

interface DevelopmentModeWarningProps {
  storeId: string;
}

export const DevelopmentModeWarning: React.FC<DevelopmentModeWarningProps> = ({ storeId }) => {
  const isMockStore = storeId.includes('mock');

  return (
    <Alert variant={isMockStore ? "destructive" : "default"} className="mb-4">
      <AlertCircle className="h-4 w-4 mr-2" />
      <AlertTitle>
        {isMockStore ? "Development Mode Active" : "Store Information"}
      </AlertTitle>
      <AlertDescription>
        <p className="text-sm mb-1">Current store ID: {storeId}</p>
        {isMockStore && (
          <p className="text-sm text-red-600 dark:text-red-400">
            You're using a mock store in development mode. 
            Some features may be limited or simulated.
          </p>
        )}
        <p className="text-sm mt-2">
          When adding domains to your store, they will need to be verified before they can be used.
        </p>
      </AlertDescription>
    </Alert>
  );
};
