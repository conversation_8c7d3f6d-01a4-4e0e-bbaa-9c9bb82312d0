
import { Product } from '@/types/unified-product';
import { CartItem } from './types';

/**
 * Transforms a product from Supabase format to the application's Product type
 */
export const transformSupabaseProduct = (supabaseProduct: any): Product => {
  return {
    id: supabaseProduct.id,
    name: supabaseProduct.name,
    description: supabaseProduct.description || '',
    price: supabaseProduct.price,
    stock_quantity: supabaseProduct.stock_quantity || 0,
    is_active: supabaseProduct.is_active,
    created_at: supabaseProduct.created_at,
    updated_at: supabaseProduct.updated_at,
    currency: supabaseProduct.currency || 'USD',
    sku: supabaseProduct.sku || '',
    images: supabaseProduct.images || [],
    category: supabaseProduct.category || '',
    tags: supabaseProduct.tags || [],
    sale_price: supabaseProduct.sale_price,
    available: supabaseProduct.available ?? true,
    specifications: supabaseProduct.specifications ?? null,
    store_id: supabaseProduct.store_id || '',
    views_count: supabaseProduct.views_count ?? 0,
  };
};

/**
 * Calculates cart totals including subtotal, shipping, total and item count
 */
export const calculateCartTotals = (items: CartItem[]) => {
  // Calculate the subtotal (sum of all item prices)
  const subtotal = items.reduce((total, item) => {
    const itemPrice = item.variant?.price || item.product.price;
    return total + (itemPrice * item.quantity);
  }, 0);
  
  // Count total number of items in cart
  const itemCount = items.reduce((count, item) => count + item.quantity, 0);
  
  // For now, use a fixed shipping cost of $5 if there are items, otherwise 0
  const shipping = items.length > 0 ? 5 : 0;
  
  // Calculate the total (subtotal + shipping)
  const total = subtotal + shipping;
  
  return {
    subtotal,
    shipping,
    total,
    itemCount
  };
};
