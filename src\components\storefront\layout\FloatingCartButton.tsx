
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ShoppingCart } from "lucide-react";
import { cn } from "@/lib/utils";
import { isStoreSubdomain } from "@/utils/authRedirects";
import { toast } from "sonner";
import { useCart } from "@/contexts/cart/CartContext";

const FloatingCartButton: React.FC = () => {
  const isStorefront = isStoreSubdomain();
  const cartPath = isStorefront ? "/cart" : "/shop/cart";
  
  // Use cart context directly
  const { itemCount, isLoading } = useCart();

  const handleCartClick = (e: React.MouseEvent) => {
    if (isLoading) {
      e.preventDefault();
      toast.info("Cart is being prepared. Please try again in a moment.");
    }
  };

  // Only show if cart is loaded and has items
  if (isLoading || itemCount <= 0) {
    return null;
  }

  return (
    <div className="fixed bottom-24 md:bottom-10 right-6 z-50">
      <Link to={cartPath} onClick={handleCartClick}>
        <Button 
          size="lg" 
          className={cn(
            "rounded-full w-14 h-14 shadow-lg flex items-center justify-center",
            "bg-primary hover:bg-primary/90 transition-all"
          )}
        >
          <ShoppingCart className="h-6 w-6" />
          <span className={cn(
            "absolute -top-2 -right-2 bg-destructive text-xs rounded-full w-6 h-6",
            "flex items-center justify-center text-white font-bold"
          )}>
            {itemCount}
          </span>
        </Button>
      </Link>
    </div>
  );
};

export default FloatingCartButton;
