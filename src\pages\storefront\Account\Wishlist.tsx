
import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Heart, Loader2, ShoppingCart, Trash } from "lucide-react";
import { useWishlist, useCart } from "@/contexts";
import { formatCurrency } from "@/utils/formatters";
import { useToast } from "@/hooks/use-toast";

const AccountWishlist: React.FC = () => {
  const { items: wishlistItems, isLoading, removeFromWishlist, clearWishlist } = useWishlist();
  const { addToCart } = useCart();
  const { toast } = useToast();
  const navigate = useNavigate();
  
  const handleRemoveItem = async (productId: string) => {
    await removeFromWishlist(productId);
    toast({
      title: "Item removed",
      description: "Product has been removed from your wishlist"
    });
  };
  
  const handleAddToCart = async (wishlistItemId: string) => {
    const item = wishlistItems.find(item => item.id === wishlistItemId);
    if (item) {
      await addToCart(item.product, 1);
      toast({
        title: "Added to cart",
        description: `${item.product.name} has been added to your cart`
      });
    }
  };
  
  const handleClearWishlist = async () => {
    await clearWishlist();
    toast({
      title: "Wishlist cleared",
      description: "All items have been removed from your wishlist"
    });
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-16">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-semibold">My Wishlist</h2>
        {wishlistItems.length > 0 && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleClearWishlist}
          >
            <Trash className="h-4 w-4 mr-2" />
            Clear Wishlist
          </Button>
        )}
      </div>
      
      {wishlistItems.length === 0 ? (
        <div className="text-center py-16 border rounded-lg bg-gray-50">
          <Heart className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h3 className="text-xl font-medium mb-2">Your wishlist is empty</h3>
          <p className="text-muted-foreground mb-6">
            Save your favorite items to your wishlist for later.
          </p>
          <Button onClick={() => navigate("/shop")}>
            Continue Shopping
          </Button>
        </div>
      ) : (
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead className="bg-gray-50 text-xs">
              <tr>
                <th className="px-4 py-3 text-left">Product</th>
                <th className="px-4 py-3 text-right">Price</th>
                <th className="px-4 py-3 text-right">Stock</th>
                <th className="px-4 py-3 text-right">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y text-sm">
              {wishlistItems.map((item) => (
                <tr key={item.id}>
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="h-16 w-16 flex-shrink-0 overflow-hidden rounded-md border mr-4">
                        <img
                          src={item.product.images && item.product.images.length > 0 
                            ? item.product.images[0] 
                            : '/placeholder.svg'}
                          alt={item.product.name}
                          className="h-full w-full object-cover object-center"
                        />
                      </div>
                      <div>
                        <h3 className="font-medium">
                          <a 
                            href={`/shop/product/${item.product.id}`}
                            className="hover:underline"
                          >
                            {item.product.name}
                          </a>
                        </h3>
                        {item.product.category && (
                          <p className="text-xs text-muted-foreground">
                            {item.product.category}
                          </p>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3 text-right">
                    {formatCurrency(item.product.price || 0, item.product.currency || 'USD')}
                  </td>
                  <td className="px-4 py-3 text-right">
                    {item.product.stock_quantity && item.product.stock_quantity > 0 ? (
                      <span className="text-green-600">In Stock</span>
                    ) : (
                      <span className="text-red-500">Out of Stock</span>
                    )}
                  </td>
                  <td className="px-4 py-3 text-right">
                    <div className="flex justify-end gap-2">
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleAddToCart(item.id)}
                        disabled={!item.product.stock_quantity || item.product.stock_quantity === 0}
                      >
                        <ShoppingCart className="h-4 w-4 mr-1" />
                        Add to Cart
                      </Button>
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleRemoveItem(item.product_id)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default AccountWishlist;
