
import React from 'react';
import NoSubscriptionAlert from '@/components/settings/billing/NoSubscriptionAlert';
import SubscriptionCard from '@/components/settings/billing/SubscriptionCard';
import ResourceUsageCard from '@/components/settings/billing/ResourceUsageCard';
import AddOnsCard from '@/components/settings/billing/AddOnsCard';
import CreditsCard from '@/components/settings/billing/CreditsCard';
import InvoicesCard from '@/components/settings/billing/InvoicesCard';

const Billing = () => {
  return (
    <div className="max-w-5xl">
      <h2 className="text-xl font-semibold mb-6">Billing</h2>
      
      <NoSubscriptionAlert />
      <SubscriptionCard />
      <ResourceUsageCard />
      <AddOnsCard />
      <CreditsCard />
      <InvoicesCard />
    </div>
  );
};

export default Billing;
