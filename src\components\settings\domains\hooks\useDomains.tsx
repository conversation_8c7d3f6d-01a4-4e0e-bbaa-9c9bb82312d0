
import { useState, useEffect } from 'react';
import { useStore } from '@/contexts';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { requestDomainVerification, getStoreDomains } from '@/utils/storeHelpers';

export interface Domain {
  id: string;
  domain: string;
  verified: boolean;
  verification_code: string;
}

export function useDomains() {
  const { currentStore } = useStore();
  const [domains, setDomains] = useState<Domain[]>([]);
  const [loading, setLoading] = useState(false);
  const [newDomain, setNewDomain] = useState('');
  const [verificationInstructions, setVerificationInstructions] = useState<string | null>(null);
  const [verificationCode, setVerificationCode] = useState<string | null>(null);
  const [checkingVerification, setCheckingVerification] = useState(false);

  const loadDomains = async () => {
    if (!currentStore?.id) return;
    
    try {
      const domainsList = await getStoreDomains(currentStore.id);
      setDomains(domainsList);
    } catch (error) {
      console.error('Error loading domains:', error);
      // Don't show error toast in development with mock stores
      if (!currentStore.id.includes('mock')) {
        toast.error('Failed to load domains');
      }
    }
  };

  useEffect(() => {
    loadDomains();
  }, [currentStore?.id]);

  const handleAddDomain = async () => {
    if (!currentStore?.id) {
      toast.error('Store not found');
      return;
    }
    
    if (!newDomain) {
      toast.error('Please enter a domain');
      return;
    }
    
    // Basic domain format validation
    const domainRegex = /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    if (!domainRegex.test(newDomain)) {
      toast.error('Please enter a valid domain (e.g., yourdomain.com)');
      return;
    }
    
    setLoading(true);
    
    try {
      const result = await requestDomainVerification(newDomain, currentStore.id);
      
      if (result.error) {
        toast.error(result.error);
        return;
      }
      
      if (result.success && result.verificationCode && result.instructions) {
        setVerificationCode(result.verificationCode);
        setVerificationInstructions(result.instructions);
        toast.success('Domain added, verification required');
        
        // Refresh the domains list
        await loadDomains();
        setNewDomain('');
      } else {
        toast.error('An unexpected error occurred');
      }
    } catch (error) {
      console.error('Error adding domain:', error);
      toast.error('Failed to add domain');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyDomain = async (domainId: string, domain: string) => {
    if (!currentStore?.id) return;
    
    setCheckingVerification(true);
    
    try {
      // For development/demo purposes, we'll simulate a successful verification
      if (currentStore.id.includes('mock')) {
        const { data, error } = await supabase
          .from('store_domains')
          .update({ verified: true })
          .eq('id', domainId)
          .eq('store_id', currentStore.id);
        
        if (error) {
          console.error('Error updating domain:', error);
          toast.error('Failed to verify domain');
          return;
        }
        
        toast.success('Domain verified successfully (demo mode)');
        await loadDomains();
        return;
      }
      
      // In a real implementation, we would check the DNS records
      // This would typically be done through an edge function
      const { error } = await supabase
        .from('store_domains')
        .update({ verified: true })
        .eq('id', domainId)
        .eq('store_id', currentStore.id);
      
      if (error) {
        toast.error('Failed to verify domain');
        return;
      }
      
      toast.success('Domain verified successfully');
      await loadDomains();
    } catch (error) {
      console.error('Error verifying domain:', error);
      toast.error('Failed to verify domain');
    } finally {
      setCheckingVerification(false);
    }
  };

  const handleDeleteDomain = async (domainId: string) => {
    if (!currentStore?.id) return;
    
    try {
      // For development/demo purposes with mock stores
      if (currentStore.id.includes('mock')) {
        // Simply remove from local state without a database call
        setDomains(prev => prev.filter(d => d.id !== domainId));
        toast.success('Domain deleted successfully (demo mode)');
        return;
      }
      
      const { error } = await supabase
        .from('store_domains')
        .delete()
        .eq('id', domainId)
        .eq('store_id', currentStore.id);
      
      if (error) {
        toast.error('Failed to delete domain');
        return;
      }
      
      toast.success('Domain deleted successfully');
      await loadDomains();
    } catch (error) {
      console.error('Error deleting domain:', error);
      toast.error('Failed to delete domain');
    }
  };

  // Function to validate domain format as user types
  const validateDomain = (domain: string) => {
    if (!domain) return true; // Empty is valid for the input field
    
    const domainRegex = /^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$/;
    return domainRegex.test(domain);
  };

  const isDomainValid = validateDomain(newDomain);

  const dismissVerificationInfo = () => {
    setVerificationInstructions(null);
    setVerificationCode(null);
  };

  return {
    domains,
    newDomain,
    setNewDomain,
    loading,
    checkingVerification,
    verificationInstructions,
    verificationCode,
    isDomainValid,
    handleAddDomain,
    handleVerifyDomain,
    handleDeleteDomain,
    dismissVerificationInfo,
    currentStore
  };
}
