
/**
 * Creates a duplicate-free version of an array, using a callback function 
 * for comparing values or taking a property path for comparison.
 *
 * @param {Array} array - The array to inspect
 * @param {Function|string} [iteratee] - The iteratee invoked per element or property path
 * @returns {Array} Returns the new duplicate free array
 */
function uniqBy(array, iteratee) {
  if (!array || !array.length) return [];
  
  const isIterateeFunction = typeof iteratee === 'function';
  const isIterateeString = typeof iteratee === 'string';
  
  const seen = new Set();
  const result = [];
  
  for (const item of array) {
    let computed;
    
    if (isIterateeFunction) {
      computed = iteratee(item);
    } else if (isIterateeString) {
      computed = item[iteratee];
    } else {
      computed = item;
    }
    
    const key = String(computed);
    if (!seen.has(key)) {
      seen.add(key);
      result.push(item);
    }
  }
  
  return result;
}

// Support both ESM and CJS
export default uniqBy;
