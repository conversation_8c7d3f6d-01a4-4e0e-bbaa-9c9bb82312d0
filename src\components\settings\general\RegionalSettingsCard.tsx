
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface RegionalSettingsCardProps {
  country: string;
  setCountry: (value: string) => void;
  language: string;
  setLanguage: (value: string) => void;
  currency: string;
  setCurrency: (value: string) => void;
  mapProvider: string;
  setMapProvider: (value: string) => void;
}

const RegionalSettingsCard = ({
  country,
  setCountry,
  language,
  setLanguage,
  currency,
  setCurrency,
  mapProvider,
  setMapProvider
}: RegionalSettingsCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Regional Settings</CardTitle>
        <CardDescription>
          Configure regional preferences for your store
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="country">Country/Region</Label>
          <Select defaultValue={country} onValueChange={setCountry}>
            <SelectTrigger>
              <SelectValue placeholder="Select country" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Tanzania">Tanzania</SelectItem>
              <SelectItem value="Kenya">Kenya</SelectItem>
              <SelectItem value="Uganda">Uganda</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="language">Language</Label>
          <Select defaultValue={language} onValueChange={setLanguage}>
            <SelectTrigger>
              <SelectValue placeholder="Select language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="English">English</SelectItem>
              <SelectItem value="Swahili">Swahili</SelectItem>
            </SelectContent>
          </Select>
          <p className="text-xs text-muted-foreground">
            Select the language for your store. This will change the language of your store interface.
          </p>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="currency">Currency</Label>
          <Select defaultValue={currency} onValueChange={setCurrency}>
            <SelectTrigger>
              <SelectValue placeholder="Select currency" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="TZS">TZS</SelectItem>
              <SelectItem value="USD">USD</SelectItem>
              <SelectItem value="KES">KES</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="distance-unit">Distance unit</Label>
          <Select defaultValue="Kilometer">
            <SelectTrigger>
              <SelectValue placeholder="Select distance unit" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Kilometer">Kilometer</SelectItem>
              <SelectItem value="Mile">Mile</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label>Map provider</Label>
          <div className="flex items-center space-x-2">
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="openstreetmap"
                checked={mapProvider === 'openstreetmap'}
                onChange={() => setMapProvider('openstreetmap')}
              />
              <Label htmlFor="openstreetmap">OpenStreetMap</Label>
            </div>
            <div className="flex items-center space-x-2">
              <input
                type="radio"
                id="google"
                checked={mapProvider === 'google'}
                onChange={() => setMapProvider('google')}
              />
              <Label htmlFor="google">Google</Label>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RegionalSettingsCard;
