
import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList,
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb";
import { Card, CardContent } from "@/components/ui/card";
import { CheckCircle, Package, ShoppingBag } from "lucide-react";
import { formatCurrency } from "@/utils/formatters";
import { useAuth } from "@/contexts/auth/AuthContext";
import { supabase } from "@/integrations/supabase/client";

interface OrderDetails {
  id: string;
  total_amount: number;
  created_at: string;
  payment_method: string;
  items: any[]; // Change from Json to any[] to fix type error
  store_id?: string;
  status?: string;
  shipping_address?: any;
  billing_address?: any;
  user_id?: string;
  currency?: string;
  updated_at?: string;
}

const OrderConfirmation: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const [order, setOrder] = useState<OrderDetails | null>(null);
  const [loading, setLoading] = useState(true);
  
  // Get order ID from URL query params
  const searchParams = new URLSearchParams(location.search);
  const orderId = searchParams.get('order_id');
  
  useEffect(() => {
    if (!orderId) {
      // If no order ID, generate a random one for demo purposes
      const demoOrder = {
        id: "ORD-" + Math.floor(100000 + Math.random() * 900000),
        total_amount: 149.99,
        created_at: new Date().toISOString(),
        payment_method: "pesapal",
        items: []
      };
      setOrder(demoOrder);
      setLoading(false);
      return;
    }
    
    const fetchOrder = async () => {
      try {
        // Fetch order details from Supabase
        const { data, error } = await supabase
          .from('orders')
          .select('*')
          .eq('id', orderId)
          .single();
          
        if (error) throw error;
        
        if (data) {
          // Convert items from JSON to array if needed
          const parsedData: OrderDetails = {
            ...data,
            items: [] // Order items stored separately in order_items table
          };
          setOrder(parsedData);
        }
      } catch (error) {
        console.error('Error fetching order:', error);
      } finally {
        setLoading(false);
      }
    };
    
    if (user) {
      fetchOrder();
    }
  }, [orderId, user]);
  
  return (
    <Container className="py-12">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/shop">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Order Confirmation</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      <div className="text-center mb-8">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 mb-4">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h1 className="text-3xl font-bold">Order Confirmed!</h1>
        <p className="text-muted-foreground mt-2">
          Thank you for your purchase. Your order has been received.
        </p>
      </div>
      
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-4">
            <div className="text-center p-4">
              <h2 className="font-semibold mb-1">Order Number</h2>
              <p className="text-muted-foreground">
                {loading ? "Loading..." : (order?.id || "-")}
              </p>
            </div>
            <div className="text-center p-4">
              <h2 className="font-semibold mb-1">Date</h2>
              <p className="text-muted-foreground">
                {loading ? "Loading..." : (
                  order?.created_at 
                    ? new Date(order.created_at).toLocaleDateString() 
                    : new Date().toLocaleDateString()
                )}
              </p>
            </div>
            <div className="text-center p-4">
              <h2 className="font-semibold mb-1">Total</h2>
              <p className="text-muted-foreground">
                {loading ? "Loading..." : formatCurrency(order?.total_amount || 0, "USD")}
              </p>
            </div>
            <div className="text-center p-4">
              <h2 className="font-semibold mb-1">Payment Method</h2>
              <p className="text-muted-foreground">
                {loading ? "Loading..." : (
                  order?.payment_method === "pesapal" ? "Pesapal" :
                  order?.payment_method === "credit-card" ? "Credit Card" :
                  order?.payment_method === "paypal" ? "PayPal" : "Online Payment"
                )}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-center mb-8">
        <Button onClick={() => navigate("/shop")}>
          <ShoppingBag className="mr-2 h-4 w-4" />
          Continue Shopping
        </Button>
        <Button variant="outline" onClick={() => navigate("/shop/account/dashboard")}>
          <Package className="mr-2 h-4 w-4" />
          Track Order
        </Button>
      </div>
      
      <div className="text-center border-t pt-6">
        <h2 className="text-lg font-semibold mb-2">Need Help?</h2>
        <p className="text-muted-foreground mb-4">
          If you have any questions about your order, please contact our customer service.
        </p>
        <Button variant="link" onClick={() => window.location.href = "mailto:<EMAIL>"}>
          <EMAIL>
        </Button>
      </div>
    </Container>
  );
};

export default OrderConfirmation;
