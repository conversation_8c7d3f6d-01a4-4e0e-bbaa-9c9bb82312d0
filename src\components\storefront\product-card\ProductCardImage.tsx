
import React from "react";
import { Badge } from "@/components/ui/badge";
import { Product } from "@/types/unified-product";

interface ProductCardImageProps {
  product: Product;
}

const ProductCardImage: React.FC<ProductCardImageProps> = ({ product }) => {
  // Get the first image or use a placeholder
  const productImage = product.images && product.images.length > 0 
    ? product.images[0] 
    : '/placeholder.svg';

  return (
    <div className="aspect-square relative overflow-hidden bg-gray-100 rounded-t-md">
      <img
        src={productImage}
        alt={product.name}
        className="w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
      />
      
      {/* Out of stock overlay */}
      {product.stock_quantity === 0 && (
        <div className="absolute inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center transition-opacity duration-300">
          <Badge className="bg-white text-black font-medium px-3 py-1.5 shadow-md">
            Out of Stock
          </Badge>
        </div>
      )}
      
      {/* Low stock indicator */}
      {product.stock_quantity > 0 && product.stock_quantity <= 5 && (
        <Badge className="absolute top-2 right-2 bg-amber-500 text-white shadow-sm transition-all duration-300 group-hover:scale-110">
          Only {product.stock_quantity} left
        </Badge>
      )}
      
      {/* Sale tag */}
      {product.tags && product.tags.includes('sale') && (
        <Badge className="absolute top-2 left-2 bg-red-500 text-white shadow-sm transition-all duration-300 group-hover:scale-110">
          Sale
        </Badge>
      )}
    </div>
  );
};

export default ProductCardImage;
