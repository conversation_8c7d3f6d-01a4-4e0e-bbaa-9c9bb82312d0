-- Create missing tables for complete functionality

-- Create store_domains table for custom domain functionality
CREATE TABLE public.store_domains (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  store_id UUID NOT NULL REFERENCES public.stores(id) ON DELETE CASCADE,
  domain TEXT NOT NULL UNIQUE,
  verified B<PERSON>OLEA<PERSON> DEFAULT false,
  verification_code TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create audit_logs table for tracking admin actions
CREATE TABLE public.audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type TEXT NOT NULL,
  actor_id UUID NOT NULL,
  target_user_id UUID,
  details JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Add missing columns to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS name TEXT;
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS email TEXT;

-- Enable RLS on new tables
ALTER TABLE public.store_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for store_domains
CREATE POLICY "Public can view verified domains" ON public.store_domains
  FOR SELECT USING (verified = true);

CREATE POLICY "Store owners can manage their domains" ON public.store_domains
  FOR ALL USING (
    store_id IN (
      SELECT id FROM public.stores 
      WHERE owner_id = auth.uid()
    )
  );

CREATE POLICY "Admins can manage all domains" ON public.store_domains
  FOR ALL USING (get_my_role() = 'admin');

-- Create RLS policies for audit_logs
CREATE POLICY "Admins can view all audit logs" ON public.audit_logs
  FOR SELECT USING (get_my_role() = 'admin');

CREATE POLICY "Admins can insert audit logs" ON public.audit_logs
  FOR INSERT WITH CHECK (get_my_role() = 'admin');

-- Create trigger for updating store_domains updated_at
CREATE TRIGGER update_store_domains_updated_at
  BEFORE UPDATE ON public.store_domains
  FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();