import { supabase } from '@/integrations/supabase/client';

/**
 * Manual email confirmation utility for development
 * This bypasses the email confirmation requirement
 */
export const manualConfirmEmail = async (email: string) => {
  try {
    console.log('Attempting manual email confirmation for:', email);
    
    // First, try to sign in the user to get their session
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: email,
      password: 'temp-password' // This will fail but might give us user info
    });
    
    // Alternative approach: Use the auth API to resend confirmation
    const { error: resendError } = await supabase.auth.resend({
      type: 'signup',
      email: email,
    });
    
    if (resendError) {
      console.log('Resend error (expected):', resendError.message);
    }
    
    // Try to get user session
    const { data: sessionData } = await supabase.auth.getSession();
    console.log('Current session:', sessionData);
    
    return { success: true, message: 'Check console for details' };
  } catch (error) {
    console.error('Manual confirm error:', error);
    return { success: false, error };
  }
};

/**
 * Check if email confirmation is required
 */
export const checkEmailConfirmationStatus = async () => {
  try {
    // Try to get auth settings (this might not work without admin access)
    const { data, error } = await supabase.auth.getSession();
    
    console.log('Auth session check:', { data, error });
    
    return {
      requiresConfirmation: true, // Assume true by default
      session: data.session
    };
  } catch (error) {
    console.error('Status check error:', error);
    return { requiresConfirmation: true, session: null };
  }
};

/**
 * Development helper to bypass email confirmation
 */
export const devBypassEmailConfirmation = async (email: string, password: string) => {
  console.log('🔧 DEVELOPMENT BYPASS ATTEMPT');
  console.log('Email:', email);
  
  try {
    // Try to sign in (this will fail if email not confirmed)
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });
    
    if (error) {
      console.log('Sign in error:', error.message);
      
      if (error.message.includes('Email not confirmed')) {
        console.log('❌ Email confirmation required');
        console.log('🔧 SOLUTION: Disable email confirmation in Supabase Dashboard');
        console.log('📍 Go to: Authentication > Settings > Disable "Enable email confirmations"');
        
        return {
          success: false,
          error: 'Email confirmation required',
          solution: 'Disable email confirmation in Supabase Dashboard'
        };
      }
    }
    
    if (data.user) {
      console.log('✅ Login successful!');
      return { success: true, user: data.user };
    }
    
    return { success: false, error: 'Unknown error' };
  } catch (error) {
    console.error('Bypass error:', error);
    return { success: false, error };
  }
};
