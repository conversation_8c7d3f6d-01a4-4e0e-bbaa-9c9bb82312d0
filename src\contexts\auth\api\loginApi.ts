
import { supabase } from '@/integrations/supabase/client';
import { AuthResponse } from '@supabase/supabase-js';
import { authLogger } from '@/utils/logger';

/**
 * Handle user login with email and password
 */
export const loginUser = async (email: string, password: string): Promise<AuthResponse> => {
  authLogger.info("Attempting to login", { email });
  try {
    // Validate inputs
    if (!email || !password) {
      throw new Error('Email and password are required');
    }

    if (!email.includes('@')) {
      throw new Error('Please enter a valid email address');
    }

    const response = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (response.error) {
      authLogger.error("Login failed", { error: response.error.message, email });

      // Provide user-friendly error messages
      if (response.error.message.includes('Invalid login credentials')) {
        throw new Error('Invalid email or password');
      }

      if (response.error.message.includes('Email not confirmed')) {
        throw new Error('Please confirm your email address before signing in');
      }

      throw response.error;
    }

    authLogger.info("Login successful", { userId: response.data.user?.id, email: response.data.user?.email });
    return response;
  } catch (error: any) {
    authLogger.error('Login error:', error);
    throw error;
  }
};

/**
 * Handle user logout
 */
export const logoutUser = async (): Promise<void> => {
  try {
    console.log("Attempting to logout");
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Logout error:", error);
      throw error;
    }
    console.log("Logout successful");
  } catch (error: any) {
    console.error('Logout error:', error);
    throw error;
  }
};
