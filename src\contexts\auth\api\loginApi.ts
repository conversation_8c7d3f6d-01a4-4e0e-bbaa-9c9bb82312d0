
import { supabase } from '@/integrations/supabase/client';
import { AuthResponse } from '@supabase/supabase-js';

/**
 * Handle user login with email and password
 */
export const loginUser = async (email: string, password: string): Promise<AuthResponse> => {
  console.log("Attempting to login with email:", email);
  try {
    const response = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (response.error) {
      console.error("Login error:", response.error.message);
      throw response.error;
    }
    
    console.log("Login successful, user data:", response.data);
    return response;
  } catch (error: any) {
    console.error('Login error:', error);
    throw error;
  }
};

/**
 * Handle user logout
 */
export const logoutUser = async (): Promise<void> => {
  try {
    console.log("Attempting to logout");
    const { error } = await supabase.auth.signOut();
    if (error) {
      console.error("Logout error:", error);
      throw error;
    }
    console.log("Logout successful");
  } catch (error: any) {
    console.error('Logout error:', error);
    throw error;
  }
};
