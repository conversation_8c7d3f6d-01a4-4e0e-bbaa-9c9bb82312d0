
import React from 'react';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, Truck } from "lucide-react";

const DeliverySettingsHeader: React.FC = () => {
  return (
    <>
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Delivery Settings</h1>
          <p className="text-muted-foreground">
            Configure shipping options and delivery methods for your store.
          </p>
        </div>
        <Truck className="h-8 w-8 text-muted-foreground" />
      </div>

      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Note</AlertTitle>
        <AlertDescription>
          Shipping rates affect your customers' checkout experience. Consider offering free shipping to increase conversion rates.
        </AlertDescription>
      </Alert>
    </>
  );
};

export default DeliverySettingsHeader;
