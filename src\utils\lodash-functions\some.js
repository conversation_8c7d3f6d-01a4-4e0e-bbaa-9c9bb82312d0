
/**
 * Checks if predicate returns truthy for any element of collection.
 * The predicate is invoked with three arguments: (value, index|key, collection).
 *
 * @param {Array|Object} collection - The collection to iterate over
 * @param {Function} predicate - The function invoked per iteration
 * @returns {boolean} Returns true if any element passes the predicate check, else false
 */
function some(collection, predicate) {
  if (!collection) return false;
  
  // Handle arrays
  if (Array.isArray(collection)) {
    for (let i = 0; i < collection.length; i++) {
      if (predicate(collection[i], i, collection)) {
        return true;
      }
    }
    return false;
  }
  
  // Handle objects
  if (typeof collection === 'object') {
    for (const key in collection) {
      if (Object.prototype.hasOwnProperty.call(collection, key)) {
        if (predicate(collection[key], key, collection)) {
          return true;
        }
      }
    }
    return false;
  }
  
  return false;
}

// Support both ESM and CJS
export default some;
