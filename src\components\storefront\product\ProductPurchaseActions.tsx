
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ShoppingCart, Heart } from 'lucide-react';
import { useCart, useWishlist } from '@/contexts';
import { useToast } from '@/hooks/use-toast';
import { Product } from '@/types/unified-product';

interface ProductPurchaseActionsProps {
  product: Product;
}

const ProductPurchaseActions: React.FC<ProductPurchaseActionsProps> = ({ product }) => {
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const { toast } = useToast();
  const [quantity, setQuantity] = useState(1);
  
  const productInWishlist = product ? isInWishlist(product.id || '') : false;
  
  const incrementQuantity = () => {
    if (quantity < (product.stock_quantity || 10)) {
      setQuantity(quantity + 1);
    }
  };
  
  const decrementQuantity = () => {
    if (quantity > 1) {
      setQuantity(quantity - 1);
    }
  };
  
  const handleAddToCart = () => {
    if (product.stock_quantity && product.stock_quantity > 0) {
      addToCart(product, quantity);
      toast({
        title: "Added to cart",
        description: `${quantity} × ${product.name} added to your cart.`
      });
    } else {
      toast({
        title: "Cannot add to cart",
        description: "This product is out of stock.",
        variant: "destructive"
      });
    }
  };
  
  const handleToggleWishlist = () => {
    if (product) {
      if (isInWishlist(product.id || '')) {
        removeFromWishlist(product.id || '');
        toast({
          title: "Removed from wishlist",
          description: `${product.name} has been removed from your wishlist.`
        });
      } else {
        addToWishlist(product);
        toast({
          title: "Added to wishlist",
          description: `${product.name} has been added to your wishlist.`
        });
      }
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-lg font-medium">Quantity</h3>
        <div className="flex items-center">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={decrementQuantity}
            disabled={quantity <= 1 || product.stock_quantity === 0}
          >
            -
          </Button>
          <Input 
            type="number" 
            min="1" 
            max={product.stock_quantity || 10}
            value={quantity}
            onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
            className="w-16 text-center mx-2"
            disabled={product.stock_quantity === 0}
          />
          <Button 
            variant="outline" 
            size="icon" 
            onClick={incrementQuantity}
            disabled={quantity >= (product.stock_quantity || 10) || product.stock_quantity === 0}
          >
            +
          </Button>
        </div>
      </div>
      
      <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-3">
        <Button 
          size="lg" 
          className="flex-1" 
          onClick={handleAddToCart}
          disabled={product.stock_quantity === 0}
        >
          <ShoppingCart className="mr-2 h-5 w-5" />
          Add to Cart
        </Button>
        <Button 
          size="lg" 
          variant={productInWishlist ? "default" : "outline"}
          onClick={handleToggleWishlist}
        >
          <Heart className={`mr-2 h-5 w-5 ${productInWishlist ? 'fill-current' : ''}`} />
          {productInWishlist ? 'In Wishlist' : 'Add to Wishlist'}
        </Button>
      </div>
    </div>
  );
};

export default ProductPurchaseActions;
