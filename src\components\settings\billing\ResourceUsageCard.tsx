
import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Store, 
  Users, 
  Image, 
  MessageCircle, 
  Package 
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

const ResourceItem = ({ 
  title, 
  icon: Icon, 
  current, 
  max 
}: { 
  title: string; 
  icon: React.ElementType; 
  current: string | number; 
  max: string | number 
}) => (
  <div className="border rounded-lg p-4">
    <div className="flex items-center justify-between mb-2">
      <h4 className="font-medium flex items-center gap-1">
        <Icon className="h-4 w-4" /> {title}
      </h4>
      <Badge>{current} / {max}</Badge>
    </div>
    <div className="w-full bg-muted rounded-full h-2">
      <div 
        className="bg-primary h-2 rounded-full" 
        style={{ width: typeof current === 'number' && typeof max === 'number' ? `${(current / max) * 100}%` : "0%" }}
      ></div>
    </div>
  </div>
);

const ResourceUsageCard = () => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" /> 
          Resource Usage
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <ResourceItem title="Projects" icon={Store} current={0} max={1} />
          <ResourceItem title="Team Members" icon={Users} current={1} max={1} />
          <ResourceItem title="Storage" icon={Image} current="0MB" max="100MB" />
          <ResourceItem title="API Calls" icon={MessageCircle} current={0} max={100} />
        </div>
      </CardContent>
    </Card>
  );
};

export default ResourceUsageCard;
