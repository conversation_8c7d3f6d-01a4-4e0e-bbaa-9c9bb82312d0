
import React, { useState, useEffect } from 'react';
import { useStore } from '@/contexts';
import ConnectionStatusCard from './ConnectionStatusCard';
import OrderWorkflowTabs from './OrderWorkflowTabs';
import { WorkflowRule, ActivityItem } from './types';

const OrderWorkflow = () => {
  const { currentStore } = useStore();
  const [activeTab, setActiveTab] = useState('workflows');
  const [workflowRules, setWorkflowRules] = useState<WorkflowRule[]>([
    {
      id: 'rule_1',
      name: 'Order Confirmation',
      trigger: 'order_placed',
      actions: ['send_whatsapp_message', 'send_email'],
      status: 'active',
      lastTriggered: '15 minutes ago'
    },
    {
      id: 'rule_2',
      name: 'Order Processing',
      trigger: 'order_processing',
      actions: ['send_whatsapp_message'],
      status: 'active',
      lastTriggered: '2 hours ago'
    },
    {
      id: 'rule_3',
      name: 'Shipping Notification',
      trigger: 'order_shipped',
      actions: ['send_whatsapp_message', 'send_email', 'update_customer_record'],
      status: 'active',
      lastTriggered: '1 day ago'
    },
    {
      id: 'rule_4',
      name: 'Abandoned Cart',
      trigger: 'cart_abandoned',
      actions: ['send_whatsapp_message'],
      status: 'draft',
      lastTriggered: 'Never'
    }
  ]);
  
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([
    {
      id: 'act_1',
      ruleName: 'Order Confirmation',
      customer: 'Jane Smith',
      timestamp: '15 minutes ago',
      status: 'success'
    },
    {
      id: 'act_2',
      ruleName: 'Order Confirmation',
      customer: 'Michael Johnson',
      timestamp: '32 minutes ago',
      status: 'success'
    },
    {
      id: 'act_3',
      ruleName: 'Shipping Notification',
      customer: 'Robert Williams',
      timestamp: '2 hours ago',
      status: 'success'
    },
    {
      id: 'act_4',
      ruleName: 'Order Processing',
      customer: 'Sarah Davis',
      timestamp: '3 hours ago',
      status: 'failed'
    }
  ]);
  
  // Simulate checking connection status
  useEffect(() => {
    const checkConnection = () => {
      setConnectionStatus('connecting');
      setTimeout(() => {
        setConnectionStatus('connected');
      }, 1500);
    };
    
    checkConnection();
    const interval = setInterval(checkConnection, 120000);
    
    return () => clearInterval(interval);
  }, []);
  
  return (
    <div className="space-y-6">
      {/* Connection Status Card */}
      <ConnectionStatusCard connectionStatus={connectionStatus} />
      
      {/* Tabs for different sections */}
      <OrderWorkflowTabs 
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        workflowRules={workflowRules}
        setWorkflowRules={setWorkflowRules}
        recentActivity={recentActivity}
      />
    </div>
  );
};

export default OrderWorkflow;
