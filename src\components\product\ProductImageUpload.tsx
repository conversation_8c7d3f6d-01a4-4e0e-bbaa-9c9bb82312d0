
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertCircle, Upload, X, Image as ImageIcon } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ProductImageUploadProps {
  images: string[];
  onChange: (images: string[]) => void;
  maxImages?: number;
}

const ProductImageUpload: React.FC<ProductImageUploadProps> = ({
  images = [],
  onChange,
  maxImages = 5,
}) => {
  const [error, setError] = useState<string | null>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;
    
    setError(null);
    
    // Check if adding new files would exceed the maximum
    if (images.length + files.length > maxImages) {
      setError(`You can upload a maximum of ${maxImages} images`);
      return;
    }
    
    // Process each file
    Array.from(files).forEach(file => {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        setError('Only image files are allowed');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        setError('Image size should not exceed 5MB');
        return;
      }
      
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          const newImages = [...images, event.target.result.toString()];
          onChange(newImages);
        }
      };
      reader.readAsDataURL(file);
    });
    
    // Reset the input
    e.target.value = '';
  };
  
  const removeImage = (index: number) => {
    const newImages = [...images];
    newImages.splice(index, 1);
    onChange(newImages);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Input
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
          id="product-image-upload"
          multiple
          disabled={images.length >= maxImages}
        />
        <Button
          type="button"
          variant="outline"
          onClick={() => document.getElementById('product-image-upload')?.click()}
          disabled={images.length >= maxImages}
        >
          <Upload className="mr-2 h-4 w-4" />
          Upload Images
        </Button>
        <p className="text-sm text-muted-foreground">
          {images.length} of {maxImages} images
        </p>
      </div>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      {images.length > 0 ? (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {images.map((src, index) => (
            <div key={index} className="relative group aspect-square border rounded-md overflow-hidden">
              <img
                src={src}
                alt={`Product image ${index + 1}`}
                className="w-full h-full object-cover"
              />
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute top-2 right-2 bg-black/70 text-white p-1 rounded-full opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          ))}
        </div>
      ) : (
        <div className="border border-dashed rounded-md p-8 flex flex-col items-center justify-center text-muted-foreground">
          <ImageIcon className="h-12 w-12 mb-2" />
          <p>No images uploaded yet</p>
          <p className="text-sm">Upload images to showcase your product</p>
        </div>
      )}
    </div>
  );
};

export default ProductImageUpload;
