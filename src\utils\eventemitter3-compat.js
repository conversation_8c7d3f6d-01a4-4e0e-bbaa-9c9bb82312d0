
/**
 * This module provides a compatibility layer for the eventemitter3 package.
 * It's a simple implementation that mimics the basic functionality of eventemitter3.
 */

class EventEmitter {
  constructor() {
    this._events = {};
  }

  on(event, listener) {
    if (!this._events[event]) {
      this._events[event] = [];
    }
    this._events[event].push(listener);
    return this;
  }

  off(event, listener) {
    if (!this._events[event]) return this;
    
    if (!listener) {
      delete this._events[event];
      return this;
    }
    
    const idx = this._events[event].indexOf(listener);
    if (idx !== -1) {
      this._events[event].splice(idx, 1);
    }
    
    if (this._events[event].length === 0) {
      delete this._events[event];
    }
    
    return this;
  }

  once(event, listener) {
    const onceWrapper = (...args) => {
      this.off(event, onceWrapper);
      listener.apply(this, args);
    };
    
    return this.on(event, onceWrapper);
  }

  emit(event, ...args) {
    if (!this._events[event]) return false;
    
    const listeners = [...this._events[event]];
    for (const listener of listeners) {
      listener.apply(this, args);
    }
    
    return true;
  }

  removeAllListeners(event) {
    if (event) {
      delete this._events[event];
    } else {
      this._events = {};
    }
    
    return this;
  }

  listeners(event) {
    return this._events[event] || [];
  }

  listenerCount(event) {
    return this._events[event] ? this._events[event].length : 0;
  }
}

// Export both as default and as named export for compatibility
const EE = EventEmitter;

// Support ESM
export { EventEmitter };
export default EventEmitter;

// Support CommonJS
if (typeof module === 'object' && module.exports) {
  module.exports = Object.assign(EventEmitter, { EventEmitter, default: EventEmitter });
}
