
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useStore } from '@/contexts';

interface SidebarHeaderProps {
  isDashboardPage?: boolean;
}

const SidebarHeader: React.FC<SidebarHeaderProps> = ({ isDashboardPage }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentStore } = useStore();
  
  // Determine if we're on the dashboard page or not
  const isOnDashboard = isDashboardPage || location.pathname === '/dashboard';

  return (
    <div className="p-6 space-y-2">
      <h2 className="text-lg font-semibold">
        {currentStore?.name || 'Store Dashboard'}
      </h2>
      <p className="text-sm text-muted-foreground">
        Manage your store
      </p>
      
      {/* Return to Dashboard button - only show when not on dashboard */}
      {!isOnDashboard && (
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full mt-4 justify-start" 
          onClick={() => navigate('/dashboard')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Return to Dashboard
        </Button>
      )}
    </div>
  );
};

export default SidebarHeader;
