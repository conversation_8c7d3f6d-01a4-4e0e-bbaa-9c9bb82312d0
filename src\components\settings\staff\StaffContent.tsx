
import React from 'react';
import StaffList from './StaffList';
import { StaffMember } from './types';
import { ScrollArea } from '@/components/ui/scroll-area';

interface StaffContentProps {
  staffMembers: StaffMember[];
  isLoading: boolean;
  onEdit: (staff: StaffMember) => void;
  onDelete: (staff: StaffMember) => void;
}

const StaffContent: React.FC<StaffContentProps> = ({ 
  staffMembers, 
  isLoading, 
  onEdit, 
  onDelete 
}) => {
  return (
    <div className="rounded-md border bg-card">
      {isLoading ? (
        <div className="py-10 text-center">Loading staff members...</div>
      ) : (
        <ScrollArea className="h-[calc(100vh-200px)]">
          <StaffList 
            staffMembers={staffMembers} 
            onEdit={onEdit} 
            onDelete={onDelete} 
          />
        </ScrollArea>
      )}
    </div>
  );
};

export default StaffContent;
