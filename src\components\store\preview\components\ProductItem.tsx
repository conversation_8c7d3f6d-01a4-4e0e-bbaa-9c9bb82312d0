
import React from 'react';
import { ProductItemProps } from '../types/product-section';
import { Product } from '@/types/unified-product';
import ProductBadges from './ProductBadges';
import QuickActions from './QuickActions';

const ProductItem: React.FC<ProductItemProps> = ({
  product,
  showRatings,
  showQuickAdd,
  layoutType,
  cornerRadius,
  fontFamily,
  darkMode,
  buttonClasses,
  primaryColor,
  imageClasses,
  displayCurrency = true,
  enableNewBadge = true,
  enableShareButtons = true,
}) => {
  // Check if product is new (created within the last 7 days)
  const isNew = enableNewBadge && new Date(product.created_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  return (
    <div 
      className={`group relative ${layoutType === 'grid' ? '' : 'flex gap-4'}`}
    >
      {/* Product Image */}
      <div 
        className={`relative overflow-hidden ${cornerRadius > 0 ? `rounded-${cornerRadius}` : ''} ${layoutType === 'grid' ? 'aspect-square w-full' : 'w-1/3'} ${imageClasses}`}
      >
        <img
          src={product.images?.[0] || '/placeholder.svg'}
          alt={product.name}
          className="w-full h-full object-cover"
        />
        
        {/* Product Badges */}
        <ProductBadges 
          product={product} 
          isNew={isNew} 
          primaryColor={primaryColor} 
        />
        
        {/* Quick Action Buttons */}
        <QuickActions 
          showQuickAdd={showQuickAdd} 
          enableShareButtons={enableShareButtons} 
        />
      </div>
      
      {/* Product Info */}
      <div className={`${layoutType === 'grid' ? 'mt-3' : 'flex-1'}`}>
        <h3 className={`font-${fontFamily} text-base font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
          {product.name}
        </h3>
        
        {showRatings && (
          <div className="flex items-center mt-1">
            {/* Stars would go here */}
            <span className={`text-xs ml-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              (4.5)
            </span>
          </div>
        )}
        
        <div className="mt-1">
          <span className={`font-${fontFamily} font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            {displayCurrency && product.currency} {product.price}
          </span>
          
          {product.sale_price && (
            <span className={`ml-2 line-through text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
              {displayCurrency && product.currency} {product.sale_price}
            </span>
          )}
        </div>
        
        {layoutType !== 'grid' && (
          <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'} line-clamp-2`}>
            {product.description}
          </p>
        )}
      </div>
    </div>
  );
};

export default ProductItem;
