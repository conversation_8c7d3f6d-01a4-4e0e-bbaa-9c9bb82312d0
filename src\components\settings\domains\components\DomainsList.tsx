
import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { CheckCircle, XCircle, RefreshCw, ExternalLink, Trash2 } from 'lucide-react';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Domain } from '../hooks/useDomains';

interface DomainsListProps {
  domains: Domain[];
  checkingVerification: boolean;
  handleVerifyDomain: (domainId: string, domain: string) => Promise<void>;
  handleDeleteDomain: (domainId: string) => Promise<void>;
}

export const DomainsList: React.FC<DomainsListProps> = ({
  domains,
  checkingVerification,
  handleVerifyDomain,
  handleDeleteDomain
}) => {
  if (domains.length === 0) {
    return (
      <div className="border rounded-md p-4 text-center text-muted-foreground">
        No domains added yet. Add a domain to get started.
      </div>
    );
  }

  return (
    <div className="space-y-2 mt-6">
      <h3 className="font-semibold">Your Domains</h3>
      <div className="border rounded-md divide-y">
        {domains.map((domain) => (
          <div key={domain.id} className="p-3 flex items-center justify-between">
            <div className="flex items-center gap-2">
              {domain.verified ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : (
                <XCircle className="h-5 w-5 text-red-500" />
              )}
              <span className="font-medium">{domain.domain}</span>
            </div>
            <div className="flex items-center gap-2">
              {domain.verified && (
                <a 
                  href={`https://${domain.domain}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-green-500 text-white p-1 rounded hover:bg-green-600 mr-1"
                  title="View store with this domain"
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              )}
              {!domain.verified && (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleVerifyDomain(domain.id, domain.domain)}
                  disabled={checkingVerification}
                >
                  {checkingVerification ? <RefreshCw className="h-4 w-4 animate-spin" /> : 'Verify'}
                </Button>
              )}
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="ghost" size="sm" className="text-red-500 hover:text-red-600">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will remove the domain {domain.domain} from your store.
                      Any links using this domain will stop working immediately.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction 
                      className="bg-red-500 hover:bg-red-600"
                      onClick={() => handleDeleteDomain(domain.id)}
                    >
                      Delete domain
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
