
import React from 'react';
import { StoreFormData } from '@/types/store';
import { 
  ResizablePanel, 
  ResizablePanelGroup, 
  ResizableHandle 
} from '@/components/ui/resizable';
import { Button } from '@/components/ui/button';
import { ArrowRight, Eye } from 'lucide-react';
import { StorePreview } from '@/components/store/preview/StorePreview';
import { ProductProvider } from '@/contexts/product/ProductContext';

interface PreviewPanelProps {
  data: StoreFormData;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const PreviewPanel: React.FC<PreviewPanelProps> = ({ data, open, onOpenChange }) => {
  return (
    <ResizablePanelGroup direction="horizontal" className="min-h-[500px] rounded-lg border">
      <ResizablePanel defaultSize={open ? 40 : 100}>
        <div className="p-6 h-full">
          {/* Content will be filled by parent */}
          {open ? (
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              className="mb-4 flex items-center gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              <span>Close Preview</span>
            </Button>
          ) : null}
          {!open && (
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(true)}
              className="flex items-center gap-2 mt-4"
            >
              <Eye className="h-4 w-4" />
              <span>Preview Storefront</span>
            </Button>
          )}
        </div>
      </ResizablePanel>
      
      {open && (
        <>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={60}>
            <ProductProvider>
              <div className="overflow-auto h-full">
                <StorePreview data={data} />
              </div>
            </ProductProvider>
          </ResizablePanel>
        </>
      )}
    </ResizablePanelGroup>
  );
};

export default PreviewPanel;
