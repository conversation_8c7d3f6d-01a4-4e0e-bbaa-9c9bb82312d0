
import React from "react";
import { Outlet, Navigate, Link, useLocation } from "react-router-dom";
import { useAuth } from "@/contexts";
import { Container } from "@/components/ui/container";
import { Card } from "@/components/ui/card";
import { 
  User2, 
  ListOrdered, 
  Heart, 
  Settings2, 
  MapPin,
  LayoutDashboard
} from "lucide-react";
import { cn } from "@/lib/utils";
import { isStoreSubdomain } from "@/utils/authRedirects";

const Account: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  
  // Determine if we're in the shop section on the main domain or on a storefront subdomain
  const isStorefront = isStoreSubdomain();
  const isShopPath = location.pathname.includes('/shop/');
  
  // Base path will be different depending on context
  const basePath = isStorefront ? "/account" : 
                  isShopPath ? "/shop/account" : "/account";

  console.log("Account component rendered with:", {
    isStorefront,
    isShopPath,
    basePath,
    location: location.pathname,
    userAuthenticated: !!user
  });

  if (!user) {
    console.log("User not authenticated, redirecting to signin");
    return <Navigate to="/signin" state={{ from: location }} replace />;
  }

  const navigation = [
    { name: "Dashboard", href: `${basePath}`, icon: LayoutDashboard, exact: true },
    { name: "Orders", href: `${basePath}/orders`, icon: ListOrdered },
    { name: "Wishlist", href: `${basePath}/wishlist`, icon: Heart },
    { name: "Addresses", href: `${basePath}/addresses`, icon: MapPin },
    { name: "Profile", href: `${basePath}/profile`, icon: User2 },
    { name: "Settings", href: `${basePath}/settings`, icon: Settings2 },
  ];

  const isLinkActive = (path: string, exact = false) => {
    if (exact) {
      return location.pathname === path;
    }
    return location.pathname === path || 
           (path !== basePath && location.pathname.startsWith(path));
  };

  return (
    <Container className="py-8">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
        {/* Sidebar Navigation */}
        <Card className="p-4 h-fit md:sticky md:top-4">
          <div className="space-y-1">
            <h2 className="text-xl font-semibold mb-4 px-2">My Account</h2>
            <nav className="space-y-1">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  to={item.href}
                  className={cn(
                    "flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",
                    isLinkActive(item.href, item.exact)
                      ? "bg-primary text-primary-foreground"
                      : "text-muted-foreground hover:text-foreground hover:bg-secondary"
                  )}
                >
                  <item.icon className="mr-3 h-5 w-5 flex-shrink-0" aria-hidden="true" />
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        </Card>

        {/* Main Content */}
        <Card className="p-6 md:col-span-3">
          <Outlet />
        </Card>
      </div>
    </Container>
  );
};

export default Account;
