
import { useState, useCallback, useEffect } from 'react';
import { Order, OrderStatus } from '@/types/order';
import { fetchOrdersByStore, updateOrderStatusInDb } from './orderApi';
import { toast } from 'sonner';
import { useAuth, useStore } from '@/contexts';

export const useOrderProvider = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user, isAuthenticated } = useAuth();
  const { currentStore } = useStore();

  const fetchOrders = useCallback(async (storeId: string) => {
    if (!isAuthenticated || !user) {
      console.log("User not authenticated, skipping order fetch");
      setOrders([]);
      return;
    }

    setIsLoading(true);
    try {
      console.log("Fetching orders for store:", storeId);
      const fetchedOrders = await fetchOrdersByStore(storeId);
      console.log("Fetched orders:", fetchedOrders.length);
      setOrders(fetchedOrders);
    } catch (error) {
      console.error("Error fetching orders:", error);
      toast.error("Could not load orders. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user]);

  // Load orders when store changes
  useEffect(() => {
    if (currentStore?.id) {
      fetchOrders(currentStore.id);
    }
  }, [currentStore?.id, fetchOrders]);

  const updateOrderStatus = async (orderId: string, status: OrderStatus) => {
    try {
      await updateOrderStatusInDb(orderId, status);
      
      // Update local state
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === orderId 
            ? { ...order, status, updatedAt: new Date() } 
            : order
        )
      );
      
      toast.success(`Order status updated to ${status}`);
    } catch (error) {
      console.error("Error updating order status:", error);
      toast.error("Failed to update order status. Please try again.");
      throw error;
    }
  };

  const getOrderById = (orderId: string) => {
    return orders.find(order => order.id === orderId);
  };

  return {
    orders,
    isLoading,
    fetchOrders,
    updateOrderStatus,
    getOrderById
  };
};
