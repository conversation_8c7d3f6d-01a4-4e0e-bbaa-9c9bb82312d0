
import React, { useState } from 'react';
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { LucideIcon } from 'lucide-react';
import FeatureItem from './FeatureItem';
import PriceDisplay from './PriceDisplay';
import { cn } from '@/lib/utils';
import { initiatePabblyCheckout } from '@/utils/pabblyCheckout';
import { useToast } from '@/hooks/use-toast';

export interface PlanCardProps {
  title: string;
  description: string;
  price: number;
  period: string;
  features: string[];
  icon: LucideIcon;
  badgeText?: string;
  badgeColor?: string;
  buttonText: string;
  buttonVariant?: "default" | "outline" | "premium";
  isPopular?: boolean;
  annualPrice?: number;
  annualDiscount?: string;
  priceLabel?: string;
  pricePrefix?: string;
  showCancelText?: boolean;
  yearlyPrice?: number;
  yearlyDiscount?: string;
}

interface PlanCardComponentProps extends PlanCardProps {
  isYearly?: boolean;
  buttonAction?: React.ReactNode;
  pabblyPlanId?: string;
}

const PlanCard = ({
  title,
  description,
  price,
  period,
  features,
  icon: Icon,
  badgeText,
  badgeColor,
  buttonText,
  buttonVariant = "default",
  isPopular = false,
  annualPrice,
  annualDiscount,
  priceLabel,
  pricePrefix,
  showCancelText = false,
  yearlyPrice,
  yearlyDiscount,
  isYearly = false,
  buttonAction,
  pabblyPlanId,
}: PlanCardComponentProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  
  const displayPrice = isYearly && yearlyPrice !== undefined ? yearlyPrice : price;
  const displayPeriod = isYearly ? "year" : period;
  const displayDiscount = isYearly ? yearlyDiscount : undefined;

  const handleSubscribe = async () => {
    if (!pabblyPlanId) return;
    
    setIsLoading(true);
    try {
      await initiatePabblyCheckout(pabblyPlanId);
    } catch (error) {
      console.error('Payment popup error:', error);
      toast({
        title: "Payment popup failed to load",
        description: "Please try again or contact customer support if the problem persists.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className={cn(
      "relative h-full transition-all duration-300 hover:shadow-md",
      isPopular ? 'border-primary/70 shadow-lg shadow-primary/10' : '',
    )}>
      {(badgeText || isPopular) && (
        <div className="absolute -top-3 left-0 w-full flex justify-center">
          <div className={cn(
            "rounded-full px-3 py-1 text-xs font-medium",
            isPopular ? 'bg-primary/10 text-primary' : badgeColor
          )}>
            {isPopular ? 'Popular' : badgeText}
          </div>
        </div>
      )}
      
      <div className="p-6 pt-8 flex flex-col h-full">
        <div className="mb-6 text-center">
          <div className="mx-auto bg-primary/10 w-12 h-12 flex items-center justify-center rounded-full mb-4">
            <Icon className="h-6 w-6 text-primary" />
          </div>
          <h3 className="text-xl font-semibold mb-2">{title}</h3>
          <p className="text-muted-foreground text-sm">{description}</p>
        </div>
        
        <div className="text-center mb-6">
          <PriceDisplay 
            price={displayPrice} 
            period={displayPeriod} 
            priceLabel={priceLabel}
            discount={displayDiscount}
          />
        
          {pricePrefix && (
            <p className="text-sm font-medium mt-2">{pricePrefix}</p>
          )}
        </div>
        
        <div className="flex-grow border-t pt-6">
          <ul className="space-y-3">
            {features.map((feature, index) => (
              <FeatureItem key={index}>{feature}</FeatureItem>
            ))}
          </ul>
        </div>
      </div>
      
      <CardFooter className="p-6 pt-0 flex flex-col">
        {buttonAction ? (
          <Button 
            className={cn(
              "w-full font-medium", 
              buttonVariant === "premium" && "bg-purple-600 hover:bg-purple-700",
              isPopular && buttonVariant !== "premium" && "bg-primary hover:bg-primary/90"
            )} 
            variant={buttonVariant === "premium" ? "default" : buttonVariant}
            asChild
          >
            {buttonAction}
          </Button>
        ) : (
          <Button 
            className={cn(
              "w-full font-medium", 
              buttonVariant === "premium" && "bg-purple-600 hover:bg-purple-700",
              isPopular && buttonVariant !== "premium" && "bg-primary hover:bg-primary/90"
            )} 
            variant={buttonVariant === "premium" ? "default" : buttonVariant}
            onClick={pabblyPlanId ? handleSubscribe : undefined}
            disabled={isLoading}
          >
            {isLoading ? "Loading..." : buttonText}
          </Button>
        )}
        
        {showCancelText && (
          <p className="text-center text-xs text-muted-foreground mt-3">Cancel any time</p>
        )}
      </CardFooter>
    </Card>
  );
};

export default PlanCard;
