
import { supabase } from '@/integrations/supabase/client';
import { CustomerType } from '@/types/customer';
import { toast } from 'sonner';

// Helper function to convert DB data to Customer format
export const formatCustomerFromDb = (data: any): CustomerType => ({
  id: data.id,
  name: data.name || 'Unknown',
  email: data.email || '',
  totalOrders: data.total_orders || 0,
  totalSpent: data.total_spent || 0,
  // Convert Date to string to match CustomerType
  lastOrderDate: data.last_order_date ? new Date(data.last_order_date).toISOString() : '',
  status: data.status || 'inactive',
  avatar: data.avatar || null,
});

// Mock data for testing or development
const MOCK_CUSTOMERS: CustomerType[] = [
  {
    id: 'mock-customer-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    totalOrders: 5,
    totalSpent: 250,
    lastOrderDate: new Date().toISOString(),
    status: 'active',
    avatar: null
  },
  {
    id: 'mock-customer-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    totalOrders: 3,
    totalSpent: 180,
    lastOrderDate: new Date().toISOString(),
    status: 'active',
    avatar: null
  },
  {
    id: 'mock-customer-3',
    name: 'Alice <PERSON>',
    email: '<EMAIL>',
    totalOrders: 8,
    totalSpent: 420,
    lastOrderDate: new Date().toISOString(),
    status: 'active',
    avatar: null
  }
];

// Fetch all customers for a store
export const fetchStoreCustomers = async (storeId: string): Promise<CustomerType[]> => {
  try {
    if (!storeId) {
      console.warn('No store ID provided to fetchStoreCustomers');
      return [];
    }

    // For development or when using mock IDs, return mock data
    if (storeId === 'mock-store-1' || storeId.startsWith('mock-')) {
      console.log('Using mock customer data for development');
      return MOCK_CUSTOMERS;
    }

    // First, get customer IDs from all orders for this store
    const { data: orderData, error: orderError } = await supabase
      .from('orders')
      .select('user_id, total_amount, created_at')
      .eq('store_id', storeId);

    if (orderError) {
      console.error('Error fetching orders:', orderError);
      throw orderError;
    }

    if (!orderData || orderData.length === 0) {
      console.log('No orders found for store:', storeId);
      // Return empty array instead of throwing error for no data
      return [];
    }

    // Process order data to calculate customer metrics
    const customerMetrics: Record<string, { 
      totalOrders: number, 
      totalSpent: number, 
      lastOrderDate: string 
    }> = {};

    orderData.forEach(order => {
      const userId = order.user_id;
      
      if (!userId) return;
      
      if (!customerMetrics[userId]) {
        customerMetrics[userId] = {
          totalOrders: 0,
          totalSpent: 0,
          lastOrderDate: order.created_at
        };
      }
      
      customerMetrics[userId].totalOrders += 1;
      customerMetrics[userId].totalSpent += Number(order.total_amount);
      
      // Update last order date if current order is more recent
      if (new Date(order.created_at) > new Date(customerMetrics[userId].lastOrderDate)) {
        customerMetrics[userId].lastOrderDate = order.created_at;
      }
    });

    // Get customer profiles for these users
    const customerIds = Object.keys(customerMetrics);
    
    if (customerIds.length === 0) {
      console.log('No customer IDs extracted from orders');
      return [];
    }
    
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('id, name, email')
      .in('id', customerIds);

    if (profileError) {
      console.error('Error fetching profiles:', profileError);
      throw profileError;
    }

    if (!profileData || profileData.length === 0) {
      console.log('No profile data found for customers');
      return [];
    }

    // Combine profile data with metrics
    const customers: CustomerType[] = profileData.map(profile => ({
      id: profile.id,
      name: profile.name || 'Unknown',
      email: profile.email || '',
      totalOrders: customerMetrics[profile.id]?.totalOrders || 0,
      totalSpent: customerMetrics[profile.id]?.totalSpent || 0,
      // Store as string rather than Date object to match CustomerType
      lastOrderDate: customerMetrics[profile.id]?.lastOrderDate || '',
      status: 'active', // Customers with orders are considered active
      avatar: null
    }));

    return customers;
  } catch (error) {
    console.error('Failed to load customers', error);
    // Return empty array instead of throwing to prevent crashing the UI
    return [];
  }
};
