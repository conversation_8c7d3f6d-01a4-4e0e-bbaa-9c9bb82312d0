
import { useState, useEffect } from 'react';
import { generateUrlFromName, checkStoreUrlAvailability } from '@/utils/storeHelpers';
import { toast } from 'sonner';

export const useStoreUrlValidation = (
  storeUrl: string,
  setStoreUrl: (value: string) => void,
  storeName: string
) => {
  const [manuallyEdited, setManuallyEdited] = useState(false);
  const [originalUrl, setOriginalUrl] = useState(storeUrl);
  const [urlCheckStatus, setUrlCheckStatus] = useState<'checking' | 'available' | 'unavailable' | 'error' | null>(null);
  const [urlSuggestion, setUrlSuggestion] = useState<string | null>(null);

  // Update store URL when store name changes, unless manually edited
  useEffect(() => {
    if (storeName && !manuallyEdited) {
      const generatedUrl = generateUrlFromName(storeName);
      setStoreUrl(generatedUrl);
    }
  }, [storeName, manuallyEdited, setStoreUrl]);

  // Check URL availability when storeUrl changes
  useEffect(() => {
    if (storeUrl === originalUrl) {
      setUrlCheckStatus(null);
      return;
    }
    
    const checkUrlAvailability = async () => {
      if (!storeUrl || storeUrl.length < 2) {
        setUrlCheckStatus(null);
        setUrlSuggestion(null);
        return;
      }

      setUrlCheckStatus('checking');
      
      try {
        const result = await checkStoreUrlAvailability(storeUrl);
        
        if (result.error) {
          setUrlCheckStatus('error');
          console.error(result.error);
          return;
        }
        
        if (result.available) {
          setUrlCheckStatus('available');
          setUrlSuggestion(null);
        } else {
          setUrlCheckStatus('unavailable');
          setUrlSuggestion(result.suggestion || null);
          
          // If there's a sanitized version that's different from the input, update the form
          if (result.sanitized && result.sanitized !== storeUrl) {
            setStoreUrl(result.sanitized);
            toast.info('The URL has been sanitized to remove invalid characters');
          }
        }
      } catch (error) {
        console.error('Error checking URL availability:', error);
        setUrlCheckStatus('error');
      }
    };

    const timeoutId = setTimeout(() => {
      checkUrlAvailability();
    }, 500); // Debounce URL checks

    return () => clearTimeout(timeoutId);
  }, [storeUrl, originalUrl, setStoreUrl]);

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setManuallyEdited(true);
    setStoreUrl(e.target.value);
  };

  const handleStoreNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    const currentLength = storeName.length;
    
    // Reset the manually edited flag if the name changes significantly
    if (Math.abs(newName.length - currentLength) > 2) {
      setManuallyEdited(false);
    }
    
    return newName;
  };

  const handleUseSuggestion = () => {
    if (urlSuggestion) {
      setStoreUrl(urlSuggestion);
      setManuallyEdited(true);
    }
  };

  return {
    urlCheckStatus,
    urlSuggestion,
    handleUrlChange,
    handleStoreNameChange,
    handleUseSuggestion
  };
};
