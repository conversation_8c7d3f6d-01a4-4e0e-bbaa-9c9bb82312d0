// List of African countries for reference
// TODO: Consider if this list also needs to be dynamic or fetched.
export const africanCountries = [
  'Kenya', 'Tanzania', 'Uganda', 
  'Nigeria', 'Ghana', 'South Africa', 
  'Egypt', 'Morocco', 'Ethiopia', 
  'Rwanda', 'Senegal'
];

// Helper function to check if a country is in the list of African countries
export const isAfricanCountry = (country: string): boolean => {
  // Assuming country names match those in africanCountries array
  return africanCountries.includes(country);
};

// Helper to get country code from country name
// TODO: Consider generating this mapping dynamically or fetching it.
export const getCountryCode = (country: string): string => {
  switch(country) {
    case 'Kenya': return 'KE';
    case 'Tanzania': return 'TZ';
    case 'Uganda': return 'UG';
    case 'Nigeria': return 'NG';
    case 'Ghana': return 'GH';
    case 'South Africa': return 'ZA';
    case 'Egypt': return 'EG';
    case 'Morocco': return 'MA';
    case 'Ethiopia': return 'ET';
    case 'Rwanda': return 'RW';
    case 'Senegal': return 'SN';
    default: return ''; // Return empty string or handle error for non-African countries
  }
};
