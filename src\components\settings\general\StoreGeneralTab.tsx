
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useStore } from '@/contexts';
import { useStoreGeneralTabState } from '@/hooks/settings/useStoreGeneralTabState';
import { toast } from 'sonner';
import { HexColorPicker } from 'react-colorful';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { uploadStoreLogoToStorage } from '@/utils/storageHelpers';
import { databaseToUIStore } from '@/utils/typeConverters';

const StoreGeneralTab = () => {
  const { currentStore, updateStore } = useStore();
  const [loading, setLoading] = useState(false);
  
  // Convert database store to UI store to ensure theme_options is available
  const uiStore = databaseToUIStore(currentStore);
  
  const storeState = useStoreGeneralTabState(uiStore);
  
  const [primaryColor, setPrimaryColor] = useState(
    uiStore?.theme_options?.colorScheme === 'custom' 
      ? uiStore?.theme_options?.primaryColor || '#10b981' 
      : '#10b981'
  );
  const [heroType, setHeroType] = useState(
    uiStore?.theme_options?.heroStyle || 'image'
  );
  const [shopLayout, setShopLayout] = useState(
    uiStore?.theme_options?.layoutType || 'grid'
  );
  const [displayCurrency, setDisplayCurrency] = useState(
    uiStore?.theme_options?.displayCurrency !== false
  );
  const [showProductCount, setShowProductCount] = useState(
    uiStore?.theme_options?.showProductCount !== false
  );
  const [enableNewBadge, setEnableNewBadge] = useState(
    uiStore?.theme_options?.enableNewBadge !== false
  );
  const [enableShareButtons, setEnableShareButtons] = useState(
    uiStore?.theme_options?.enableShareButtons !== false
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      let logoUrl = currentStore?.logo_url;
      
      if (storeState.logo instanceof File) {
        logoUrl = await uploadStoreLogoToStorage(storeState.logo, currentStore?.id);
      }
      
      await updateStore(currentStore?.id || '', {
        name: storeState.storeName,
        themeOptions: {
          ...((currentStore?.theme as any) || {}),
          colorScheme: 'custom',
          primaryColor,
          heroStyle: heroType,
          layoutType: shopLayout,
          displayCurrency,
          showProductCount,
          enableNewBadge,
          enableShareButtons,
          cornerRadius: 8,
        },
      });

      // Update logo separately
      if (logoUrl) {
        await updateStore(currentStore?.id || '', {
          logo_url: logoUrl
        });
      }
      
      toast.success('Store details updated successfully!');
    } catch (error) {
      toast.error('Failed to update store details');
      console.error('Store update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Store Details</CardTitle>
          <CardDescription>
            Configure general store settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="store-name">Store Name</Label>
              <Input
                id="store-name"
                value={storeState.storeName}
                onChange={(e) => storeState.setStoreName(e.target.value)}
                placeholder="Enter your store name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="order-prefix">Order Prefix</Label>
              <Input
                id="order-prefix"
                value={storeState.orderPrefix}
                onChange={(e) => storeState.setOrderPrefix(e.target.value)}
                placeholder="e.g., ORD-"
              />
              <p className="text-xs text-muted-foreground">
                This prefix will be added to all order numbers (e.g., ORD-12345)
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="meta-title">Default Meta Title</Label>
              <Input
                id="meta-title"
                value={storeState.defaultMetaTitle}
                onChange={(e) => storeState.setDefaultMetaTitle(e.target.value)}
                placeholder="Enter default meta title"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="meta-description">Default Meta Description</Label>
              <Textarea
                id="meta-description"
                value={storeState.defaultMetaDescription}
                onChange={(e) => storeState.setDefaultMetaDescription(e.target.value)}
                placeholder="Enter default meta description"
                rows={3}
              />
            </div>
          </form>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Store Appearance</CardTitle>
          <CardDescription>
            Customize how your store looks to customers
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Primary Color</Label>
                <div className="flex items-center gap-4">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button 
                        variant="outline" 
                        className="w-[100px] h-[40px] border-2" 
                        style={{ backgroundColor: primaryColor }}
                      />
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <HexColorPicker color={primaryColor} onChange={setPrimaryColor} />
                      <div className="p-2">
                        <Input 
                          value={primaryColor}
                          onChange={(e) => setPrimaryColor(e.target.value)}
                          className="mt-2"
                        />
                      </div>
                    </PopoverContent>
                  </Popover>
                  <span className="text-sm text-muted-foreground">
                    This color will be used for buttons, links, and accents
                  </span>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Hero Section Style</Label>
                <Select value={heroType} onValueChange={setHeroType}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select hero style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="image">Image Banner</SelectItem>
                    <SelectItem value="carousel">Image Carousel</SelectItem>
                    <SelectItem value="video">Video Background</SelectItem>
                    <SelectItem value="simple">Simple Text Only</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label>Shop Layout</Label>
                <Select value={shopLayout} onValueChange={setShopLayout}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Select shop layout" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grid">Grid Layout</SelectItem>
                    <SelectItem value="list">List Layout</SelectItem>
                    <SelectItem value="masonry">Masonry Grid</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-4 mt-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Display Currency Symbol</Label>
                    <p className="text-sm text-muted-foreground">
                      Show currency symbol (e.g., $) with prices
                    </p>
                  </div>
                  <Switch
                    checked={displayCurrency}
                    onCheckedChange={setDisplayCurrency}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Show Product Count</Label>
                    <p className="text-sm text-muted-foreground">
                      Display total number of products in each category
                    </p>
                  </div>
                  <Switch
                    checked={showProductCount}
                    onCheckedChange={setShowProductCount}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>"New" Badge</Label>
                    <p className="text-sm text-muted-foreground">
                      Show a badge on products added in the last 14 days
                    </p>
                  </div>
                  <Switch
                    checked={enableNewBadge}
                    onCheckedChange={setEnableNewBadge}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Social Share Buttons</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable sharing products on social media
                    </p>
                  </div>
                  <Switch
                    checked={enableShareButtons}
                    onCheckedChange={setEnableShareButtons}
                  />
                </div>
              </div>
            </div>
            
            <Button 
              type="button" 
              onClick={handleSubmit} 
              disabled={loading}
            >
              {loading ? 'Saving...' : 'Save Appearance Settings'}
            </Button>
          </form>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>Store Features</CardTitle>
          <CardDescription>
            Enable or disable store features
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Product Reviews</Label>
                <p className="text-sm text-muted-foreground">
                  Allow customers to leave reviews on products
                </p>
              </div>
              <Switch
                checked={storeState.enableReviews}
                onCheckedChange={storeState.setEnableReviews}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Wishlist Feature</Label>
                <p className="text-sm text-muted-foreground">
                  Allow customers to save products to wishlist
                </p>
              </div>
              <Switch
                checked={storeState.enableWishlist}
                onCheckedChange={storeState.setEnableWishlist}
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>Inventory Tracking</Label>
                <p className="text-sm text-muted-foreground">
                  Track product stock quantities
                </p>
              </div>
              <Switch
                checked={storeState.enableInventoryTracking}
                onCheckedChange={storeState.setEnableInventoryTracking}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="low-stock">Low Stock Threshold</Label>
              <Input
                id="low-stock"
                type="number"
                min="0"
                value={storeState.lowStockThreshold}
                onChange={(e) => storeState.setLowStockThreshold(e.target.value)}
                className="w-full md:w-32"
              />
              <p className="text-xs text-muted-foreground">
                Get notified when stock falls below this number
              </p>
            </div>
            
            <Button type="button" onClick={handleSubmit} disabled={loading}>
              {loading ? 'Saving...' : 'Save Feature Settings'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default StoreGeneralTab;
