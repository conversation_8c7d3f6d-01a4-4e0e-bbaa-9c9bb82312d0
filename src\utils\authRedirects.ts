
/**
 * Gets the appropriate redirect URL based on the current domain
 */
export const getRedirectUrl = () => {
  const origin = window.location.origin;
  return `${origin}/signin`;
};

/**
 * Gets the site URL based on the current domain
 */
export const getSiteUrl = () => {
  const hostname = window.location.hostname;
  return hostname;
};

/**
 * Detects if the current domain is a store subdomain
 */
export const isStoreSubdomain = () => {
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  
  console.log("Domain check - Current hostname:", hostname);
  console.log("Domain check - Current pathname:", pathname);
  
  // For development and preview environments, use path-based detection
  if (hostname === 'localhost' || 
      hostname.includes('lovable.app') || 
      hostname.includes('lovableproject.com') ||
      hostname.includes('vercel.app') ||
      hostname.includes('preview--')) {
    console.log("Development/preview environment detected");
    
    const isShopPath = pathname.startsWith('/shop');
    console.log("Using path-based detection:", { isShopPath });
    return isShopPath;
  }
  
  // Check if this is a store subdomain for production domains
  if (hostname.endsWith('.m-duka.app') && 
      !hostname.startsWith('www.') && 
      hostname !== 'm-duka.app') {
    console.log("M-duka store subdomain detected:", hostname);
    return true;
  }
  
  if (hostname.endsWith('.viomify.com') && 
      !hostname.startsWith('www.') && 
      hostname !== 'viomify.com') {
    console.log("Viomify store subdomain detected:", hostname);
    return true;
  }
  
  console.log("Domain check result for:", hostname, "=> not a store subdomain");
  return false;
};

/**
 * Extracts store slug from subdomain or path
 */
export const getStoreSlugFromSubdomain = () => {
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  
  // For development environments, extract from path
  if (hostname === 'localhost' || 
      hostname.includes('lovable.app') || 
      hostname.includes('lovableproject.com') ||
      hostname.includes('vercel.app')) {
    
    if (pathname.startsWith('/shop/')) {
      const pathParts = pathname.split('/');
      return pathParts[2] || 'default-store';
    }
    return null;
  }
  
  if (isStoreSubdomain()) {
    return hostname.split('.')[0];
  }
  
  return null;
};

/**
 * Checks if this is the main domain (not a subdomain)
 */
export const isMainDomain = () => {
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  
  console.log("Main domain check for:", hostname);
  
  // For development and preview environments, use path-based detection
  if (hostname === 'localhost' || 
      hostname.includes('lovable.app') || 
      hostname.includes('lovableproject.com') ||
      hostname.includes('vercel.app')) {
    
    const isMainPath = !pathname.startsWith('/shop');
    console.log("Using path-based detection for main domain:", { hostname, isMainPath });
    return isMainPath;
  }
  
  // Check for exact matches of main domains
  const isMain = hostname === 'm-duka.app' || 
                 hostname === 'viomify.com' || 
                 hostname === 'www.m-duka.app' || 
                 hostname === 'www.viomify.com';
  
  console.log("Main domain check result:", { hostname, isMain });
  return isMain;
};

// Debug function to output all relevant domain info
export const logDomainInfo = () => {
  const hostname = window.location.hostname;
  const pathname = window.location.pathname;
  const subdomain = isStoreSubdomain();
  const mainDomain = isMainDomain();
  const storeSlug = getStoreSlugFromSubdomain();
  const isPreview = hostname.includes('preview--') || 
                   hostname.includes('lovable.app') ||
                   hostname.includes('vercel.app');
  
  console.log("=== Domain Information ===");
  console.log("Hostname:", hostname);
  console.log("Path:", pathname);
  console.log("Is store subdomain:", subdomain);
  console.log("Is main domain:", mainDomain);
  console.log("Store slug:", storeSlug);
  console.log("Is preview:", isPreview);
  console.log("========================");
  
  return {
    hostname,
    pathname,
    isSubdomain: subdomain,
    isMainDomain: mainDomain,
    storeSlug,
    isPreview
  };
};

// Log domain info on load
if (typeof window !== 'undefined') {
  logDomainInfo();
}
