name: Push to lovable.dev

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  push-to-lovable:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Push to lovable.dev
        run: |
          git remote add lovable ${{ secrets.LOVABLE_GIT_URL }}
          git push lovable main
        env:
          GIT_SSH_COMMAND: "ssh -o StrictHostKeyChecking=no"