import { Tables } from '@/integrations/supabase/types';

// Product variant interface
export interface ProductVariant {
  id?: string;  
  name?: string; 
  price: number;
  sku: string;
  stock_quantity: number;
  is_active: boolean;
  size?: string;  
  color?: string; 
  options?: {
    [key: string]: string | number | boolean;
  };
}

// Use the Supabase schema as the source of truth
export type DatabaseProduct = Tables<'products'>;

// Extended Product type for UI components that need additional fields
export interface Product extends DatabaseProduct {
  currency: string;
  sku: string;
  tags: string[];
  images: string[]; // Override to make it easier to work with
  has_variants?: boolean;
  variants?: ProductVariant[];
}

// Utility function to convert database product to UI product
export const toUIProduct = (dbProduct: DatabaseProduct): Product => ({
  ...dbProduct,
  currency: 'USD',
  sku: `SKU-${dbProduct.id.slice(0, 8)}`,
  tags: [],
  images: Array.isArray(dbProduct.images) ? dbProduct.images as string[] : [],
});

// Utility function to convert UI product back to database format
export const toDatabaseProduct = (uiProduct: Product): DatabaseProduct => {
  const { currency, sku, tags, ...dbProduct } = uiProduct;
  return dbProduct;
};