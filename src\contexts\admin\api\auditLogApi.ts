
import { supabase } from "@/integrations/supabase/client";

/**
 * Log an audit event to the audit_logs table (admin-only insertion).
 */
export async function logAuditEvent(eventType: string, actorId: string, targetUserId: string, details: any) {
  const { error } = await supabase.from("audit_logs").insert([
    {
      event_type: eventType,
      actor_id: actorId,
      target_user_id: targetUserId,
      details,
    },
  ]);
  return { error };
}

/**
 * Fetch audit logs (only shows for admin).
 */
export async function fetchAuditLogs(limit = 50) {
  const { data, error } = await supabase
    .from("audit_logs")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(limit);
  return { data, error };
}

// --CHANGELOG--
// - Functions for logging/fetching audit events (for a future AdminAuditLogTab)
// - These will be hooked to the UI in the next step.
