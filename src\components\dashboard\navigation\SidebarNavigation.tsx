
import React from "react";
import { useLocation } from "react-router-dom";
import { 
  Store, 
  Users, 
  Package, 
  ShoppingCart, 
  Settings, 
  CreditCard,
  Truck,
  UserRound,
  MessageCircle,
  Workflow
} from "lucide-react";
import SidebarNavItem from "./SidebarNavItem";

const SidebarNavigation = () => {
  const location = useLocation();
  
  const navItems = [
    { name: "Dashboard", path: "/dashboard", icon: <Store className="w-5 h-5 mr-2" /> },
    { name: "Products", path: "/products", icon: <Package className="w-5 h-5 mr-2" /> },
    { name: "Orders", path: "/orders", icon: <ShoppingCart className="w-5 h-5 mr-2" /> },
    { name: "Customers", path: "/customers", icon: <Users className="w-5 h-5 mr-2" /> },
    { name: "Profile", path: "/profile", icon: <UserRound className="w-5 h-5 mr-2" /> },
    { name: "Settings", path: "/settings", icon: <Settings className="w-5 h-5 mr-2" /> },
    { name: "Payments", path: "/payments", icon: <CreditCard className="w-5 h-5 mr-2" /> },
    { name: "Delivery", path: "/settings/delivery", icon: <Truck className="w-5 h-5 mr-2" /> },
    { name: "WhatsApp", path: "/settings/whatsapp", icon: <MessageCircle className="w-5 h-5 mr-2" /> },
    { name: "Workflow", path: "/settings/workflow", icon: <Workflow className="w-5 h-5 mr-2" /> },
    { name: "Storefront", path: "/shop", icon: <Store className="w-5 h-5 mr-2" /> },
  ];

  const isActive = (itemPath: string) => {
    // For specific paths like "/dashboard", "/products", etc.
    if (location.pathname === itemPath) {
      return true;
    }
    
    // For settings pages
    if (itemPath === "/settings" && location.pathname.startsWith("/settings") && 
        !location.pathname.includes("/delivery") &&
        !location.pathname.includes("/whatsapp") &&
        !location.pathname.includes("/workflow")) {
      return true;
    }
    
    // For delivery settings
    if (itemPath === "/settings/delivery" && location.pathname.includes("/delivery")) {
      return true;
    }
    
    // For WhatsApp settings
    if (itemPath === "/settings/whatsapp" && location.pathname.includes("/whatsapp")) {
      return true;
    }
    
    // For Workflow settings
    if (itemPath === "/settings/workflow" && location.pathname.includes("/workflow")) {
      return true;
    }
    
    // For payments
    if (itemPath === "/payments" && location.pathname === "/payments") {
      return true;
    }
    
    // For shop/storefront
    if (itemPath === "/shop" && location.pathname.startsWith("/shop")) {
      return true;
    }
    
    return false;
  };

  return (
    <nav className="space-y-1">
      {navItems.map((item) => (
        <SidebarNavItem
          key={item.path}
          item={item}
          isActive={isActive(item.path)}
        />
      ))}
    </nav>
  );
};

export default SidebarNavigation;
