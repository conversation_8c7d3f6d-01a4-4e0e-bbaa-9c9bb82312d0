
import React from 'react';
import { useStaffMembers } from '@/hooks/staff/useStaffMembers';
import StaffHeader from '@/components/settings/staff/StaffHeader';
import StaffContent from '@/components/settings/staff/StaffContent';
import StaffDialogs from '@/components/settings/staff/StaffDialogs';
import { StaffMember } from '@/components/settings/staff/types';

const StaffSettings = () => {
  const {
    staffMembers,
    isLoading,
    selectedStaff,
    setSelectedStaff,
    handleAddStaff,
    handleEditStaff,
    handleDeleteStaff
  } = useStaffMembers();

  // Dialog state moved outside of any conditional rendering
  const [isAddStaffOpen, setIsAddStaffOpen] = React.useState(false);
  const [isEditStaffOpen, setIsEditStaffOpen] = React.useState(false);
  const [isDeleteStaffOpen, setIsDeleteStaffOpen] = React.useState(false);

  const handleAddClick = () => {
    setIsAddStaffOpen(true);
  };

  const handleEditClick = (staff: any) => {
    setIsEditStaffOpen(true);
  };

  const handleDeleteClick = (staff: any) => {
    setIsDeleteStaffOpen(true);
  };

  const handleCloseDialogs = () => {
    setIsAddStaffOpen(false);
    setIsEditStaffOpen(false);
    setIsDeleteStaffOpen(false);
  };

  return (
    <div className="space-y-6">
      <StaffHeader onAddStaff={handleAddClick} />

      <StaffContent 
        staffMembers={staffMembers} 
        isLoading={isLoading} 
        onEdit={handleEditClick} 
        onDelete={handleDeleteClick} 
      />

      <StaffDialogs 
        isAddStaffOpen={isAddStaffOpen}
        isEditStaffOpen={isEditStaffOpen}
        isDeleteStaffOpen={isDeleteStaffOpen}
        selectedStaff={selectedStaff}
        isLoading={isLoading}
        onCloseDialogs={handleCloseDialogs}
        onAddStaff={() => handleAddStaff({})}
        onEditStaff={() => handleEditStaff({})}
        onDeleteStaff={() => handleDeleteStaff({})}
      />
    </div>
  );
};

export default StaffSettings;
