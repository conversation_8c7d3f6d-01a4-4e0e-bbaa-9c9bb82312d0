
import React, { useState } from 'react';
import { ProductVariant } from '@/types/unified-product';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Plus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SIZE_OPTIONS, COLOR_OPTIONS } from './constants';

interface NewVariantFormProps {
  initialPrice: number;
  onAddVariant: (variant: ProductVariant) => void;
  generateSku: (size?: string, color?: string) => string;
}

const NewVariantForm: React.FC<NewVariantFormProps> = ({
  initialPrice,
  onAddVariant,
  generateSku
}) => {
  const [newVariant, setNewVariant] = useState<ProductVariant>({
    id: undefined, // Add missing required fields with default values
    name: undefined,
    size: 'N/A',
    color: 'N/A',
    price: initialPrice,
    stock_quantity: 0,
    sku: '',
    is_active: true
  });

  const handleVariantChange = (field: keyof ProductVariant, value: any) => {
    setNewVariant({
      ...newVariant,
      [field]: value
    });
  };

  const handleAddVariant = () => {
    onAddVariant(newVariant);
    
    // Reset the form
    setNewVariant({
      id: undefined,
      name: undefined,
      size: 'N/A',
      color: 'N/A',
      price: initialPrice,
      stock_quantity: 0,
      sku: '',
      is_active: true
    });
  };

  return (
    <div className="border rounded-md p-4 bg-muted/30">
      <h4 className="text-sm font-medium mb-3">Add New Variant</h4>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 items-end">
        <div>
          <label className="text-xs mb-1 block">Size</label>
          <Select 
            value={newVariant.size || 'N/A'}
            onValueChange={(value) => handleVariantChange('size', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Size" />
            </SelectTrigger>
            <SelectContent>
              {SIZE_OPTIONS.map((size) => (
                <SelectItem key={size} value={size}>{size}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="text-xs mb-1 block">Color</label>
          <Select 
            value={newVariant.color || 'N/A'}
            onValueChange={(value) => handleVariantChange('color', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Color" />
            </SelectTrigger>
            <SelectContent>
              {COLOR_OPTIONS.map((color) => (
                <SelectItem key={color} value={color}>{color}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <label className="text-xs mb-1 block">Price</label>
          <Input
            type="number"
            value={newVariant.price}
            onChange={(e) => handleVariantChange('price', parseFloat(e.target.value))}
            min="0"
            step="0.01"
          />
        </div>
        
        <div>
          <label className="text-xs mb-1 block">SKU (Optional)</label>
          <Input
            value={newVariant.sku || ''}
            onChange={(e) => handleVariantChange('sku', e.target.value)}
            placeholder={generateSku(newVariant.size, newVariant.color)}
          />
        </div>
        
        <div>
          <label className="text-xs mb-1 block">Stock</label>
          <Input
            type="number"
            value={newVariant.stock_quantity}
            onChange={(e) => handleVariantChange('stock_quantity', parseInt(e.target.value))}
            min="0"
          />
        </div>
        
        <div>
          <Button onClick={handleAddVariant} className="w-full">
            <Plus className="h-4 w-4 mr-1" />
            Add Variant
          </Button>
        </div>
      </div>
    </div>
  );
};

export default NewVariantForm;
