
/**
 * This is a workaround for Framer Motion JSX compatibility issues
 * It provides a compatible interface that Framer Motion expects
 */
import * as React from 'react';

// Log the fix being applied (only in development)
if (import.meta.env.DEV) {
  console.log("%c JSX Runtime Fix Loaded", "background: #222; color: #66ff66; font-size: 14px;");
}

// Re-export the JSX runtime that Framer Motion expects
export const jsx = React.createElement;
export const jsxs = React.createElement;
export const Fragment = React.Fragment;

// Add memoization support
export const jsxDEV = React.createElement;
export const jsxsDEV = React.createElement;

// Additional exports for advanced use cases
export const createElement = React.createElement;
export const Children = React.Children;
export const createContext = React.createContext;
export const forwardRef = React.forwardRef;

// Export additional React items that might be needed
export default {
  jsx,
  jsxs,
  Fragment,
  jsxDEV,
  jsxsDEV,
  createElement,
  Children,
  createContext,
  forwardRef
};

// Add to window for debugging purposes
if (typeof window !== 'undefined') {
  (window as any).__JSX_RUNTIME_FIX__ = { jsx, jsxs, Fragment };
}
