
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";

// CORS headers for web requests
const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

// Pabbly endpoint
const PABBLY_API_URL = "https://payments.pabbly.com/payment/checkout";

serve(async (req) => {
  if (req.method === "OPTIONS") {
    // Handle preflight requests for CORS
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Only allow POST requests
    if (req.method !== "POST") {
      return new Response(
        JSON.stringify({ success: false, error: "Method Not Allowed" }),
        { status: 405, headers: corsHeaders }
      );
    }

    // Parse JSON body from client (should contain the request payload for Pabbly checkout)
    const body = await req.json();

    // Retrieve Pabbly secret key from Supabase secrets
    const PABBLY_SECRET_KEY = Deno.env.get("PABBLY_SECRET_KEY");
    if (!PABBLY_SECRET_KEY) {
      return new Response(
        JSON.stringify({ success: false, error: "Pabbly secret key is not configured." }),
        { status: 500, headers: corsHeaders }
      );
    }

    // Prepare payload for Pabbly (add secret key)
    // Pabbly may expect secret as part of headers or body depending on API requirements.
    // Assume it's sent as part of the payload (body), add key here:
    body.api_secret = PABBLY_SECRET_KEY;

    // Forward to Pabbly
    const resp = await fetch(PABBLY_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(body),
    });

    const result = await resp.json();

    // Proxy Pabbly response back to frontend
    return new Response(JSON.stringify(result), {
      status: resp.status,
      headers: corsHeaders,
    });
  } catch (error) {
    console.error("pabbly-checkout edge function error:", error);
    return new Response(
      JSON.stringify({ success: false, error: error.message }),
      { status: 500, headers: corsHeaders }
    );
  }
});

