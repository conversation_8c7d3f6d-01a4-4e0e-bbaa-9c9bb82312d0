
import React from "react";
import { MapPin, Search, Tags, Filter, ShoppingBag } from "lucide-react";
import { Container } from "@/components/ui/container";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

interface StoreInfoProps {
  name: string;
  description: string;
  phone?: string;
  email?: string;
  businessHours?: string[] | string;
  logo?: string;
  website?: string;
  address?: string;
  location?: string;
}

const StoreInfo: React.FC<StoreInfoProps> = ({
  name,
  description,
  phone,
  email,
  businessHours,
  logo,
  website,
  address,
  location,
}) => {
  console.log("StoreInfo rendering with:", { address, location }); // Debug log
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = React.useState("");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/shop/search?q=${encodeURIComponent(searchQuery)}`);
    }
  };

  return (
    <div className="bg-white shadow-sm border-b">
      <Container>
        <div className="py-6">
          {/* Store Logo */}
          <div className="flex justify-center mb-4">
            {logo ? (
              <img 
                src={logo} 
                alt={`${name} logo`} 
                className="w-24 h-24 object-contain rounded-full border border-gray-100 shadow-sm"
              />
            ) : (
              <div className="w-24 h-24 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center text-3xl font-bold text-white shadow-sm">
                {name.charAt(0)}
              </div>
            )}
          </div>
          
          {/* Store Name */}
          <h1 className="text-2xl font-bold text-center mb-2">{name}</h1>
          
          {/* Tagline - Using description as tagline */}
          <p className="text-gray-600 text-center max-w-xl mx-auto mb-4 text-sm">{description}</p>
          
          {/* Address and Location */}
          <div className="flex items-center justify-center gap-2 text-sm text-gray-600 mb-4">
            <MapPin className="h-4 w-4 text-gray-500" />
            <span className="text-center">
              {location || ""}{(location && address) ? ", " : ""}{address || ""}
            </span>
          </div>
          
          {/* About Text - Kept as an italic description */}
          <div className="text-center max-w-xl mx-auto mb-6">
            <p className="text-gray-600 text-sm italic">
              Welcome to our store. We offer quality products and excellent service.
            </p>
          </div>
          
          {/* Search Section */}
          <div className="max-w-md mx-auto mb-5">
            <form onSubmit={handleSearch} className="relative">
              <Input
                type="search"
                placeholder="Search products..."
                className="w-full pr-20 py-2 border-green-100 focus:border-green-300 focus:ring-green-300"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <Button 
                type="submit"
                size="sm"
                className="absolute right-1 top-1 h-8"
              >
                <Search className="h-4 w-4 mr-1" />
                Search
              </Button>
            </form>
          </div>
          
          {/* Quick Action Buttons */}
          <div className="flex flex-wrap justify-center gap-2 mb-2">
            <Button variant="outline" size="sm" className="rounded-full" onClick={() => navigate('/shop/categories')}>
              <Tags className="h-4 w-4 mr-1" />
              Categories
            </Button>
            <Button variant="outline" size="sm" className="rounded-full" onClick={() => navigate('/shop/featured')}>
              <ShoppingBag className="h-4 w-4 mr-1" />
              Featured
            </Button>
            <Button variant="outline" size="sm" className="rounded-full" onClick={() => navigate('/shop/new')}>
              <Filter className="h-4 w-4 mr-1" />
              New Arrivals
            </Button>
          </div>
          
          {/* Store Info Grid - Only showing contact info */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-xl mx-auto text-sm">
            {phone && (
              <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-md">
                <span className="text-gray-700">{phone}</span>
              </div>
            )}
            
            {email && (
              <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-md">
                <span className="text-gray-700">{email}</span>
              </div>
            )}
            
            {businessHours && (
              <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-md">
                <span className="text-gray-700">{businessHours}</span>
              </div>
            )}
            
            {website && (
              <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-md">
                <a href={website} className="text-blue-600 hover:underline truncate" target="_blank" rel="noopener noreferrer">
                  {website.replace(/^https?:\/\//, '')}
                </a>
              </div>
            )}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default StoreInfo;
