
/**
 * Checks if predicate returns truthy for all elements of collection.
 * The predicate is invoked with three arguments: (value, index|key, collection).
 *
 * @param {Array|Object} collection - The collection to iterate over
 * @param {Function} predicate - The function invoked per iteration
 * @returns {boolean} Returns true if all elements pass the predicate check, else false
 */
function every(collection, predicate) {
  if (!collection) return true;
  
  // Handle arrays
  if (Array.isArray(collection)) {
    for (let i = 0; i < collection.length; i++) {
      if (!predicate(collection[i], i, collection)) {
        return false;
      }
    }
    return true;
  }
  
  // Handle objects
  if (typeof collection === 'object') {
    for (const key in collection) {
      if (Object.prototype.hasOwnProperty.call(collection, key)) {
        if (!predicate(collection[key], key, collection)) {
          return false;
        }
      }
    }
    return true;
  }
  
  return true;
}

// Support both ESM and CJS
export default every;
