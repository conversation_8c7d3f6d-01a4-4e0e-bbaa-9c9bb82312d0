import { Json } from '@/integrations/supabase/types';

export interface Store {
  id: string;
  name: string;
  description: string;
  owner_id: string;
  store_type: 'physical' | 'digital' | 'service' | 'hybrid' | string;
  category: string;
  payment_methods: string[];
  theme_options: {
    primaryColor: string;
    secondaryColor: string;
    colorScheme?: string;
    darkMode?: boolean;
    customHeader?: boolean;
    customFooter?: boolean;
    layoutType?: string;
    displayCurrency?: boolean;
    showProductCount?: boolean;
    enableNewBadge?: boolean;
    enableShareButtons?: boolean;
    heroStyle?: string;
    showProductRatings?: boolean;
    enableWishlist?: boolean;
    [key: string]: any;
  };
  notifications: {
    email: boolean;
    sms: boolean;
    orderNotifications?: boolean;
    stockNotifications?: boolean;
    marketingNotifications?: boolean;
  };
  logo_url: string | null;
  store_url: string;
  whatsapp_settings?: {
    enabled: boolean;
    number: string;
    message: string;
    businessNumber?: string;
    enableOrderNotifications?: boolean;
    enableCustomerUpdates?: boolean;
    customMessage?: string;
    autoReply?: boolean;
  };
  created_at: string;
  updated_at: string;
  
  // Additional properties for UI compatibility
  storeUrl?: string;
  themeOptions?: {
    primaryColor: string;
    secondaryColor: string;
    colorScheme?: string;
    darkMode?: boolean;
    customHeader?: boolean;
    customFooter?: boolean;
    layoutType?: string;
    displayCurrency?: boolean;
    showProductCount?: boolean;
    enableNewBadge?: boolean;
    enableShareButtons?: boolean;
    heroStyle?: string;
    showProductRatings?: boolean;
    enableWishlist?: boolean;
    [key: string]: any;
  };
  paymentMethods?: string[];
  logo?: string | null;
  whatsappSettings?: {
    enabled: boolean;
    number: string;
    message: string;
    businessNumber?: string;
    enableOrderNotifications?: boolean;
    enableCustomerUpdates?: boolean;
    customMessage?: string;
    autoReply?: boolean;
  };
  address?: string;
  location?: string;
  about_text?: string;
  notificationsEmail?: string;
  phone?: string;
  email?: string;
  business_hours?: string[];
  theme_id?: string;
  is_active?: boolean;
  bannerImage?: string | null;
  theme?: {
    primaryColor: string;
    secondaryColor: string;
    [key: string]: any;
  };
}

export interface StoreFormData {
  name: string;
  description: string;
  storeUrl: string;
  category: string;
  themeId: string;
  storeType: 'physical' | 'digital' | 'service' | 'hybrid' | string;
  paymentMethods: string[];
  notificationsEmail?: string;
  logo?: string | null;
  bannerImage?: string | null;
  notifications: {
    orderNotifications?: boolean;
    stockNotifications?: boolean;
    marketingNotifications?: boolean;
    email?: boolean;
    sms?: boolean;
  };
  themeOptions: {
    primaryColor: string;
    colorScheme: string;
    darkMode?: boolean;
    customHeader?: boolean;
    customFooter?: boolean;
    layoutType?: string;
    displayCurrency?: boolean;
    showProductCount?: boolean;
    enableNewBadge?: boolean;
    enableShareButtons?: boolean;
    heroStyle?: string;
    showProductRatings?: boolean;
    enableWishlist?: boolean;
    [key: string]: any;
  };
  whatsappSettings?: {
    businessNumber: string;
    enableOrderNotifications: boolean;
    enableCustomerUpdates: boolean;
    customMessage: string;
    autoReply: boolean;
    enabled?: boolean;
    number?: string;
    message?: string;
  };
  workflowSettings?: {
    enableAutomation: boolean;
    messageTemplates: any[];
    orderWorkflows: any[];
    customerWorkflows: any[];
    inventoryWorkflows?: any[];
  };
  phone?: string;
  email?: string;
  business_hours?: string[];
  theme_id?: string;
}
