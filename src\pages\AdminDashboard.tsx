
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import AdminUsersTab from '@/components/admin/AdminUsersTab';
import AdminStoresTab from '@/components/admin/AdminStoresTab';
import AdminHeader from '@/components/admin/AdminHeader';
import AdminOverviewTab from '@/components/admin/dashboard/AdminOverviewTab';
import AdminOrdersTab from '@/components/admin/dashboard/AdminOrdersTab';
import AdminSettingsTab from '@/components/admin/dashboard/AdminSettingsTab';
import AdminSidebar from '@/components/admin/navigation/AdminSidebar';
import { SidebarProvider } from '@/components/ui/sidebar';
import { formatCurrency } from '@/utils/formatters';
import { 
  fetchAdminMetrics, 
  fetchRevenueData, 
  fetchUserGrowthData, 
  fetchOrdersByStoreData 
} from '@/contexts/admin/api/adminMetrics';

const AdminDashboard: React.FC = () => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("overview");
  const [isLoading, setIsLoading] = useState(true);
  const [metrics, setMetrics] = useState({
    totalUsers: 0,
    totalStores: 0,
    totalOrders: 0,
    totalRevenue: 0
  });
  const [revenueData, setRevenueData] = useState([]);
  const [userGrowthData, setUserGrowthData] = useState([]);
  const [ordersByStoreData, setOrdersByStoreData] = useState([]);

  useEffect(() => {
    // Check for admin permissions
    if (isAuthenticated && user && user.role !== 'admin') {
      navigate('/dashboard');
    }

    // Get the tab from URL if present
    const urlParams = new URLSearchParams(location.search);
    const tabParam = urlParams.get('tab');
    if (tabParam && ['overview', 'users', 'stores', 'orders', 'settings'].includes(tabParam)) {
      setActiveTab(tabParam);
    } else if (!tabParam && location.pathname === '/admin') {
      setActiveTab('overview');
    }
  }, [user, isAuthenticated, navigate, location]);

  // Fetch admin dashboard data
  useEffect(() => {
    const loadDashboardData = async () => {
      setIsLoading(true);
      try {
        // Fetch all data in parallel
        const [metricsData, revenue, userGrowth, storeOrders] = await Promise.all([
          fetchAdminMetrics(),
          fetchRevenueData(),
          fetchUserGrowthData(),
          fetchOrdersByStoreData()
        ]);

        setMetrics(metricsData);
        setRevenueData(revenue);
        setUserGrowthData(userGrowth);
        setOrdersByStoreData(storeOrders);
      } catch (error) {
        console.error("Error loading admin dashboard data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    if (isAuthenticated && user?.role === 'admin') {
      loadDashboardData();
    }
  }, [isAuthenticated, user]);

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    // Update the URL without refreshing the page
    const url = new URL(window.location.href);
    url.searchParams.set('tab', value);
    window.history.pushState({}, '', url);
  };

  if (!user || user.role !== 'admin') {
    return null; // Don't render anything until we redirect
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-background">
        <AdminSidebar />
        
        <div className="flex-1 flex flex-col">
          <AdminHeader />
          <main className="flex-1 container py-6 mx-auto">
            <div className="mb-8">
              <h1 className="text-3xl font-bold tracking-tight">Admin Dashboard</h1>
              <p className="text-muted-foreground">
                Manage your platform, users, and stores
              </p>
            </div>

            <Tabs value={activeTab} className="w-full" onValueChange={handleTabChange}>
              <TabsList className="mb-8 w-full justify-start">
                <TabsTrigger value="overview">Overview</TabsTrigger>
                <TabsTrigger value="users">Users</TabsTrigger>
                <TabsTrigger value="stores">Stores</TabsTrigger>
                <TabsTrigger value="orders">Orders</TabsTrigger>
                <TabsTrigger value="settings">Platform Settings</TabsTrigger>
              </TabsList>
              
              <TabsContent value="overview">
                <AdminOverviewTab 
                  metrics={metrics}
                  revenueData={revenueData}
                  userGrowthData={userGrowthData}
                  ordersByStoreData={ordersByStoreData}
                  formatCurrency={(amount) => formatCurrency(amount, 'USD')}
                  isLoading={isLoading}
                />
              </TabsContent>

              <TabsContent value="users">
                <AdminUsersTab />
              </TabsContent>

              <TabsContent value="stores">
                <AdminStoresTab />
              </TabsContent>

              <TabsContent value="orders">
                <AdminOrdersTab />
              </TabsContent>

              <TabsContent value="settings">
                <AdminSettingsTab />
              </TabsContent>
            </Tabs>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AdminDashboard;
