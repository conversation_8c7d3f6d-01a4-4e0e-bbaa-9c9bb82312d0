import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { 
  Search,
  MoreHorizontal, 
  User as UserIcon,
  UserCog,
  UserX,
  ChevronDown,
  DownloadIcon,
  FilterIcon,
  RefreshCw,
  PlusCircle,
  ChevronUp
} from 'lucide-react';
import UserDetailsDialog from './UserDetailsDialog';
import { UserType, UserFilterState } from '@/types/admin';
import { toast } from 'sonner';

const mockUsers: UserType[] = [
  { 
    id: '1', 
    name: 'John Doe', 
    email: '<EMAIL>', 
    role: 'store_owner',
    status: 'active',
    storeCount: 2,
    createdAt: '2023-05-15T10:30:00'
  },
  { 
    id: '2', 
    name: 'Jane Smith', 
    email: '<EMAIL>', 
    role: 'customer',
    status: 'active',
    storeCount: 0,
    createdAt: '2023-06-22T14:45:00'
  },
  { 
    id: '3', 
    name: 'Admin User', 
    email: '<EMAIL>', 
    role: 'admin',
    status: 'active',
    storeCount: 0,
    createdAt: '2023-01-10T09:15:00'
  },
  { 
    id: '4', 
    name: 'Bob Johnson', 
    email: '<EMAIL>', 
    role: 'store_owner',
    status: 'suspended',
    storeCount: 1,
    createdAt: '2023-07-05T16:20:00'
  },
  { 
    id: '5', 
    name: 'Sarah Wilson', 
    email: '<EMAIL>', 
    role: 'store_owner',
    status: 'active',
    storeCount: 3,
    createdAt: '2023-08-12T11:15:00'
  },
  { 
    id: '6', 
    name: 'Michael Brown', 
    email: '<EMAIL>', 
    role: 'customer',
    status: 'pending',
    storeCount: 0,
    createdAt: '2023-09-18T13:30:00'
  },
  { 
    id: '7', 
    name: 'Emily Davis', 
    email: '<EMAIL>', 
    role: 'store_owner',
    status: 'active',
    storeCount: 1,
    createdAt: '2023-04-30T09:45:00'
  }
];

const DevBypassBanner = () => (
  <div className="bg-amber-100 border-b border-amber-300 text-amber-900 py-2 px-4 mb-4 flex items-center justify-center">
    <span className="font-semibold mr-2">DEV MODE:</span>
    Authentication and role restrictions are bypassed for development/testing.
  </div>
);

const AdminUsersTab: React.FC = () => {
  const [users, setUsers] = useState<UserType[]>(mockUsers);
  const [filteredUsers, setFilteredUsers] = useState<UserType[]>(mockUsers);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [detailsUser, setDetailsUser] = useState<UserType | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [filters, setFilters] = useState<UserFilterState>({
    search: '',
    role: 'all',
    status: 'all',
    sortBy: 'name',
    sortOrder: 'asc'
  });

  useEffect(() => {
    setIsLoading(true);
    
    let result = [...users];
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      result = result.filter(user => 
        user.name.toLowerCase().includes(searchLower) || 
        user.email.toLowerCase().includes(searchLower)
      );
    }
    
    if (filters.role !== 'all') {
      result = result.filter(user => user.role === filters.role);
    }
    
    if (filters.status !== 'all') {
      result = result.filter(user => user.status === filters.status);
    }
    
    result.sort((a, b) => {
      let comparison = 0;
      
      switch (filters.sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'email':
          comparison = a.email.localeCompare(b.email);
          break;
        case 'role':
          comparison = a.role.localeCompare(b.role);
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        case 'storeCount':
          comparison = a.storeCount - b.storeCount;
          break;
        case 'createdAt':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        default:
          comparison = 0;
      }
      
      return filters.sortOrder === 'asc' ? comparison : -comparison;
    });
    
    setFilteredUsers(result);
    
    setSelectedUsers([]);
    
    setTimeout(() => {
      setIsLoading(false);
    }, 500);
  }, [users, filters]);

  const handleSort = (column: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: column,
      sortOrder: prev.sortBy === column && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };

  const handleFilterChange = (key: keyof UserFilterState, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleResetFilters = () => {
    setFilters({
      search: '',
      role: 'all',
      status: 'all',
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(filteredUsers.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (checked: boolean, userId: string) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleUserAction = (action: string, userId: string) => {
    const user = users.find(u => u.id === userId);
    
    switch (action) {
      case 'view':
        setDetailsUser(user || null);
        setIsDetailsOpen(true);
        break;
      case 'edit':
        toast.info(`Edit user: ${user?.name}`);
        break;
      case 'suspend':
        toast.success(`User ${user?.name} has been suspended`);
        setUsers(prev => 
          prev.map(u => 
            u.id === userId ? { ...u, status: 'suspended' } : u
          )
        );
        break;
      case 'activate':
        toast.success(`User ${user?.name} has been activated`);
        setUsers(prev => 
          prev.map(u => 
            u.id === userId ? { ...u, status: 'active' } : u
          )
        );
        break;
      case 'delete':
        toast.error(`User ${user?.name} has been deleted`);
        setUsers(prev => prev.filter(u => u.id !== userId));
        break;
      default:
        console.log(`Action: ${action}, User ID: ${userId}`);
    }
  };

  const handleBulkAction = (action: string) => {
    if (selectedUsers.length === 0) {
      toast.error('No users selected');
      return;
    }
    
    switch (action) {
      case 'suspend':
        toast.success(`${selectedUsers.length} users have been suspended`);
        setUsers(prev => 
          prev.map(user => 
            selectedUsers.includes(user.id) ? { ...user, status: 'suspended' } : user
          )
        );
        break;
      case 'activate':
        toast.success(`${selectedUsers.length} users have been activated`);
        setUsers(prev => 
          prev.map(user => 
            selectedUsers.includes(user.id) ? { ...user, status: 'active' } : user
          )
        );
        break;
      case 'delete':
        toast.error(`${selectedUsers.length} users have been deleted`);
        setUsers(prev => 
          prev.filter(user => !selectedUsers.includes(user.id))
        );
        break;
      case 'export':
        toast.info('Exporting selected users');
        console.log('Export users:', selectedUsers);
        break;
      default:
        console.log(`Bulk action: ${action}, Users: ${selectedUsers.length}`);
    }
    
    setSelectedUsers([]);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const SortIndicator = ({ column }: { column: string }) => {
    if (filters.sortBy !== column) return null;
    
    return filters.sortOrder === 'asc' ? 
      <ChevronUp className="ml-1 h-3 w-3" /> : 
      <ChevronDown className="ml-1 h-3 w-3" />;
  };

  return (
    <Card>
      {import.meta.env.DEV && (
        <DevBypassBanner />
      )}
      <CardHeader>
        <CardTitle>User Management</CardTitle>
        <CardDescription>
          View and manage all users and their roles on your platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col space-y-4 md:space-y-0 md:flex-row md:justify-between md:items-center mb-6">
          <div className="relative w-full md:w-64">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search users..."
              className="pl-8"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
            />
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Select 
              value={filters.role} 
              onValueChange={(value) => handleFilterChange('role', value)}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Filter by role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Roles</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
                <SelectItem value="store_owner">Store Owner</SelectItem>
                <SelectItem value="customer">Customer</SelectItem>
              </SelectContent>
            </Select>
            
            <Select 
              value={filters.status} 
              onValueChange={(value) => handleFilterChange('status', value)}
            >
              <SelectTrigger className="w-[130px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="suspended">Suspended</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
              </SelectContent>
            </Select>
            
            <Button 
              variant="outline" 
              size="icon"
              onClick={handleResetFilters}
              title="Reset filters"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
            
            <Button onClick={() => toast.info('Add user form would open here')}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Add User
            </Button>
          </div>
        </div>

        {selectedUsers.length > 0 && (
          <div className="bg-muted p-2 rounded-md mb-4 flex items-center justify-between">
            <span className="text-sm font-medium ml-2">
              {selectedUsers.length} user{selectedUsers.length !== 1 ? 's' : ''} selected
            </span>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleBulkAction('activate')}
              >
                Activate
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleBulkAction('suspend')}
              >
                Suspend
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handleBulkAction('export')}
              >
                <DownloadIcon className="mr-1 h-3 w-3" />
                Export
              </Button>
              <Button 
                variant="destructive" 
                size="sm" 
                onClick={() => handleBulkAction('delete')}
              >
                Delete
              </Button>
            </div>
          </div>
        )}

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Checkbox 
                    checked={
                      filteredUsers.length > 0 && 
                      selectedUsers.length === filteredUsers.length
                    }
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all users"
                  />
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('name')}>
                  <div className="flex items-center">
                    Name
                    <SortIndicator column="name" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('email')}>
                  <div className="flex items-center">
                    Email
                    <SortIndicator column="email" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('role')}>
                  <div className="flex items-center">
                    Role
                    <SortIndicator column="role" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('status')}>
                  <div className="flex items-center">
                    Status
                    <SortIndicator column="status" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('storeCount')}>
                  <div className="flex items-center">
                    Stores
                    <SortIndicator column="storeCount" />
                  </div>
                </TableHead>
                <TableHead className="cursor-pointer" onClick={() => handleSort('createdAt')}>
                  <div className="flex items-center">
                    Joined
                    <SortIndicator column="createdAt" />
                  </div>
                </TableHead>
                <TableHead className="w-[80px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                Array.from({ length: 5 }).map((_, index) => (
                  <TableRow key={`loading-${index}`} className="animate-pulse">
                    <TableCell>
                      <div className="h-4 w-4 rounded-sm bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-4 w-32 rounded bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-4 w-48 rounded bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-5 w-20 rounded-full bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-5 w-16 rounded-full bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-4 w-4 rounded bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-4 w-24 rounded bg-muted"></div>
                    </TableCell>
                    <TableCell>
                      <div className="h-8 w-8 rounded bg-muted"></div>
                    </TableCell>
                  </TableRow>
                ))
              ) : filteredUsers.length > 0 ? (
                filteredUsers.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Checkbox 
                        checked={selectedUsers.includes(user.id)}
                        onCheckedChange={(checked) => 
                          handleSelectUser(checked as boolean, user.id)
                        }
                        aria-label={`Select ${user.name}`}
                      />
                    </TableCell>
                    <TableCell className="font-medium">{user.name}</TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Badge 
                        variant={user.role === 'admin' ? 'destructive' : 
                                user.role === 'store_owner' ? 'default' : 'secondary'}
                      >
                        {user.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          user.status === 'active' ? 'outline' : 
                          user.status === 'suspended' ? 'destructive' : 'secondary'
                        }
                      >
                        {user.status}
                      </Badge>
                    </TableCell>
                    <TableCell>{user.storeCount}</TableCell>
                    <TableCell>{formatDate(user.createdAt)}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Open menu</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleUserAction('view', user.id)}>
                            <UserIcon className="mr-2 h-4 w-4" />
                            <span>View Details</span>
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleUserAction('edit', user.id)}>
                            <UserCog className="mr-2 h-4 w-4" />
                            <span>Edit User</span>
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          {user.status === 'active' ? (
                            <DropdownMenuItem onClick={() => handleUserAction('suspend', user.id)}>
                              <UserX className="mr-2 h-4 w-4" />
                              <span>Suspend User</span>
                            </DropdownMenuItem>
                          ) : (
                            <DropdownMenuItem onClick={() => handleUserAction('activate', user.id)}>
                              <UserIcon className="mr-2 h-4 w-4" />
                              <span>Activate User</span>
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleUserAction('delete', user.id)}
                            className="text-red-600"
                          >
                            <UserX className="mr-2 h-4 w-4" />
                            <span>Delete User</span>
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    No users found matching your filters
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          Showing {filteredUsers.length} of {users.length} users
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => toast.info('Previous page')}
            disabled={true}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => toast.info('Next page')}
            disabled={true}
          >
            Next
          </Button>
        </div>
      </CardFooter>
      
      <UserDetailsDialog 
        open={isDetailsOpen} 
        onOpenChange={setIsDetailsOpen}
        user={detailsUser}
      />
    </Card>
  );
};

export default AdminUsersTab;
