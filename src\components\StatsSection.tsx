
import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Container } from "@/components/ui/container";

const stats = [
  { value: "100+", label: "Active stores" },
  { value: "95%", label: "Customer satisfaction" },
  { value: "2,500+", label: "Monthly orders" },
  { value: "5+", label: "Payment methods" }
];

const StatsSection = () => {
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  return (
    <section 
      className="py-16 bg-gradient-to-b from-accent/30 to-background" 
      ref={sectionRef}
    >
      <Container>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="text-center"
            >
              <div className="text-3xl md:text-4xl font-bold text-green-600 mb-2">
                {stat.value}
              </div>
              <div className="text-muted-foreground">
                {stat.label}
              </div>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
};

export default StatsSection;
