
/**
 * Iterates over elements of collection, returning the first element where the predicate returns truthy.
 *
 * @param {Array|Object} collection - The collection to inspect
 * @param {Function} predicate - The function invoked per iteration
 * @returns {*} Returns the matched element, else undefined
 */
function find(collection, predicate) {
  if (Array.isArray(collection)) {
    for (let i = 0; i < collection.length; i++) {
      const value = collection[i];
      if (predicate(value, i, collection)) {
        return value;
      }
    }
  } else if (collection && typeof collection === 'object') {
    const keys = Object.keys(collection);
    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const value = collection[key];
      if (predicate(value, key, collection)) {
        return value;
      }
    }
  }
  
  return undefined;
}

// Support both ESM and CJS
export default find;
