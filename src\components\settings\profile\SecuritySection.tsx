
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Shield, Key } from 'lucide-react';

interface SecuritySectionProps {
  twoFactorEnabled: boolean;
  setTwoFactorEnabled: (value: boolean) => void;
  onPasswordChange: () => void;
}

const SecuritySection: React.FC<SecuritySectionProps> = ({
  twoFactorEnabled,
  setTwoFactorEnabled,
  onPasswordChange
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Security
        </CardTitle>
        <CardDescription>
          Manage your account security settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="two-factor" className="text-base">Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Switch
              id="two-factor"
              checked={twoFactorEnabled}
              onCheckedChange={setTwoFactorEnabled}
            />
          </div>
          
          <div className="border-t pt-4">
            <Label className="text-base mb-2 block">Password</Label>
            <Button 
              variant="outline" 
              className="w-full sm:w-auto" 
              onClick={onPasswordChange}
              type="button"
            >
              <Key className="h-4 w-4 mr-2" />
              Change Password
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SecuritySection;
