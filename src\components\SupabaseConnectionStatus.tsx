import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Check<PERSON>ircle, XCircle, Loader2 } from 'lucide-react';

export function SupabaseConnectionStatus() {
  const [status, setStatus] = useState<'loading' | 'connected' | 'error'>('loading');
  const [error, setError] = useState<string | null>(null);
  const [connectionInfo, setConnectionInfo] = useState<any>(null);

  useEffect(() => {
    async function testConnection() {
      try {
        setStatus('loading');
        
        // Test auth connection
        const { data: { session }, error: authError } = await supabase.auth.getSession();
        
        // Test database connection (try to access a simple table or just test the connection)
        const { data, error: dbError } = await supabase
          .from('stores')
          .select('count')
          .limit(1);

        if (dbError && !dbError.message.includes('relation "stores" does not exist')) {
          throw dbError;
        }

        setConnectionInfo({
          url: 'https://nheycjpozywomwscplcz.supabase.co',
          hasSession: !!session,
          dbAccess: !dbError || dbError.message.includes('relation "stores" does not exist') ? 'accessible' : 'error',
          timestamp: new Date().toLocaleTimeString()
        });
        
        setStatus('connected');
        setError(null);
      } catch (err: any) {
        setStatus('error');
        setError(err.message);
      }
    }

    testConnection();
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-4 w-4 animate-spin" />;
      case 'connected':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'loading':
        return <Badge variant="secondary">Testing...</Badge>;
      case 'connected':
        return <Badge variant="default" className="bg-green-500">Connected</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          {getStatusIcon()}
          Supabase Connection
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {status === 'connected' && connectionInfo && (
          <div className="space-y-2 text-sm">
            <div>
              <strong>URL:</strong> {connectionInfo.url}
            </div>
            <div>
              <strong>Auth Session:</strong> {connectionInfo.hasSession ? 'Active' : 'None'}
            </div>
            <div>
              <strong>Database:</strong> {connectionInfo.dbAccess}
            </div>
            <div>
              <strong>Last Check:</strong> {connectionInfo.timestamp}
            </div>
          </div>
        )}
        
        {status === 'error' && (
          <div className="text-sm text-red-600">
            <strong>Error:</strong> {error}
          </div>
        )}
        
        {status === 'loading' && (
          <div className="text-sm text-gray-600">
            Testing connection to Supabase...
          </div>
        )}
      </CardContent>
    </Card>
  );
} 