
import { supabase } from '@/integrations/supabase/client';

/**
 * Update user's email address
 */
export const updateUserEmail = async (newEmail: string): Promise<{ success: boolean; error?: any }> => {
  try {
    console.log("Updating email to:", newEmail);
    
    const { data, error } = await supabase.auth.updateUser({
      email: newEmail,
    });
    
    if (error) {
      console.error("Email update error:", error.message);
      return { success: false, error };
    }
    
    console.log("Email update initiated successfully, verification required:", data);
    return { success: true };
  } catch (error: any) {
    console.error('Email update error:', error);
    return { success: false, error };
  }
};

/**
 * Confirm email change by verifying the token sent to the new email
 */
export const confirmEmailChange = async (token: string): Promise<{ success: boolean; error?: any }> => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email_change',
    });
    
    if (error) {
      console.error("Email change verification error:", error.message);
      return { success: false, error };
    }
    
    console.log("Email change verified successfully:", data);
    return { success: true };
  } catch (error: any) {
    console.error('Email change verification error:', error);
    return { success: false, error };
  }
};
