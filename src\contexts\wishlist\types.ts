
import { Product } from '@/types/unified-product';

export interface WishlistItem {
  id: string;
  product_id: string;
  user_id?: string;
  product: Product;
}

export interface WishlistContextType {
  items: WishlistItem[];
  isLoading: boolean;
  isAddingToWishlist: boolean;
  error: Error | null;
  addToWishlist: (product: Product) => Promise<void>;
  removeFromWishlist: (productId: string) => Promise<void>;
  isInWishlist: (productId: string) => boolean;
  clearWishlist: () => Promise<void>;
  // Aliases for consistency with other contexts
  addItem: (productIdOrProduct: string | Product) => Promise<void>;
  removeItem: (productId: string) => Promise<void>;
}
