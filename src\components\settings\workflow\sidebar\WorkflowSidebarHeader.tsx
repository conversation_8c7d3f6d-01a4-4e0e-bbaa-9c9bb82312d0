
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

const WorkflowSidebarHeader: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">Workflow Automation</h2>
      <p className="text-sm text-muted-foreground">
        Configure automated processes and notifications for your store operations.
      </p>
      
      <Button 
        variant="outline" 
        size="sm" 
        className="w-full justify-start" 
        onClick={() => navigate('/dashboard')}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Return to Dashboard
      </Button>
    </div>
  );
};

export default WorkflowSidebarHeader;
