
import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.36.0';

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// M-Pesa API credentials
const consumerKey = Deno.env.get('MPESA_CONSUMER_KEY') || '';
const consumerSecret = Deno.env.get('MPESA_CONSUMER_SECRET') || '';
const mpesaPasskey = Deno.env.get('MPESA_PASSKEY') || '';
const shortcode = Deno.env.get('MPESA_SHORTCODE') || '';
const callbackUrl = Deno.env.get('MPESA_CALLBACK_URL') || '';

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Function to get M-Pesa access token
async function getMpesaAccessToken() {
  if (!consumerKey || !consumerSecret) {
    throw new Error('M-Pesa API credentials not configured');
  }
  
  const auth = btoa(`${consumerKey}:${consumerSecret}`);
  
  try {
    console.log('Getting M-Pesa access token');
    const response = await fetch(
      'https://sandbox.safaricom.co.ke/oauth/v1/generate?grant_type=client_credentials',
      {
        method: 'GET',
        headers: {
          'Authorization': `Basic ${auth}`,
        },
      }
    );
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`Failed to get M-Pesa access token: ${response.status} ${errorData}`);
    }
    
    const data = await response.json();
    console.log('M-Pesa access token retrieved successfully');
    return data.access_token;
  } catch (error) {
    console.error('Error getting M-Pesa access token:', error);
    throw error;
  }
}

// Function to initiate STK Push
async function initiateSTKPush(accessToken, phoneNumber, amount, orderId) {
  if (!shortcode || !mpesaPasskey || !callbackUrl) {
    throw new Error('M-Pesa API configuration incomplete');
  }
  
  // Validate phone number format (should be 2547XXXXXXXX)
  const phoneRegex = /^2547\d{8}$/;
  if (!phoneRegex.test(phoneNumber)) {
    throw new Error('Invalid phone number format. Should be 2547XXXXXXXX');
  }
  
  // Prepare the timestamp
  const timestamp = new Date().toISOString().replace(/[-:\.]/g, '').slice(0, 14);
  
  // Generate the password
  const password = btoa(`${shortcode}${mpesaPasskey}${timestamp}`);
  
  try {
    console.log(`Initiating STK Push for order ${orderId} with amount ${amount}`);
    const response = await fetch(
      'https://sandbox.safaricom.co.ke/mpesa/stkpush/v1/processrequest',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          BusinessShortCode: shortcode,
          Password: password,
          Timestamp: timestamp,
          TransactionType: 'CustomerPayBillOnline',
          Amount: amount,
          PartyA: phoneNumber,
          PartyB: shortcode,
          PhoneNumber: phoneNumber,
          CallBackURL: callbackUrl,
          AccountReference: orderId,
          TransactionDesc: 'Payment for order ' + orderId,
        }),
      }
    );
    
    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`M-Pesa STK Push failed: ${response.status} ${errorData}`);
    }
    
    const data = await response.json();
    console.log(`STK Push initiated successfully for order ${orderId}`);
    return data;
  } catch (error) {
    console.error('Error initiating STK Push:', error);
    throw error;
  }
}

// Handle requests
serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Validate content type for non-OPTIONS requests
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return new Response(
        JSON.stringify({
          error: 'Invalid content type. Expected application/json',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    const { phoneNumber, amount, orderId, userId } = await req.json();
    
    // Validate input
    if (!phoneNumber || !amount || !orderId || !userId) {
      const missingParams = [];
      if (!phoneNumber) missingParams.push('phoneNumber');
      if (!amount) missingParams.push('amount');
      if (!orderId) missingParams.push('orderId');
      if (!userId) missingParams.push('userId');
      
      console.error('Missing required parameters:', missingParams);
      
      return new Response(
        JSON.stringify({
          error: 'Missing required parameters',
          details: `Required parameters: ${missingParams.join(', ')}`,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }
    
    // Validate M-Pesa credentials
    if (!consumerKey || !consumerSecret || !shortcode || !mpesaPasskey || !callbackUrl) {
      console.error('M-Pesa configuration incomplete');
      return new Response(
        JSON.stringify({
          error: 'Payment gateway configuration incomplete',
          details: 'The M-Pesa payment gateway is not properly configured',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 500,
        }
      );
    }
    
    // Get access token
    const accessToken = await getMpesaAccessToken();
    
    // Initiate STK Push
    const stkResponse = await initiateSTKPush(accessToken, phoneNumber, amount, orderId);
    
    // Update transaction in database
    if (stkResponse.ResponseCode === '0') {
      // Find the transaction for this order
      const { data: transactions, error: txError } = await supabase
        .from('payment_transactions')
        .select('*')
        .eq('order_id', orderId)
        .limit(1);
      
      if (txError) {
        console.error('Error fetching transaction from database:', txError);
        throw txError;
      }
      
      if (transactions && transactions.length > 0) {
        const transaction = transactions[0];
        
        // Update transaction with STK push details
        const { error: updateError } = await supabase
          .from('payment_transactions')
          .update({
            status: 'processing',
            payment_data: {
              ...transaction.payment_data,
              stk_push_response: stkResponse,
              checkout_request_id: stkResponse.CheckoutRequestID,
              merchant_request_id: stkResponse.MerchantRequestID,
              phone_number: phoneNumber,
              initiated_at: new Date().toISOString()
            }
          })
          .eq('id', transaction.id);
        
        if (updateError) {
          console.error('Error updating transaction in database:', updateError);
          throw updateError;
        }
        
        console.log(`Successfully updated transaction for order ${orderId}`);
      } else {
        console.log(`No transaction found for order ${orderId}, creating new one`);
        
        // Create a new transaction if one doesn't exist
        const { error: insertError } = await supabase
          .from('payment_transactions')
          .insert({
            order_id: orderId,
            user_id: userId,
            amount: amount,
            currency: 'KES', // Assuming M-Pesa payments are in Kenyan Shillings
            payment_method: 'mpesa',
            status: 'processing',
            payment_data: {
              stk_push_response: stkResponse,
              checkout_request_id: stkResponse.CheckoutRequestID,
              merchant_request_id: stkResponse.MerchantRequestID,
              phone_number: phoneNumber,
              initiated_at: new Date().toISOString()
            }
          });
        
        if (insertError) {
          console.error('Error creating new transaction in database:', insertError);
          throw insertError;
        }
      }
    } else {
      console.error('M-Pesa STK Push failed with response code:', stkResponse.ResponseCode);
    }
    
    return new Response(
      JSON.stringify(stkResponse),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error processing M-Pesa payment:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
