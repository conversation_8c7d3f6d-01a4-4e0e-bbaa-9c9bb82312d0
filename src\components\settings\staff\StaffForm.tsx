
import React from 'react';
import { z } from 'zod';
import { useForm, FormProvider } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Form } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { StaffFormValues, StaffMember } from './types';
import ContactInfoSection from './form/ContactInfoSection';
import RoleSelector from './form/RoleSelector';
import PermissionsSection, { permissionOptions } from './form/PermissionsSection';
import { Loader2 } from 'lucide-react';

const formSchema = z.object({
  email: z.string().email({ message: 'Please enter a valid email address' }),
  role: z.enum(['admin', 'manager', 'staff'], {
    required_error: 'Please select a role',
  }),
  permissions: z.record(z.boolean()).optional(),
  whatsappNumber: z.string().optional(),
  receiveNotifications: z.boolean().optional(),
});

interface StaffFormProps {
  onSubmit: (data: StaffFormValues) => void;
  initialValues?: StaffMember;
  isLoading?: boolean;
}

const StaffForm: React.FC<StaffFormProps> = ({ 
  onSubmit, 
  initialValues,
  isLoading = false 
}) => {
  const form = useForm<StaffFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: initialValues ? {
      email: initialValues.email,
      role: initialValues.role === 'owner' ? 'admin' : initialValues.role as 'admin' | 'manager' | 'staff',
      permissions: initialValues.permissions || {},
      whatsappNumber: initialValues.whatsappNumber || '',
      receiveNotifications: initialValues.receiveNotifications || false
    } : {
      email: '',
      role: 'staff',
      permissions: {
        orders: false,
        products: false,
        customers: false,
        settings: false,
        reports: false
      },
      whatsappNumber: '',
      receiveNotifications: false
    },
  });

  React.useEffect(() => {
    const role = form.watch('role');
    
    if (role === 'admin') {
      const adminPermissions = permissionOptions.reduce((acc, perm) => {
        acc[perm.id] = true;
        return acc;
      }, {} as Record<string, boolean>);
      
      form.setValue('permissions', adminPermissions);
    } else if (role === 'manager') {
      const managerPermissions = {
        orders: true,
        products: true,
        customers: true,
        settings: false,
        reports: true
      };
      
      form.setValue('permissions', managerPermissions);
    } else {
      const staffPermissions = {
        orders: true,
        products: false,
        customers: false,
        settings: false,
        reports: false
      };
      
      form.setValue('permissions', staffPermissions);
    }
  }, [form.watch('role')]);

  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <ContactInfoSection isEditMode={!!initialValues} />
          <RoleSelector />
          <PermissionsSection />

          <div className="flex justify-end space-x-2">
            <Button 
              type="submit" 
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {initialValues ? 'Updating...' : 'Adding...'}
                </>
              ) : (
                initialValues ? 'Update Staff' : 'Add Staff'
              )}
            </Button>
          </div>
        </form>
      </Form>
    </FormProvider>
  );
};

export default StaffForm;
