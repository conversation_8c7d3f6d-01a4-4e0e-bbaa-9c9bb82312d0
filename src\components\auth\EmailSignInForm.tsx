
import React from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "react-router-dom";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Loader2, Mail, Lock } from "lucide-react";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
});

export type EmailSignInFormValues = z.infer<typeof formSchema>;

interface EmailSignInFormProps {
  onSubmit: (data: EmailSignInFormValues) => void;
  isSubmitting: boolean;
  isLoading: boolean;
}

const EmailSignInForm: React.FC<EmailSignInFormProps> = ({ 
  onSubmit, 
  isSubmitting, 
  isLoading 
}) => {
  const form = useForm<EmailSignInFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleSubmit = (data: EmailSignInFormValues) => {
    onSubmit(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4 w-full">
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Email</FormLabel>
              <FormControl>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground pointer-events-none"/>
                  <Input 
                    placeholder="<EMAIL>" 
                    type="email"
                    autoComplete="email"
                    className="pl-11"
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Password</FormLabel>
              <FormControl>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-muted-foreground pointer-events-none"/>
                  <Input 
                    type="password" 
                    placeholder="********" 
                    autoComplete="current-password"
                    className="pl-11"
                    {...field} 
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end items-center mt-1">
          <Link to="/forgot-password" className="text-sm text-green-600 hover:underline font-medium">
            Forgot password?
          </Link>
        </div>

        <Button 
          type="submit" 
          className="w-full bg-green-500 hover:bg-green-600 text-white font-semibold py-2 rounded-xl transition"
          disabled={isSubmitting}
        >
          {(isLoading || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isLoading || isSubmitting ? "Signing in..." : "Sign In"}
        </Button>
      </form>
    </Form>
  );
};

export default EmailSignInForm;
