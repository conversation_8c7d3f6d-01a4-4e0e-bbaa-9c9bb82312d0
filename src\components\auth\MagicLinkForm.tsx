
import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/contexts";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type FormValues = z.infer<typeof formSchema>;

interface MagicLinkFormProps {
  onSuccess?: () => void;
}

const MagicLinkForm: React.FC<MagicLinkFormProps> = ({ onSuccess }) => {
  const { sendMagicLink } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (isSubmitting) return;
    
    setFormError(null);
    setIsSubmitting(true);
    
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Sending magic link...");
      
      const { success, error } = await sendMagicLink(data.email);
      
      if (toastId) toast.dismiss(toastId);
      
      if (!success) {
        throw error;
      }
      
      setEmailSent(true);
      toast.success("Magic link sent! Please check your email.");
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Magic link error:", error);
      if (toastId) toast.dismiss(toastId);
      setFormError(error.message || "Failed to send magic link");
      toast.error(error.message || "Failed to send magic link");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {emailSent ? (
        <Alert className="mb-6 bg-green-50">
          <AlertDescription>
            <p className="text-sm">
              We've sent a magic link to your email. Click the link in the email to sign in.
            </p>
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {formError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? "Sending..." : "Send Magic Link"}
              </Button>
            </form>
          </Form>
        </>
      )}
    </>
  );
};

export default MagicLinkForm;
