
import React from 'react';
import { AdminStoreType } from '@/types/admin';
import { TableRow, TableCell } from '@/components/ui/table';
import StoreStatusBadge from './StoreStatusBadge';
import StoreActionsMenu from './StoreActionsMenu';

interface StoreTableRowProps {
  store: AdminStoreType;
  onStoreAction: (action: string, storeId: string) => void;
  formatDate: (dateString: string) => string;
}

const StoreTableRow: React.FC<StoreTableRowProps> = ({ 
  store, 
  onStoreAction, 
  formatDate 
}) => {
  return (
    <TableRow key={store.id}>
      <TableCell className="font-medium">{store.name}</TableCell>
      <TableCell>{store.url}</TableCell>
      <TableCell>{store.owner}</TableCell>
      <TableCell>
        <StoreStatusBadge status={store.status} />
      </TableCell>
      <TableCell>{store.productCount}</TableCell>
      <TableCell>{store.orderCount}</TableCell>
      <TableCell>{formatDate(store.createdAt)}</TableCell>
      <TableCell>
        <StoreActionsMenu
          storeId={store.id}
          storeName={store.name}
          storeUrl={store.url}
          status={store.status}
          onAction={onStoreAction}
        />
      </TableCell>
    </TableRow>
  );
};

export default StoreTableRow;
