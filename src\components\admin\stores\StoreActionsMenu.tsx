
import React from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import {
  MoreHorizontal,
  Eye,
  Store,
  Ban,
  Check,
  AlertTriangle,
} from 'lucide-react';

interface StoreActionsMenuProps {
  storeId: string;
  storeName: string;
  storeUrl: string;
  status: string;
  onAction: (action: string, storeId: string) => void;
}

const StoreActionsMenu: React.FC<StoreActionsMenuProps> = ({
  storeId,
  storeName,
  storeUrl,
  status,
  onAction,
}) => {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon">
          <MoreHorizontal className="h-4 w-4" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onAction('view', storeId)}>
          <Eye className="mr-2 h-4 w-4" />
          <span>View Store</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onAction('visit', storeId)}>
          <Store className="mr-2 h-4 w-4" />
          <span>Visit Storefront</span>
        </DropdownMenuItem>
        {status === 'pending-review' && (
          <DropdownMenuItem onClick={() => onAction('approve', storeId)}>
            <Check className="mr-2 h-4 w-4" />
            <span>Approve Store</span>
          </DropdownMenuItem>
        )}
        {status === 'active' && (
          <DropdownMenuItem onClick={() => onAction('suspend', storeId)}>
            <Ban className="mr-2 h-4 w-4" />
            <span>Suspend Store</span>
          </DropdownMenuItem>
        )}
        {status === 'suspended' && (
          <DropdownMenuItem onClick={() => onAction('reactivate', storeId)}>
            <Check className="mr-2 h-4 w-4" />
            <span>Reactivate Store</span>
          </DropdownMenuItem>
        )}
        <DropdownMenuItem onClick={() => onAction('flag', storeId)}>
          <AlertTriangle className="mr-2 h-4 w-4" />
          <span>Flag for Review</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StoreActionsMenu;
