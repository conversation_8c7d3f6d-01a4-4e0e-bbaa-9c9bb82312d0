
import React, { useState } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Home, 
  Plus, 
  Edit, 
  Trash2, 
  MapPin,
  Building,
  CheckCircle
} from "lucide-react";
import { useAuth } from "@/contexts";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { toast } from "sonner";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";

// Mock address types
type AddressType = "home" | "work" | "other";

// Mock address interface
interface Address {
  id: string;
  type: AddressType;
  isDefault: boolean;
  name: string;
  street: string;
  apartment?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  phone?: string;
  instructions?: string;
}

const mockAddresses: Address[] = [
  {
    id: "addr-1",
    type: "home",
    isDefault: true,
    name: "John Doe",
    street: "123 Main Street",
    city: "San Francisco",
    state: "CA",
    zipCode: "94105",
    country: "United States",
    phone: "************"
  }
];

// Country options for the form
const countries = ["United States", "Canada", "United Kingdom", "Australia", "Germany", "France", "Japan", "Brazil", "Kenya", "Nigeria", "South Africa"];

const AccountAddresses: React.FC = () => {
  const { user } = useAuth();
  const [addresses, setAddresses] = useState<Address[]>(mockAddresses);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingAddressId, setEditingAddressId] = useState<string | null>(null);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [addressToDelete, setAddressToDelete] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<Omit<Address, "id">>({
    type: "home",
    isDefault: false,
    name: "",
    street: "",
    city: "",
    state: "",
    zipCode: "",
    country: "United States",
    phone: "",
    instructions: ""
  });

  const resetForm = () => {
    setFormData({
      type: "home",
      isDefault: false,
      name: "",
      street: "",
      city: "",
      state: "",
      zipCode: "",
      country: "United States",
      phone: "",
      instructions: ""
    });
    setEditingAddressId(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleCheckboxChange = (checked: boolean) => {
    setFormData(prev => ({ ...prev, isDefault: checked }));
  };

  const handleEditAddress = (address: Address) => {
    setFormData(address);
    setEditingAddressId(address.id);
    setIsAddDialogOpen(true);
  };

  const handleDeleteAddress = (addressId: string) => {
    setAddressToDelete(addressId);
    setIsDeleteConfirmOpen(true);
  };

  const confirmDeleteAddress = () => {
    if (addressToDelete) {
      setAddresses(addresses.filter(a => a.id !== addressToDelete));
      toast.success("Address deleted successfully");
      setIsDeleteConfirmOpen(false);
      setAddressToDelete(null);
    }
  };

  const handleAddressFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // If setting as default, update other addresses
    let newAddresses = [...addresses];
    if (formData.isDefault) {
      newAddresses = newAddresses.map(addr => ({
        ...addr,
        isDefault: false
      }));
    }
    
    if (editingAddressId) {
      // Update existing address
      newAddresses = newAddresses.map(addr => 
        addr.id === editingAddressId ? { ...formData, id: addr.id } : addr
      );
      toast.success("Address updated successfully");
    } else {
      // Add new address
      newAddresses.push({
        ...formData,
        id: `addr-${Date.now()}`
      });
      toast.success("Address added successfully");
    }
    
    setAddresses(newAddresses);
    setIsAddDialogOpen(false);
    resetForm();
  };

  const getAddressTypeIcon = (type: AddressType) => {
    switch (type) {
      case 'home':
        return <Home className="h-4 w-4" />;
      case 'work':
        return <Building className="h-4 w-4" />;
      default:
        return <MapPin className="h-4 w-4" />;
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">My Addresses</h2>
          <p className="text-muted-foreground">Manage your shipping addresses</p>
        </div>
        <Button onClick={() => {
          resetForm();
          setIsAddDialogOpen(true);
        }}>
          <Plus className="h-4 w-4 mr-2" />
          Add New Address
        </Button>
      </div>
      
      {addresses.length === 0 ? (
        <Card className="text-center py-16">
          <CardContent>
            <MapPin className="h-16 w-16 mx-auto text-gray-300 mb-4" />
            <h3 className="text-xl font-medium mb-2">No addresses saved</h3>
            <p className="text-muted-foreground mb-6">
              Add a shipping address to make checkout faster.
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add New Address
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {addresses.map(address => (
            <Card key={address.id} className={`border ${address.isDefault ? 'border-primary' : ''}`}>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center">
                    {getAddressTypeIcon(address.type)}
                    <CardTitle className="text-base ml-2">
                      {address.type.charAt(0).toUpperCase() + address.type.slice(1)} Address
                    </CardTitle>
                  </div>
                  {address.isDefault && (
                    <div className="flex items-center text-xs text-primary">
                      <CheckCircle className="h-3 w-3 mr-1 fill-primary" />
                      Default
                    </div>
                  )}
                </div>
              </CardHeader>
              <CardContent className="py-2">
                <div className="text-sm space-y-1">
                  <p className="font-medium">{address.name}</p>
                  <p>{address.street}</p>
                  {address.apartment && <p>{address.apartment}</p>}
                  <p>{address.city}, {address.state} {address.zipCode}</p>
                  <p>{address.country}</p>
                  {address.phone && <p className="text-muted-foreground mt-1">{address.phone}</p>}
                </div>
              </CardContent>
              <CardFooter className="pt-2 flex justify-end gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => handleEditAddress(address)}
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  className="text-destructive border-destructive hover:bg-destructive/10"
                  onClick={() => handleDeleteAddress(address.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
      
      {/* Add/Edit Address Dialog */}
      <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>{editingAddressId ? "Edit Address" : "Add New Address"}</DialogTitle>
            <DialogDescription>
              {editingAddressId 
                ? "Update your shipping address information." 
                : "Add a new shipping address to your account."}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleAddressFormSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="col-span-2">
                <Label htmlFor="name">Full Name</Label>
                <Input 
                  id="name" 
                  name="name" 
                  value={formData.name} 
                  onChange={handleInputChange} 
                  placeholder="Enter recipient's full name" 
                  required 
                />
              </div>
              
              <div>
                <Label htmlFor="type">Address Type</Label>
                <Select 
                  value={formData.type} 
                  onValueChange={(value) => handleSelectChange("type", value)}
                >
                  <SelectTrigger id="type">
                    <SelectValue placeholder="Select type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="home">Home</SelectItem>
                    <SelectItem value="work">Work</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="street">Street Address</Label>
              <Input 
                id="street" 
                name="street" 
                value={formData.street} 
                onChange={handleInputChange} 
                placeholder="Street address or P.O. Box" 
                required 
              />
            </div>
            
            <div>
              <Label htmlFor="apartment">Apartment, Suite, etc. (optional)</Label>
              <Input 
                id="apartment" 
                name="apartment" 
                value={formData.apartment || ""} 
                onChange={handleInputChange} 
                placeholder="Apt, Suite, Unit, etc." 
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="city">City</Label>
                <Input 
                  id="city" 
                  name="city" 
                  value={formData.city} 
                  onChange={handleInputChange} 
                  placeholder="City" 
                  required 
                />
              </div>
              <div>
                <Label htmlFor="state">State / Province</Label>
                <Input 
                  id="state" 
                  name="state" 
                  value={formData.state} 
                  onChange={handleInputChange} 
                  placeholder="State" 
                  required 
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="zipCode">Zip / Postal Code</Label>
                <Input 
                  id="zipCode" 
                  name="zipCode" 
                  value={formData.zipCode} 
                  onChange={handleInputChange} 
                  placeholder="Zip code" 
                  required 
                />
              </div>
              <div>
                <Label htmlFor="country">Country</Label>
                <Select 
                  value={formData.country} 
                  onValueChange={(value) => handleSelectChange("country", value)}
                >
                  <SelectTrigger id="country">
                    <SelectValue placeholder="Select country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map(country => (
                      <SelectItem key={country} value={country}>{country}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="phone">Phone Number (optional)</Label>
              <Input 
                id="phone" 
                name="phone" 
                value={formData.phone || ""} 
                onChange={handleInputChange} 
                placeholder="For delivery questions" 
              />
            </div>
            
            <div>
              <Label htmlFor="instructions">Delivery Instructions (optional)</Label>
              <Textarea 
                id="instructions" 
                name="instructions" 
                value={formData.instructions || ""} 
                onChange={handleInputChange} 
                placeholder="Special instructions for delivery" 
                rows={2} 
              />
            </div>
            
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox 
                id="isDefault" 
                checked={formData.isDefault} 
                onCheckedChange={handleCheckboxChange} 
              />
              <Label 
                htmlFor="isDefault" 
                className="text-sm font-normal cursor-pointer"
              >
                Set as default shipping address
              </Label>
            </div>
            
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setIsAddDialogOpen(false);
                  resetForm();
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {editingAddressId ? "Save Changes" : "Add Address"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteConfirmOpen} onOpenChange={setIsDeleteConfirmOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Address</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this address? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteConfirmOpen(false)}
            >
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDeleteAddress}
            >
              Delete Address
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AccountAddresses;
