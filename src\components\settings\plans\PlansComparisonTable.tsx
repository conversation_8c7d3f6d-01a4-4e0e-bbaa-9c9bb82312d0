
import React from "react";
import { Check } from "lucide-react";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";

export interface ComparisonFeature {
  name: string;
  basic?: boolean;
  premium?: boolean;
  business?: boolean;
}

interface ComparisonTableProps {
  features: ComparisonFeature[];
  collapsible?: boolean;
}

const PlansComparisonTable: React.FC<ComparisonTableProps> = ({
  features,
  collapsible = false
}) => {
  const table = (
    <div className="overflow-x-auto border rounded-lg bg-muted/30">
      <table className="min-w-[640px] w-full text-sm">
        <thead>
          <tr className="border-b">
            <th className="py-3 px-4 text-left font-semibold">Feature</th>
            <th className="py-3 px-4 text-center font-semibold">Basic</th>
            <th className="py-3 px-4 text-center font-semibold">Premium</th>
            <th className="py-3 px-4 text-center font-semibold">Business</th>
          </tr>
        </thead>
        <tbody>
          {features.map((f, idx) => (
            <tr key={f.name} className={idx < features.length - 1 ? "border-b" : ""}>
              <td className="py-2 px-4">{f.name}</td>
              <td className="py-2 px-4 text-center">
                {f.basic ? <Check className="mx-auto text-green-500 h-4 w-4" /> : "-"}
              </td>
              <td className="py-2 px-4 text-center">
                {f.premium ? <Check className="mx-auto text-green-500 h-4 w-4" /> : "-"}
              </td>
              <td className="py-2 px-4 text-center">
                {f.business ? <Check className="mx-auto text-green-500 h-4 w-4" /> : "-"}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );

  if (!collapsible) return table;

  return (
    <Accordion type="single" collapsible defaultValue="comparison">
      <AccordionItem value="comparison">
        <AccordionTrigger className="justify-center font-bold text-lg my-0">Compare Plans</AccordionTrigger>
        <AccordionContent>
          {table}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default PlansComparisonTable;
