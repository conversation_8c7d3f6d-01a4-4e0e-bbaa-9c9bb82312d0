
import React from 'react';
import { Star } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Product } from '@/types/unified-product';

interface ProductCardProps {
  product?: Product;
  showRatings: boolean;
  showQuickAdd: boolean;
  layoutType: string;
  cornerRadius: number;
  fontFamily: string;
  darkMode: boolean;
  buttonClasses: string;
  primaryColor: string;
  imageClasses: string;
  isPlaceholder?: boolean;
  index?: number;
}

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  showRatings,
  showQuickAdd,
  layoutType,
  cornerRadius,
  fontFamily,
  darkMode,
  buttonClasses,
  primaryColor,
  imageClasses,
  isPlaceholder = false,
  index = 0
}) => {
  if (isPlaceholder) {
    return (
      <div 
        key={index} 
        className={cn(
          "border overflow-hidden flex flex-col",
          layoutType === 'list' ? "flex-row items-center" : "h-full",
          darkMode ? "bg-gray-800 border-gray-700" : "bg-white",
          `rounded-[${cornerRadius}px]`
        )}
      >
        <div className={cn(
          layoutType === 'list' ? "w-1/3" : "w-full aspect-square",
          "bg-gray-100 dark:bg-gray-700"
        )}>
        </div>
        <div className={cn(
          "p-4",
          layoutType === 'list' ? "flex-1" : ""
        )}>
          <div className="h-5 w-32 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
          {showRatings && (
            <div className="h-4 w-16 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
          )}
          <div className="h-6 w-20 bg-gray-200 dark:bg-gray-600 rounded mb-2"></div>
          {showQuickAdd && (
            <div className="h-8 w-24 bg-gray-200 dark:bg-gray-600 rounded mt-2"></div>
          )}
        </div>
      </div>
    );
  }

  return (
    <div 
      className={cn(
        "border overflow-hidden flex flex-col",
        layoutType === 'list' ? "flex-row items-center" : "h-full",
        darkMode ? "bg-gray-800 border-gray-700" : "bg-white",
        `rounded-[${cornerRadius}px]`
      )}
    >
      <div className={cn(
        layoutType === 'list' ? "w-1/3" : "w-full aspect-square",
      )}>
        <img 
          src={product?.images && product.images.length > 0 ? product.images[0] : '/placeholder.svg'} 
          alt={product?.name} 
          className={cn("w-full h-full", imageClasses)}
        />
      </div>
      <div className={cn(
        "p-4",
        layoutType === 'list' ? "flex-1" : ""
      )}>
        <h3 className={cn("font-medium mb-1", `font-${fontFamily}`)}>
          {product?.name}
        </h3>
        {showRatings && (
          <div className="flex items-center gap-1 mb-2">
            <Star className="h-4 w-4 fill-amber-400 text-amber-400" />
            <span className="text-sm text-gray-500">4.5</span>
          </div>
        )}
        <div className="font-bold mb-2">{product?.currency}{product?.price}</div>
        {showQuickAdd && (
          <Button 
            className={cn(buttonClasses, "mt-2", primaryColor)}
            size="sm"
          >
            Add to Cart
          </Button>
        )}
      </div>
    </div>
  );
};

export default ProductCard;
