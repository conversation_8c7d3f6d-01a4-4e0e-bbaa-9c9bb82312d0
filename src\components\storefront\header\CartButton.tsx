
import React from "react";
import { Link } from "react-router-dom";
import { ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useCart } from "@/contexts";

interface CartButtonProps {
  variant?: "icon" | "full";
  onClick?: () => void;
}

const CartButton: React.FC<CartButtonProps> = ({ 
  variant = "icon",
  onClick
}) => {
  const { itemCount } = useCart();

  if (variant === "full") {
    return (
      <Link to="/shop/cart" onClick={onClick} className="relative">
        <Button variant="outline">
          <ShoppingCart className="h-4 w-4 mr-2" />
          Cart
          {itemCount > 0 && (
            <Badge className="ml-2">{itemCount}</Badge>
          )}
        </Button>
      </Link>
    );
  }

  return (
    <Link to="/shop/cart" className="relative">
      <Button variant="ghost" size="icon">
        <ShoppingCart className="h-5 w-5" />
        {itemCount > 0 && (
          <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center">
            {itemCount}
          </Badge>
        )}
      </Button>
    </Link>
  );
};

export default CartButton;
