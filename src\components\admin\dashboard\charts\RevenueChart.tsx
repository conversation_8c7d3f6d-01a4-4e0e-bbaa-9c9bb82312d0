
import React from 'react';
import { Card, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { AreaChart } from '@/components/ui/chart';
import { TrendingUp } from 'lucide-react';

interface RevenueChartProps {
  data: { name: string; total: number }[];
  formatCurrency: (amount: number) => string;
}

const RevenueChart: React.FC<RevenueChartProps> = ({ data, formatCurrency }) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base flex items-center">
          <TrendingUp className="h-4 w-4 mr-2 text-green-500" />
          Revenue Growth
        </CardTitle>
        <CardDescription>Monthly revenue over time</CardDescription>
      </CardHeader>
      <CardContent>
        <AreaChart 
          data={data}
          index="name"
          categories={["total"]}
          colors={["green"]}
          valueFormatter={(value) => formatCurrency(value)}
          className="h-[300px]"
        />
      </CardContent>
    </Card>
  );
};

export default RevenueChart;
