
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ShoppingCart, Package, Truck } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ActivityItem {
  id: string;
  ruleName: string;
  customer: string;
  timestamp: string;
  status: 'success' | 'failed';
}

interface ActivityTabProps {
  recentActivity: ActivityItem[];
}

const ActivityTab: React.FC<ActivityTabProps> = ({ recentActivity }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Recent workflow executions and their status</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentActivity.map(activity => (
            <div key={activity.id} className="flex items-start gap-3 p-3 border rounded-md">
              <div className={`mt-0.5 rounded-full p-2 ${
                activity.status === 'success' ? 'bg-green-100' : 'bg-red-100'
              }`}>
                {activity.ruleName.includes('Order Confirmation') && (
                  <ShoppingCart className={`h-4 w-4 ${
                    activity.status === 'success' ? 'text-green-600' : 'text-red-600'
                  }`} />
                )}
                {activity.ruleName.includes('Processing') && (
                  <Package className={`h-4 w-4 ${
                    activity.status === 'success' ? 'text-green-600' : 'text-red-600'
                  }`} />
                )}
                {activity.ruleName.includes('Shipping') && (
                  <Truck className={`h-4 w-4 ${
                    activity.status === 'success' ? 'text-green-600' : 'text-red-600'
                  }`} />
                )}
              </div>
              <div className="flex-1">
                <div className="flex justify-between">
                  <p className="font-medium">{activity.ruleName}</p>
                  <Badge variant={activity.status === 'success' ? 'default' : 'destructive'} className="text-xs">
                    {activity.status === 'success' ? 'Success' : 'Failed'}
                  </Badge>
                </div>
                <p className="text-sm">Customer: {activity.customer}</p>
                <div className="flex justify-between mt-1">
                  <p className="text-xs text-muted-foreground">{activity.timestamp}</p>
                  <Button variant="link" className="h-auto p-0 text-xs">View details</Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ActivityTab;
