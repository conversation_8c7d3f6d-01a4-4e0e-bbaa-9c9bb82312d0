
/**
 * Performs a deep comparison between two values to determine if they are equivalent.
 * This is a simplified version of lodash's isEqual.
 *
 * @param {*} value - The value to compare
 * @param {*} other - The other value to compare
 * @returns {boolean} Returns true if the values are equivalent, else false
 */
function isEqual(value, other) {
  // Handle simple cases
  if (value === other) return true;
  if (value == null || other == null || typeof value !== 'object' || typeof other !== 'object') return false;

  // Handle dates
  if (value instanceof Date && other instanceof Date) {
    return value.getTime() === other.getTime();
  }
  
  // Handle arrays
  if (Array.isArray(value) && Array.isArray(other)) {
    if (value.length !== other.length) return false;
    for (let i = 0; i < value.length; i++) {
      if (!isEqual(value[i], other[i])) return false;
    }
    return true;
  }
  
  // Handle objects
  const keysA = Object.keys(value);
  const keysB = Object.keys(other);
  if (keysA.length !== keysB.length) return false;
  for (const key of keysA) {
    if (!Object.prototype.hasOwnProperty.call(other, key) || !isEqual(value[key], other[key])) {
      return false;
    }
  }
  
  return true;
}

// Support both ESM and CJS
export default isEqual;
