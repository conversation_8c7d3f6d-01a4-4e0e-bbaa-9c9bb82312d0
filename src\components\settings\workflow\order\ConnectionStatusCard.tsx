
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, RefreshCw, AlertCircle, Settings } from 'lucide-react';

interface ConnectionStatusCardProps {
  connectionStatus: 'connected' | 'connecting' | 'disconnected';
}

const ConnectionStatusCard: React.FC<ConnectionStatusCardProps> = ({ connectionStatus }) => {
  return (
    <Card className="border-green-200 bg-green-50">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {connectionStatus === 'connected' ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : connectionStatus === 'connecting' ? (
              <RefreshCw className="h-5 w-5 text-amber-600 animate-spin" />
            ) : (
              <AlertCircle className="h-5 w-5 text-red-600" />
            )}
            <div>
              <p className="font-medium">Pubbly Connect</p>
              <p className="text-sm text-green-700">
                {connectionStatus === 'connected' 
                  ? 'Order automation system connected' 
                  : connectionStatus === 'connecting'
                  ? 'Checking connection...'
                  : 'Disconnected'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" className="border-green-200 text-green-700">
              <RefreshCw className="h-4 w-4 mr-2" />
              Check Status
            </Button>
            <Button variant="outline" size="sm" className="border-green-200 text-green-700">
              <Settings className="h-4 w-4 mr-2" />
              Configure
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ConnectionStatusCard;
