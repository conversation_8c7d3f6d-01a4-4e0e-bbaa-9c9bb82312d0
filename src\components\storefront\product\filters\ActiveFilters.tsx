
import React from 'react';
import { X } from 'lucide-react';

interface ActiveFiltersProps {
  selectedCategory: string | null;
  searchQuery: string;
  minPrice: string;
  maxPrice: string;
  onClearCategory: () => void;
  onClearSearch: () => void;
  onClearMinPrice: () => void;
  onClearMaxPrice: () => void;
}

const ActiveFilters: React.FC<ActiveFiltersProps> = ({
  selectedCategory,
  searchQuery,
  minPrice,
  maxPrice,
  onClearCategory,
  onClearSearch,
  onClearMinPrice,
  onClearMaxPrice,
}) => {
  if (!selectedCategory && !searchQuery && !minPrice && !maxPrice) return null;

  return (
    <div className="flex items-center text-sm mb-4">
      <span className="text-gray-500 mr-2">Active filters:</span>
      <div className="flex flex-wrap gap-1">
        {selectedCategory && (
          <span className="bg-gray-100 px-2 py-1 rounded-full flex items-center">
            Category: {selectedCategory}
            <X className="h-3 w-3 ml-1 cursor-pointer" onClick={onClearCategory} />
          </span>
        )}
        {searchQuery && (
          <span className="bg-gray-100 px-2 py-1 rounded-full flex items-center">
            Search: {searchQuery}
            <X className="h-3 w-3 ml-1 cursor-pointer" onClick={onClearSearch} />
          </span>
        )}
        {minPrice && (
          <span className="bg-gray-100 px-2 py-1 rounded-full flex items-center">
            Min Price: {minPrice}
            <X className="h-3 w-3 ml-1 cursor-pointer" onClick={onClearMinPrice} />
          </span>
        )}
        {maxPrice && (
          <span className="bg-gray-100 px-2 py-1 rounded-full flex items-center">
            Max Price: {maxPrice}
            <X className="h-3 w-3 ml-1 cursor-pointer" onClick={onClearMaxPrice} />
          </span>
        )}
      </div>
    </div>
  );
};

export default ActiveFilters;
