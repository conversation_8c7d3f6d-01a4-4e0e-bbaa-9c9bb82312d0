
import React, { useState } from 'react';
import { StoreFormData } from '@/types/store';
import { Eye, Palette, Layout, Type } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PreviewPanel from '../preview/PreviewPanel';
import TabTheme from './theme/TabTheme';
import TabColors from './theme/TabColors';
import TabLayout from './theme/TabLayout';
import TabTypography from './theme/TabTypography';

interface ThemeStepProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

// Theme options data
const themes = [
  {
    id: 'default',
    name: 'Modern (Default)',
    image: '/placeholder.svg',
    description: 'Clean and modern design with focus on product showcasing',
  },
  {
    id: 'minimal',
    name: 'Minimal',
    image: '/placeholder.svg',
    description: 'Minimalist design with focus on typography and whitespace',
  },
  {
    id: 'boutique',
    name: 'Boutique',
    image: '/placeholder.svg',
    description: 'Elegant design perfect for fashion and luxury products',
  },
  {
    id: 'tech',
    name: 'Tech',
    image: '/placeholder.svg',
    description: 'Bold design with tech-inspired elements and dark mode',
  },
];

const colorSchemes = [
  { id: 'green', name: 'Green (Default)', primary: '#10b981', secondary: '#d1fae5' },
  { id: 'blue', name: 'Blue', primary: '#3b82f6', secondary: '#dbeafe' },
  { id: 'purple', name: 'Purple', primary: '#8b5cf6', secondary: '#ede9fe' },
  { id: 'amber', name: 'Amber', primary: '#f59e0b', secondary: '#fef3c7' },
  { id: 'red', name: 'Red', primary: '#ef4444', secondary: '#fee2e2' },
];

const layoutOptions = [
  { id: 'grid', name: 'Grid (Default)', description: 'Products displayed in a grid layout' },
  { id: 'list', name: 'List', description: 'Products displayed in a list layout' },
  { id: 'masonry', name: 'Masonry', description: 'Products displayed in a varied height grid' },
];

const fontOptions = [
  { id: 'inter', name: 'Inter (Default)', sample: 'The quick brown fox jumps over the lazy dog' },
  { id: 'poppins', name: 'Poppins', sample: 'The quick brown fox jumps over the lazy dog' },
  { id: 'playfair', name: 'Playfair Display', sample: 'The quick brown fox jumps over the lazy dog' },
  { id: 'roboto', name: 'Roboto', sample: 'The quick brown fox jumps over the lazy dog' },
];

const ThemeStep: React.FC<ThemeStepProps> = ({ data, updateData }) => {
  const defaultThemeOptions = {
    primaryColor: '#10b981', // Make primaryColor required, not optional
    darkMode: false,
    customHeader: false,
    customFooter: false,
    colorScheme: 'green',
    layoutType: 'grid',
    cornerRadius: 8,
    fontFamily: 'inter',
    productCardsPerRow: 4,
    showProductRatings: true,
    showQuickAdd: true,
    enableAnimations: true,
    heroStyle: 'image',
    productImageStyle: 'contain',
    buttonStyle: 'rounded',
  };

  const [themeOptions, setThemeOptions] = React.useState({
    ...defaultThemeOptions,
    ...(data.themeOptions || {})
  });
  
  const [selectedTheme, setSelectedTheme] = React.useState(data.themeId || 'default');
  const [previewOpen, setPreviewOpen] = React.useState(false);

  const handleUpdateThemeOptions = (updates: Partial<typeof themeOptions>) => {
    const newOptions = { ...themeOptions, ...updates };
    setThemeOptions(newOptions);
    updateData({ 
      themeId: selectedTheme,
      themeOptions: newOptions 
    });
  };

  const handleSelectTheme = (themeId: string) => {
    setSelectedTheme(themeId);
    updateData({ themeId });
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-4">Store Appearance</h2>
      <p className="text-muted-foreground mb-6">
        Customize how your store looks and feels to your customers. Changes will be reflected in real-time.
      </p>

      <PreviewPanel 
        data={data} 
        open={previewOpen} 
        onOpenChange={setPreviewOpen}
      />

      <Tabs defaultValue="theme" className="mb-8 mt-8">
        <TabsList className="grid grid-cols-4 mb-4">
          <TabsTrigger value="theme" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span>Theme</span>
          </TabsTrigger>
          <TabsTrigger value="colors" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span>Colors</span>
          </TabsTrigger>
          <TabsTrigger value="layout" className="flex items-center gap-2">
            <Layout className="h-4 w-4" />
            <span>Layout</span>
          </TabsTrigger>
          <TabsTrigger value="typography" className="flex items-center gap-2">
            <Type className="h-4 w-4" />
            <span>Typography</span>
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="theme">
          <TabTheme
            themeOptions={themeOptions}
            selectedTheme={selectedTheme}
            handleUpdateThemeOptions={handleUpdateThemeOptions}
            handleSelectTheme={handleSelectTheme}
            themes={themes}
          />
        </TabsContent>
        
        <TabsContent value="colors">
          <TabColors
            themeOptions={themeOptions}
            handleUpdateThemeOptions={handleUpdateThemeOptions}
            colorSchemes={colorSchemes}
          />
        </TabsContent>
        
        <TabsContent value="layout">
          <TabLayout
            themeOptions={themeOptions}
            handleUpdateThemeOptions={handleUpdateThemeOptions}
            layoutOptions={layoutOptions}
          />
        </TabsContent>
        
        <TabsContent value="typography">
          <TabTypography
            themeOptions={themeOptions}
            handleUpdateThemeOptions={handleUpdateThemeOptions}
            fontOptions={fontOptions}
          />
        </TabsContent>
      </Tabs>

      <div className="border-t pt-6">
        <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Preview Settings
        </h3>
        <p className="text-sm text-muted-foreground mb-4">
          These changes will be immediately visible to customers when you save your store settings.
        </p>
        <Button 
          variant="outline" 
          onClick={() => setPreviewOpen(true)}
          className="flex items-center gap-2"
        >
          <Eye className="h-4 w-4" />
          Preview Storefront
        </Button>
      </div>
    </div>
  );
};

export default ThemeStep;
