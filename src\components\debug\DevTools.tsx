
import React, { useEffect, useState } from 'react';
import { debugApp } from '@/utils/debug';

interface DevToolsProps {
  children: React.ReactNode;
}

export const DevTools: React.FC<DevToolsProps> = ({ children }) => {
  const [debugMode, setDebugMode] = useState(false);
  const [loadingMetrics, setLoadingMetrics] = useState({
    startTime: performance.now(),
    firstRender: 0,
    fullyLoaded: 0
  });
  
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      // Log component mount
      debugApp.log('DevTools mounted');
      
      // Track loading performance
      const startTime = performance.now();
      setLoadingMetrics(prev => ({ ...prev, startTime }));
      
      // Record first render timing
      const firstRenderTime = performance.now() - startTime;
      setLoadingMetrics(prev => ({ ...prev, firstRender: firstRenderTime }));
      
      // Wait for full load
      window.addEventListener('load', () => {
        const fullyLoadedTime = performance.now() - startTime;
        setLoadingMetrics(prev => ({ ...prev, fullyLoaded: fullyLoadedTime }));
        debugApp.log(`App fully loaded in ${fullyLoadedTime.toFixed(0)}ms`);
      });
      
      // Check for debug mode in localStorage
      const savedDebugMode = localStorage.getItem('mduka_debug_mode');
      if (savedDebugMode === 'true') {
        setDebugMode(true);
        enableDebugFeatures();
      }
      
      // Add keyboard shortcut for toggling debug mode
      const handleKeyPress = (e: KeyboardEvent) => {
        if (e.ctrlKey && e.shiftKey && e.key === 'D') {
          const newMode = !debugMode;
          setDebugMode(newMode);
          localStorage.setItem('mduka_debug_mode', String(newMode));
          
          if (newMode) {
            enableDebugFeatures();
          } else {
            disableDebugFeatures();
          }
          
          debugApp.log(`Debug mode ${newMode ? 'enabled' : 'disabled'}`);
        }
      };

      window.addEventListener('keydown', handleKeyPress);
      return () => {
        window.removeEventListener('keydown', handleKeyPress);
        window.removeEventListener('load', () => {});
      };
    }
  }, [debugMode]);
  
  const enableDebugFeatures = () => {
    if (typeof window !== 'undefined') {
      // Add debug utilities to window for console access
      (window as any).__DEBUG_TOOLS__ = {
        debugApp,
        toggleDebugMode: () => setDebugMode(!debugMode),
        inspectComponents: () => {
          console.log('React component tree:', children);
        },
        checkPerformance: () => {
          const measures: Record<string, number> = {};
          
          performance.mark('start-check');
          for (let i = 0; i < 1000; i++) {
            const obj = { test: 'value' };
            Object.keys(obj);
          }
          performance.mark('end-check');
          performance.measure('basic-operations', 'start-check', 'end-check');
          
          const entries = performance.getEntriesByType('measure');
          entries.forEach(entry => {
            measures[entry.name] = entry.duration;
          });
          
          console.table(measures);
        },
        loadingMetrics
      };
      
      console.log(
        '%c Debug Mode Enabled - Press Ctrl+Shift+D to toggle or use window.__DEBUG_TOOLS__ in console',
        'background: #007700; color: white; font-size: 14px; padding: 4px;'
      );
    }
  };
  
  const disableDebugFeatures = () => {
    if (typeof window !== 'undefined') {
      delete (window as any).__DEBUG_TOOLS__;
      console.log(
        '%c Debug Mode Disabled - Press Ctrl+Shift+D to re-enable',
        'background: #770000; color: white; font-size: 14px; padding: 4px;'
      );
    }
  };

  return (
    <>
      {debugMode && (
        <div className="fixed bottom-4 right-4 bg-black/80 text-white p-2 rounded text-xs z-50 flex flex-col gap-1">
          <div>Debug Mode Active</div>
          <div className="text-xs text-green-300">
            Render: {loadingMetrics.firstRender.toFixed(0)}ms
          </div>
          {loadingMetrics.fullyLoaded > 0 && (
            <div className="text-xs text-blue-300">
              Loaded: {loadingMetrics.fullyLoaded.toFixed(0)}ms
            </div>
          )}
        </div>
      )}
      {children}
    </>
  );
};

export default DevTools;
