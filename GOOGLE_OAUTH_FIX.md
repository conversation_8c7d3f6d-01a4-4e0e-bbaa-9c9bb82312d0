# Google OAuth Fix Guide for M-Duka App

## 🚨 Current Issue
**Error:** `Unable to exchange external code: 4/0AVMBsJg6KOCS7lhGvAympF9AhduF4vriHxGwyJIFb-6rRBB6wLOd5MudfMNA_2ezczkCEA`

This error occurs when Google OAuth cannot exchange the authorization code for an access token due to configuration mismatches.

## 🛠️ Step-by-Step Fix

### 1. Supabase Configuration

1. **Go to Supabase Dashboard:**
   - Visit: https://supabase.com/dashboard/project/nheycjpozywomwscplcz
   - Navigate to **Authentication > Providers**

2. **Configure Google Provider:**
   - Click on **Google** provider
   - Ensure it's **enabled**
   - Set the **Redirect URL** to: `https://nheycjpozywomwscplcz.supabase.co/auth/v1/callback`
   - Add your Google OAuth credentials (Client ID and Client Secret)

### 2. Google Cloud Console Configuration

1. **Access Google Cloud Console:**
   - Go to: https://console.cloud.google.com/
   - Select your project or create a new one

2. **Enable Google+ API:**
   - Navigate to **APIs & Services > Library**
   - Search for "Google+ API" and enable it
   - Also enable "Google Identity API" if not already enabled

3. **Configure OAuth 2.0 Credentials:**
   - Go to **APIs & Services > Credentials**
   - Find your OAuth 2.0 Client ID or create a new one
   - Add these **Authorized redirect URIs**:
     ```
     https://nheycjpozywomwscplcz.supabase.co/auth/v1/callback
     https://www.m-duka.tz/auth/callback
     https://m-duka.tz/auth/callback
     http://localhost:8081/auth/callback
     ```

4. **Domain Verification:**
   - Go to **APIs & Services > Domain verification**
   - Add and verify these domains:
     - `m-duka.tz`
     - `www.m-duka.tz`
   - Follow Google's domain verification process

### 3. OAuth Consent Screen

1. **Configure Consent Screen:**
   - Go to **APIs & Services > OAuth consent screen**
   - Set **User Type** to "External" (unless you have Google Workspace)
   - Fill in required fields:
     - App name: "M-Duka"
     - User support email: your email
     - Developer contact information: your email
   - Add authorized domains:
     - `m-duka.tz`
     - `supabase.co`

2. **Scopes:**
   - Add these scopes:
     - `email`
     - `profile`
     - `openid`

### 4. Environment Variables Check

Ensure your production environment has these variables set:
```bash
VITE_PUBLIC_SUPABASE_URL=https://nheycjpozywomwscplcz.supabase.co
VITE_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g
```

### 5. Test the Fix

1. **Clear browser cache and cookies**
2. **Try Google sign-in again**
3. **Check browser console for any errors**
4. **Verify the redirect flow works correctly**

## 🔍 Debugging Steps

If the issue persists:

1. **Check Supabase Logs:**
   - Go to Supabase Dashboard > Logs
   - Look for authentication errors

2. **Verify Google Cloud Console Settings:**
   - Ensure all redirect URIs are correctly added
   - Check that the OAuth consent screen is properly configured

3. **Test with Different Browsers:**
   - Try incognito/private mode
   - Test with different browsers

## 📝 Code Changes Made

1. **Updated `socialAuthApi.ts`:**
   - Added production-specific redirect URL handling
   - Improved error handling and logging

2. **Created `AuthCallback.tsx`:**
   - Dedicated page for handling OAuth callbacks
   - Better error display and user feedback

3. **Updated routing:**
   - Added `/auth/callback` route for OAuth handling

## ⚠️ Important Notes

- **Domain verification** is crucial for production
- **Redirect URIs** must match exactly (including protocol and trailing slashes)
- **OAuth consent screen** must be properly configured for external users
- Test thoroughly in both development and production environments

## 🆘 If Issues Persist

1. Contact Google Cloud Support for domain verification issues
2. Check Supabase documentation for latest OAuth configuration
3. Verify that your Google Cloud project has the necessary APIs enabled
4. Ensure your domain is properly configured in DNS

---

**Last Updated:** January 2025
**Status:** Ready for implementation
