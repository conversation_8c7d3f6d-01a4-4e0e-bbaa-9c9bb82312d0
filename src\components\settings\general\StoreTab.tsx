
import React from 'react';
import { toast } from 'sonner';
import StoreTabContent from './StoreTabContent';
import { useStoreTabState } from '@/hooks/settings/useStoreTabState';
import { useStore } from '@/contexts';
import { databaseToUIStore } from '@/utils/typeConverters';

const StoreTab = () => {
  const { currentStore, updateStore } = useStore();
  const [loading, setLoading] = React.useState(false);
  
  // Use type assertion to ensure compatibility
  const uiStore = currentStore ? databaseToUIStore(currentStore) : null;
  
  const storeState = useStoreTabState(uiStore);
  
  const handleStoreSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Update store information including WhatsApp settings
      await updateStore(currentStore?.id || '', {
        name: storeState.storeName,
        description: storeState.storeDescription,
        store_url: storeState.storeUrl,
        notifications_email: storeState.email,
        whatsapp_settings: {
          businessNumber: storeState.whatsappNumber,
          enableOrderNotifications: storeState.enableOrderNotifications,
          enableCustomerUpdates: storeState.enableCustomerUpdates,
          customMessage: storeState.customMessage,
          autoReply: storeState.autoReply,
          enabled: true,
          number: storeState.whatsappNumber,
          message: storeState.customMessage || 'Thank you for your order!'
        }
      });
      
      toast.success('Store settings updated successfully!');
    } catch (error) {
      toast.error('Failed to update store settings');
      console.error('Store update error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <StoreTabContent
      {...storeState}
      loading={loading}
      setLoading={setLoading}
      handleSubmit={handleStoreSubmit}
    />
  );
};

export default StoreTab;
