import React, { useState } from 'react';
import { Card, CardContent } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Textarea } from './ui/textarea';
import { generateAIContent, AIGenerationRequest } from '@/services/aiGenerationService';
import { <PERSON>rkles, ArrowRight, Loader2, Copy, CheckCircle } from 'lucide-react';
import { Badge } from './ui/badge';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

interface AIChatWidgetProps {
  onContentGenerated?: (content: string) => void;
  contentType?: 'description' | 'features' | 'title' | 'seo' | 'general';
  placeholder?: string;
  label?: string;
  initialValue?: string;
  className?: string;
}

const AIChatWidget = ({
  onContentGenerated,
  contentType = 'description',
  placeholder = 'What kind of store are you creating?',
  label = 'AI Assistant',
  initialValue = '',
  className
}: AIChatWidgetProps) => {
  const [prompt, setPrompt] = useState('');
  const [generatedContent, setGeneratedContent] = useState(initialValue);
  const [isLoading, setIsLoading] = useState(false);
  const [copied, setCopied] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const { toast } = useToast();

  const handleContentGeneration = async () => {
    if (!prompt.trim()) {
      toast({
        title: 'Please enter a prompt',
        description: 'Describe your store to generate content',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);
    try {
      const request: AIGenerationRequest = {
        prompt,
        type: contentType
      };
      
      const response = await generateAIContent(request);
      
      if (response.isError) {
        toast({
          title: 'Error generating content',
          description: response.errorMessage || 'Please try again',
          variant: 'destructive',
        });
        return;
      }
      
      setGeneratedContent(response.content);
      if (onContentGenerated) {
        onContentGenerated(response.content);
      }
    } catch (error) {
      console.error('Error generating content:', error);
      toast({
        title: 'Error generating content',
        description: 'Something went wrong. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
    toast({
      title: 'Content copied!',
      description: 'The generated content has been copied to clipboard',
    });
  };

  const handleApply = () => {
    if (onContentGenerated && generatedContent) {
      onContentGenerated(generatedContent);
      toast({
        title: 'Content applied!',
        description: 'The generated content has been applied',
      });
    }
  };

  return (
    <Card className={cn("relative overflow-hidden border-dashed", className)}>
      <div className="absolute right-2 top-2">
        <Badge variant="outline" className="flex items-center gap-1 bg-primary/10">
          <Sparkles className="h-3 w-3" />
          <span>AI</span>
        </Badge>
      </div>
      <CardContent className={cn("pt-6", expanded ? "pb-6" : "pb-2")}>
        <div className="space-y-4">
          <div className="flex flex-col space-y-1.5">
            <p className="text-sm font-medium">{label}</p>
            <div className="flex gap-2">
              <Input
                placeholder={placeholder}
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="flex-grow"
                disabled={isLoading}
              />
              <Button
                onClick={handleContentGeneration}
                disabled={isLoading || !prompt.trim()}
                size="sm"
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <ArrowRight className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>

          {generatedContent && (
            <div className="space-y-3">
              <Textarea
                value={generatedContent}
                onChange={(e) => setGeneratedContent(e.target.value)}
                className="min-h-[100px] text-sm"
                placeholder="Generated content will appear here..."
              />
              
              <div className="flex justify-between">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={copyToClipboard}
                  className="gap-1"
                >
                  {copied ? (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Copied
                    </>
                  ) : (
                    <>
                      <Copy className="h-4 w-4" />
                      Copy
                    </>
                  )}
                </Button>
                
                <Button
                  size="sm"
                  onClick={handleApply}
                  disabled={!generatedContent}
                >
                  Apply
                </Button>
              </div>
            </div>
          )}

          {!expanded && !generatedContent && (
            <div className="flex justify-center mt-2">
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-xs"
                onClick={() => setExpanded(true)}
              >
                <Sparkles className="h-3 w-3 mr-1" />
                Let AI help you create content
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default AIChatWidget;
