import { Product, ProductVariant, toUIProduct } from '@/types/unified-product';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';
import { ReactNode } from 'react';

export type { Product, ProductVariant };
export { toUIProduct };
export type DatabaseProduct = Tables<'products'>;
export type InsertProduct = TablesInsert<'products'>;
export type UpdateProduct = TablesUpdate<'products'>;

export interface ProductFilters {
  searchTerm?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  isActive?: boolean;
  inStock?: boolean;
  tags?: string[];
}

export interface ProductContextType {
  products: Product[];
  filteredProducts: Product[];
  currentProduct: Product | null;
  isLoading: boolean;
  error: Error | null;
  searchTerm: string;
  filters: ProductFilters;
  categories: string[];
  fetchProducts: (storeId: string) => Promise<Product[]>;
  getProduct: (productId: string) => Promise<Product | null>;
  createProduct: (productData: InsertProduct, storeId: string) => Promise<Product>;
  updateProduct: (productId: string, productData: UpdateProduct) => Promise<Product>;
  deleteProduct: (productId: string) => Promise<void>;
  setCurrentProduct: (product: Product | null) => void;
  searchProducts: (searchTerm: string) => void;
  setFilters: (filters: ProductFilters) => void;
  clearFilters: () => void;
}

export interface ProductProviderProps {
  children: ReactNode;
}
