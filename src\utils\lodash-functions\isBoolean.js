
/**
 * Checks if value is classified as a boolean primitive or object.
 *
 * @param {*} value - The value to check
 * @returns {boolean} Returns true if value is a boolean, else false
 */
function isBoolean(value) {
  return value === true || value === false || 
         (value !== null && typeof value === 'object' && Object.prototype.toString.call(value) === '[object Boolean]');
}

// Support both ESM and CJS
export default isBoolean;
