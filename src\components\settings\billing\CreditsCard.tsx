
import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Wallet } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { formatCurrency } from "@/utils/formatters";

const CreditsCard = () => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wallet className="h-5 w-5" /> 
          Credits
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <p className="text-xl font-bold">{formatCurrency(0, "USD")} <span className="text-sm font-normal text-muted-foreground">USD</span></p>
            <p className="text-sm text-muted-foreground">You have no credits in your account</p>
          </div>
          <Button variant="outline">Add Credits</Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default CreditsCard;
