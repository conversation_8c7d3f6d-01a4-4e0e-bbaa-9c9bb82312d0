name: Deploy to lovable.dev

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build
        env:
          VITE_PUBLIC_SUPABASE_URL: ${{ secrets.VITE_PUBLIC_SUPABASE_URL }}
          VITE_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.VITE_PUBLIC_SUPABASE_ANON_KEY }}
          VITE_OPENAI_API_KEY: ${{ secrets.VITE_OPENAI_API_KEY }}
          VITE_RECAPTCHA_SITE_KEY: ${{ secrets.VITE_RECAPTCHA_SITE_KEY }}
          
      # Deploy to lovable.dev with your specific project ID
      - name: Deploy to lovable.dev
        uses: lovable-tech/lovable-deploy-action@v1
        with:
          api-key: ${{ secrets.LOVABLE_API_KEY }}
          site-id: "d6edfe1f-9b57-457c-b66b-98d94e822107"
          dist-dir: './dist'
          # Add custom domains configuration
          domains: '["m-duka.app", "viomify.com"]'