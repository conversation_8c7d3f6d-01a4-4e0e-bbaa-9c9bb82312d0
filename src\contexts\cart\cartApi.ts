
import { supabase } from '@/lib/supabase';
import { CartItem } from './types';
import { Product, ProductVariant } from '@/types/unified-product';
import { transformSupabaseProduct } from './utils';

// Fetch cart items for a user
export const fetchCartItems = async (userId: string): Promise<CartItem[]> => {
  try {
    const { data, error } = await supabase
      .from('cart_items')
      .select(`
        id,
        user_id,
        product_id,
        quantity,
        variant_id,
        products:product_id(*)
      `)
      .eq('user_id', userId);
      
    if (error) throw error;
    
    // Transform the data to match CartItem structure
    const cartItems: CartItem[] = data.map(item => ({
      id: item.id,
      user_id: item.user_id,
      product: transformSupabaseProduct(item.products),
      quantity: item.quantity,
      variant: item.variant_id ? { id: item.variant_id } as ProductVariant : undefined
    }));
    
    return cartItems;
  } catch (error) {
    console.error('Error fetching cart items:', error);
    return [];
  }
};

// Add or update cart item
export const addOrUpdateCartItem = async (itemData: {
  id?: string;
  user_id?: string;
  product?: Product;
  quantity?: number;
  variant?: ProductVariant;
}): Promise<string> => {
  try {
    // If item has an ID, update it
    if (itemData.id) {
      const { data, error } = await supabase
        .from('cart_items')
        .update({ quantity: itemData.quantity })
        .eq('id', itemData.id)
        .select()
        .single();
        
      if (error) throw error;
      return data.id;
    }
    
    // Otherwise, create a new item
    const { data, error } = await supabase
      .from('cart_items')
      .insert({
        user_id: itemData.user_id,
        product_id: itemData.product?.id,
        quantity: itemData.quantity || 1,
        variant_id: itemData.variant?.id
      })
      .select()
      .single();
      
    if (error) throw error;
    return data.id;
  } catch (error) {
    console.error('Error adding/updating cart item:', error);
    throw error;
  }
};

// Remove cart item
export const removeCartItem = async (itemId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('id', itemId);
      
    if (error) throw error;
  } catch (error) {
    console.error('Error removing cart item:', error);
    throw error;
  }
};

// Clear user cart
export const clearUserCart = async (userId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('cart_items')
      .delete()
      .eq('user_id', userId);
      
    if (error) throw error;
  } catch (error) {
    console.error('Error clearing user cart:', error);
    throw error;
  }
};
