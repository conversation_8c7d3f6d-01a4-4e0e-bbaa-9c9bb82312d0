
import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowRight, CheckCircle } from "lucide-react";
import { Container } from "@/components/ui/container";

const benefits = [
  "No credit card required to start",
  "Free tier available forever",
  "Set up your store in minutes",
  "World-class support"
];

const FinalCTA = () => {
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });

  return (
    <section className="py-20 sm:py-28" ref={sectionRef}>
      <Container>
        <div className="relative rounded-2xl overflow-hidden shadow-lg">
          <div className="absolute inset-0 bg-gradient-to-br from-green-600 to-green-600 opacity-90"></div>
          
          <div className="relative z-10 px-6 py-16 md:px-12 lg:px-16 text-white">
            <div className="max-w-3xl mx-auto text-center">
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5 }}
                className="text-3xl md:text-4xl font-bold mb-6"
              >
                Ready to transform your online business?
              </motion.h2>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="text-white/90 text-lg md:text-xl mb-8 max-w-2xl mx-auto"
              >
                Join thousands of successful businesses that trust M-Duka to power their online stores.
              </motion.p>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="flex flex-wrap justify-center gap-3 mb-8"
              >
                {benefits.map((benefit, index) => (
                  <div 
                    key={index} 
                    className="flex items-center gap-2 bg-white/10 rounded-full px-4 py-1.5 text-sm backdrop-blur-sm"
                  >
                    <CheckCircle className="h-4 w-4 text-green-300" />
                    <span>{benefit}</span>
                  </div>
                ))}
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <Button 
                  size="lg" 
                  className="bg-white text-green-600 hover:bg-white/90 rounded-full h-12 px-8 text-base font-medium"
                  asChild
                >
                  <Link to="/signup" className="flex items-center gap-2">
                    Get started for free <ArrowRight className="h-4 w-4" />
                  </Link>
                </Button>
                
                <Button 
                  variant="outline" 
                  size="lg" 
                  className="border-white/30 text-white hover:bg-white/10 rounded-full h-12 px-8 text-base font-medium"
                  asChild
                >
                  <Link to="/pricing">View pricing</Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
};

export default FinalCTA;
