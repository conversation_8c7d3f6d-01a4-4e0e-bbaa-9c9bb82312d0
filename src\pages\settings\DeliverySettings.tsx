
import React from 'react';
import { Form } from "@/components/ui/form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton"; // Import Skeleton for loading state
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"; // Import Alert for error state
import { AlertTriangle } from "lucide-react"; // Icon for alert
import { useDeliveryForm } from "@/hooks/settings/useDeliveryForm";
import CountrySelector from "@/components/settings/delivery/CountrySelector";
import DeliverySettingsHeader from "@/components/settings/delivery/DeliverySettingsHeader";
import DomesticShippingCard from "@/components/settings/delivery/DomesticShippingCard";
import LocalPickupCard from "@/components/settings/delivery/LocalPickupCard";
import InternationalShippingCard from "@/components/settings/delivery/InternationalShippingCard";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";

const DeliverySettings = () => {
  const { 
    form, 
    selectedCountry, 
    handleCountryChange, 
    onSubmit,
    isAfricanCountry,
    allAfricanRegions,  // Get fetched regions
    isLoadingRegions, // Get loading state
    errorLoadingRegions // Get error state
  } = useDeliveryForm();

  // Handle loading state
  if (isLoadingRegions) {
    return (
      <div className="space-y-6">
        <DeliverySettingsHeader />
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-48 w-full" />
      </div>
    );
  }

  // Handle error state
  if (errorLoadingRegions) {
    return (
      <div className="space-y-6">
        <DeliverySettingsHeader />
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Error Loading Regions</AlertTitle>
          <AlertDescription>{errorLoadingRegions}</AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <DeliverySettingsHeader />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Card>
            <CardHeader>
              <CardTitle>Location Settings</CardTitle>
              <CardDescription>Configure your primary location and country settings.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* CountrySelector might need regions if it does region-specific logic, 
                  but for now, assuming it only needs the country list */} 
              <CountrySelector form={form} onCountryChange={handleCountryChange} />
            </CardContent>
          </Card>

          {/* Pass fetched regions data to DomesticShippingCard */}
          <DomesticShippingCard 
            form={form}
            isAfricanCountry={isAfricanCountry}
            selectedCountry={selectedCountry}
            africanRegions={allAfricanRegions[form.getValues('country')] || []} // Pass regions for the selected country
          />

          <LocalPickupCard form={form} />

          {/* InternationalShippingCard might need regions too depending on implementation */}
          <InternationalShippingCard form={form} /> 

          <Separator />

          <div className="flex justify-end">
            <Button type="submit" disabled={form.formState.isSubmitting}>Save Settings</Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default DeliverySettings;
