
export interface StaffMember {
  id: string;
  email: string;
  role: 'owner' | 'admin' | 'manager' | 'staff';
  status: 'active' | 'pending' | 'invited';
  permissions: Record<string, boolean>;
  joinedAt?: string;
  invitedAt: string;
  whatsappNumber?: string;
  receiveNotifications?: boolean;
}

export interface StaffFormValues {
  email: string;
  role: 'admin' | 'manager' | 'staff';
  permissions?: Record<string, boolean>;
  whatsappNumber?: string;
  receiveNotifications?: boolean;
}
