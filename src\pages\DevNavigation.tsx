import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

const DevNavigation: React.FC = () => {
  const pageCategories = [
    {
      title: 'Public Pages',
      color: 'bg-blue-100 text-blue-800',
      pages: [
        { path: '/', name: 'Home/Landing' },
        { path: '/signin', name: 'Sign In' },
        { path: '/signup', name: 'Sign Up' },
        { path: '/forgot-password', name: 'Forgot Password' },
        { path: '/reset-password', name: 'Reset Password' },
        { path: '/confirm-signup', name: 'Confirm Sign Up' },
        { path: '/pricing', name: 'Pricing' },
        { path: '/terms', name: 'Terms of Service' },
        { path: '/privacy', name: 'Privacy Policy' },
        { path: '/cookies', name: '<PERSON>ie Policy' },
      ]
    },
    {
      title: 'Store Owner Pages',
      color: 'bg-green-100 text-green-800',
      pages: [
        { path: '/dashboard', name: 'Dashboard' },
        { path: '/products', name: 'Products' },
        { path: '/products/categories', name: 'Categories' },
        { path: '/products/discounts', name: 'Discounts' },
        { path: '/products/featured', name: 'Featured Products' },
        { path: '/orders', name: 'Orders' },
        { path: '/customers', name: 'Customers' },
        { path: '/marketing', name: 'Marketing' },
        { path: '/reports', name: 'Reports' },
        { path: '/payments', name: 'Payments' },
        { path: '/profile', name: 'Profile' },
        { path: '/create-store', name: 'Create Store' },
      ]
    },
    {
      title: 'Settings Pages',
      color: 'bg-yellow-100 text-yellow-800',
      pages: [
        { path: '/settings', name: 'Settings Main' },
        { path: '/settings/general', name: 'General Settings' },
        { path: '/settings/billing', name: 'Billing Settings' },
        { path: '/settings/payments', name: 'Payment Settings' },
        { path: '/settings/delivery', name: 'Delivery Settings' },
        { path: '/settings/domains', name: 'Domains Settings' },
        { path: '/settings/whatsapp', name: 'WhatsApp Settings' },
        { path: '/settings/workflow', name: 'Workflow Settings' },
        { path: '/settings/staff', name: 'Staff Settings' },
        { path: '/settings/checkout', name: 'Checkout Settings' },
        { path: '/settings/membership', name: 'Membership Settings' },
        { path: '/settings/seo', name: 'SEO Settings' },
        { path: '/settings/plans', name: 'Plans' },
      ]
    },
    {
      title: 'Admin Pages',
      color: 'bg-red-100 text-red-800',
      pages: [
        { path: '/admin', name: 'Admin Dashboard' },
        { path: '/admin/users', name: 'Admin Users' },
        { path: '/admin/stores', name: 'Admin Stores' },
        { path: '/admin/orders', name: 'Admin Orders' },
        { path: '/admin/settings', name: 'Admin Settings' },
      ]
    },
    {
      title: 'Storefront Pages',
      color: 'bg-purple-100 text-purple-800',
      pages: [
        { path: '/shop', name: 'Shop Home' },
        { path: '/shop/products', name: 'Product Listing' },
        { path: '/shop/featured', name: 'Featured Products' },
        { path: '/shop/categories', name: 'Categories' },
        { path: '/shop/cart', name: 'Shopping Cart' },
        { path: '/shop/checkout', name: 'Checkout' },
      ]
    },
    {
      title: 'Customer Account Pages',
      color: 'bg-orange-100 text-orange-800',
      pages: [
        { path: '/shop/account', name: 'Account Dashboard' },
        { path: '/shop/account/wishlist', name: 'Wishlist' },
        { path: '/shop/account/profile', name: 'Account Profile' },
        { path: '/shop/account/settings', name: 'Account Settings' },
        { path: '/shop/account/orders', name: 'Order History' },
        { path: '/shop/account/addresses', name: 'Addresses' },
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Development Navigation</h1>
          <p className="text-gray-600">Quick access to all application pages</p>
          <Badge variant="outline" className="mt-2 bg-amber-50">
            Authentication Bypass: ENABLED
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pageCategories.map((category) => (
            <Card key={category.title} className="h-fit">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  {category.title}
                  <Badge className={category.color}>
                    {category.pages.length} pages
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {category.pages.map((page) => (
                    <div key={page.path} className="flex items-center justify-between">
                      <span className="text-sm">{page.name}</span>
                      <div className="flex gap-1">
                        <Link to={page.path}>
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                        </Link>
                        <Link to={page.path} target="_blank">
                          <Button size="sm" variant="ghost">
                            New Tab
                          </Button>
                        </Link>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DevNavigation; 