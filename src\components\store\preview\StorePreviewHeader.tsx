
import React from 'react';
import { ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import ViewModeSelector from './ViewModeSelector';

interface StorePreviewHeaderProps {
  onClose: () => void;
  viewMode: 'desktop' | 'tablet' | 'mobile';
  setViewMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
}

const StorePreviewHeader: React.FC<StorePreviewHeaderProps> = ({
  onClose,
  viewMode,
  setViewMode
}) => {
  return (
    <div className="sticky top-0 z-10 p-4 flex justify-between items-center border-b shadow-sm">
      <Button variant="outline" size="sm" onClick={onClose} className="flex items-center gap-2">
        <ArrowLeft className="h-4 w-4" />
        Back to Editor
      </Button>
      <ViewModeSelector viewMode={viewMode} setViewMode={setViewMode} />
    </div>
  );
};

export default StorePreviewHeader;
