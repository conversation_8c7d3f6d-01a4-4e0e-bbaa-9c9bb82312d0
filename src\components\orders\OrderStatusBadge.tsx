
import React from "react";
import { Badge } from "@/components/ui/badge";
import { OrderStatus } from "@/types/order";
import { CheckCircle, Clock, Package, XCircle, AlertTriangle, RefreshCw, Truck } from "lucide-react";

interface OrderStatusBadgeProps {
  status: OrderStatus;
}

const OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({ status }) => {
  const getStatusConfig = () => {
    switch (status) {
      case "pending":
        return {
          label: "Pending",
          variant: "outline" as const,
          icon: <Clock className="w-3 h-3 mr-1" />,
          className: "text-amber-600 border-amber-300 bg-amber-50"
        };
      case "processing":
        return {
          label: "Processing",
          variant: "outline" as const,
          icon: <RefreshCw className="w-3 h-3 mr-1" />,
          className: "text-blue-600 border-blue-300 bg-blue-50"
        };
      case "paid":
        return {
          label: "Paid",
          variant: "outline" as const,
          icon: <CheckCircle className="w-3 h-3 mr-1" />,
          className: "text-green-600 border-green-300 bg-green-50"
        };
      case "shipped":
        return {
          label: "Shipped",
          variant: "outline" as const,
          icon: <Package className="w-3 h-3 mr-1" />,
          className: "text-indigo-600 border-indigo-300 bg-indigo-50"
        };
      case "delivered":
        return {
          label: "Delivered",
          variant: "outline" as const,
          icon: <Truck className="w-3 h-3 mr-1" />,
          className: "text-green-600 border-green-300 bg-green-50"
        };
      case "cancelled":
        return {
          label: "Cancelled",
          variant: "outline" as const,
          icon: <XCircle className="w-3 h-3 mr-1" />,
          className: "text-red-600 border-red-300 bg-red-50"
        };
      case "refunded":
        return {
          label: "Refunded",
          variant: "outline" as const,
          icon: <RefreshCw className="w-3 h-3 mr-1" />,
          className: "text-purple-600 border-purple-300 bg-purple-50"
        };
      case "payment_failed":
        return {
          label: "Payment Failed",
          variant: "outline" as const,
          icon: <AlertTriangle className="w-3 h-3 mr-1" />,
          className: "text-red-600 border-red-300 bg-red-50"
        };
      default:
        return {
          label: status,
          variant: "outline" as const,
          icon: null,
          className: ""
        };
    }
  };

  const { label, variant, icon, className } = getStatusConfig();

  return (
    <Badge variant={variant} className={`flex items-center font-normal ${className}`}>
      {icon}
      {label}
    </Badge>
  );
};

export default OrderStatusBadge;
