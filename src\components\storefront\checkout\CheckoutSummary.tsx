
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/utils/formatters";
import { CreditCard, Phone, Wallet, CheckCircle, Clock, AlertTriangle, Globe } from "lucide-react";
import { useAuth } from '@/contexts/auth/AuthContext';

interface CheckoutSummaryProps {
  subtotal: number;
  shipping: number;
  total: number;
  paymentMethod: string;
  paymentStatus: "idle" | "processing" | "success" | "error";
  onProcessPayment?: () => void;
}

const CheckoutSummary: React.FC<CheckoutSummaryProps> = ({
  subtotal,
  shipping,
  total,
  paymentMethod,
  paymentStatus,
  onProcessPayment
}) => {
  const { user } = useAuth();
  
  const getPaymentIcon = () => {
    switch (paymentMethod) {
      case "mpesa":
        return <Phone className="h-4 w-4" />;
      case "pesapal":
        return <Globe className="h-4 w-4" />;
      case "credit-card":
        return <CreditCard className="h-4 w-4" />;
      case "paypal":
        return <Wallet className="h-4 w-4" />;
      default:
        return null;
    }
  };
  
  const getPaymentMethodName = () => {
    switch (paymentMethod) {
      case "mpesa":
        return "M-Pesa";
      case "pesapal":
        return "Pesapal";
      case "credit-card":
        return "Credit Card";
      case "paypal":
        return "PayPal";
      default:
        return "";
    }
  };

  const getStatusIcon = () => {
    switch (paymentStatus) {
      case "success":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "processing":
        return <Clock className="h-4 w-4 text-amber-500" />;
      case "error":
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (paymentStatus) {
      case "success":
        return <span className="text-green-600">Payment successful</span>;
      case "processing":
        return <span className="text-amber-600">Processing payment</span>;
      case "error":
        return <span className="text-red-600">Payment failed</span>;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardContent className="p-6">
        <h2 className="text-xl font-bold mb-4">Order Summary</h2>
        
        <div className="space-y-3 mb-6">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Subtotal</span>
            <span>{formatCurrency(subtotal, "USD")}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Shipping</span>
            <span>{shipping === 0 ? "Free" : formatCurrency(shipping, "USD")}</span>
          </div>
          
          <div className="border-t pt-3 flex justify-between font-semibold">
            <span>Total</span>
            <span>{formatCurrency(total, "USD")}</span>
          </div>
        </div>
        
        <div className="bg-muted p-3 rounded-md mb-6 flex items-center">
          <div className="mr-2">
            {getPaymentIcon()}
          </div>
          <div className="text-sm flex-1">
            <p className="font-medium">Payment method</p>
            <p className="text-muted-foreground">{getPaymentMethodName()}</p>
          </div>
          {paymentStatus !== "idle" && (
            <div className="flex items-center">
              {getStatusIcon()}
              <span className="ml-1 text-xs">{getStatusText()}</span>
            </div>
          )}
        </div>
        
        <div className="text-xs text-muted-foreground mb-6">
          <p>
            By completing your purchase, you agree to our Terms of Service and Privacy Policy.
          </p>
        </div>
        
        {onProcessPayment && (
          <Button 
            className="w-full" 
            size="lg" 
            onClick={onProcessPayment}
            disabled={paymentStatus === "processing"}
          >
            {paymentStatus === "processing" ? "Processing..." : "Complete Payment"}
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export default CheckoutSummary;
