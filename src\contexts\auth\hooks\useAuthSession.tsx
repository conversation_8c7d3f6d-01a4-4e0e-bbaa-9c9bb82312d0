
import { useState, useEffect } from 'react';
import { User } from '../types';
import { supabase } from '@/lib/supabase';

export const useAuthSession = () => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const startMs = Date.now();
    let eventMs: number;

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        eventMs = Date.now();
        console.log(`[AuthSession] Auth state changed: ${event}`, { session, time: new Date().toISOString() });
        if (event === 'SIGNED_IN' && session) {
          try {
            setIsLoading(true);
            const profileStart = Date.now();
            console.log('[AuthSession] Fetching profile for SIGNED_IN...');
            const { data: profile, error } = await supabase
              .from('profiles')
              .select('*')
              .eq('user_id', session.user.id)
              .single();
            const profileEnd = Date.now();
            console.log(`[AuthSession] Profile fetch complete. Duration: ${profileEnd - profileStart}ms`);
            if (error) {
              console.error('[AuthSession] Profile fetch error after sign in:', error);
            }

            if (profile) {
              setUser({
                id: session.user.id,
                email: session.user.email || '',
                name: profile.name || '',
                role: profile.role,
                avatar_url: profile.avatar_url
              });
            } else {
              setUser(null);
            }
          } catch (error) {
            console.error('[AuthSession] Error setting user after sign in:', error);
            setUser(null);
          } finally {
            setIsLoading(false);
            console.log(`[AuthSession] SIGNED_IN flow finished in ${Date.now() - eventMs}ms`);
          }
        } else if (event === 'SIGNED_OUT') {
          setUser(null);
          setIsLoading(false);
          console.log(`[AuthSession] SIGNED_OUT event handled in ${Date.now() - eventMs}ms`);
        } else {
          // Log any other event
          setIsLoading(false);
          console.log(`[AuthSession] Other auth event: ${event} handled in ${Date.now() - eventMs}ms`);
        }
      }
    );

    const checkSession = async () => {
      const sessionFetchStart = Date.now();
      console.log('[AuthSession] Checking existing session...');
      const { data: { session } } = await supabase.auth.getSession();
      const sessionFetchEnd = Date.now();
      console.log(`[AuthSession] getSession completed in ${sessionFetchEnd - sessionFetchStart}ms`);

      if (session?.user) {
        try {
          const profileStart = Date.now();
          console.log('[AuthSession] Fetching profile during checkSession...');
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('user_id', session.user.id)
            .single();
          const profileEnd = Date.now();
          console.log(`[AuthSession] Profile fetch in checkSession took ${profileEnd - profileStart}ms`);
          if (error) {
            console.error("[AuthSession] Error fetching user profile (checkSession):", error);
          }
          if (profile) {
            setUser({
              id: session.user.id,
              email: session.user.email || '',
              name: profile.name || '',
              role: profile.role,
              avatar_url: profile.avatar_url
            });
          } else {
            setUser(null);
          }
        } catch (error) {
          console.error("[AuthSession] Error fetching user profile (checkSession):", error);
        } finally {
          setIsLoading(false);
          console.log(`[AuthSession] checkSession (with user) finished in ${Date.now() - sessionFetchStart}ms`);
        }
      } else {
        setIsLoading(false);
        console.log(`[AuthSession] checkSession: No user found. Finished in ${Date.now() - sessionFetchStart}ms`);
      }
    };

    checkSession();

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  return { user, isLoading };
};

