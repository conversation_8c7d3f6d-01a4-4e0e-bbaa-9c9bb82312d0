import React from "react";
import { Link, useLocation } from "react-router-dom";
import { ChevronDown, User, LogOut } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts";
import { toast } from "sonner";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import StoreSelector from "./StoreSelector";
import SidebarNavigation from "./SidebarNavigation";

const MobileNavigation = () => {
  const location = useLocation();
  const { logout } = useAuth();
  const [isLoggingOut, setIsLoggingOut] = React.useState(false);

  const handleLogout = async () => {
    if (isLoggingOut) return;
    
    setIsLoggingOut(true);
    try {
      const toastId = toast.loading("Logging out...");
      await logout();
      toast.dismiss(toastId);
      toast.success("Logged out successfully");
    } catch (error) {
      console.error("Error logging out:", error);
      toast.error("Error logging out. Please try again.");
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Get the navItems from the SidebarNavigation component
  const navItems = SidebarNavigation().props.children.map(child => child.props.item);

  return (
    <header className="bg-background/95 backdrop-blur-md border-b border-border md:hidden safe-top sticky top-0 z-40">
      <div className="mobile-container">
        <div className="flex items-center justify-between py-3 min-h-[60px]">
          <Link 
            to="/dashboard" 
            className="text-xl font-bold text-primary focus-enhanced touch-feedback"
          >
            M-Duka
          </Link>
          
          <div className="flex items-center space-x-2">
            <StoreSelector variant="mobile" />
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="touch-target focus-enhanced"
                >
                  <ChevronDown className="h-5 w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent 
                align="end" 
                className="w-56 bg-background/95 backdrop-blur-md border border-border"
              >
                <DropdownMenuLabel className="text-sm font-semibold">Navigation</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                {navItems.map((item) => {
                  const isImplemented = ['/dashboard', '/products', '/orders', '/customers', '/settings', '/payments'].includes(item.path);
                  // Handle special case for payments path
                  const linkPath = item.path === '/payments' ? '/settings/payments' : item.path;
                  
                  return isImplemented ? (
                    <DropdownMenuItem key={item.path} asChild>
                      <Link 
                        to={linkPath} 
                        className="flex items-center cursor-pointer touch-target focus-enhanced"
                      >
                        <div className="w-5 h-5 mr-3 flex items-center justify-center">
                          {item.icon}
                        </div>
                        <span className="text-sm">{item.name}</span>
                      </Link>
                    </DropdownMenuItem>
                  ) : (
                    <DropdownMenuItem 
                      key={item.path} 
                      disabled 
                      className="flex items-center cursor-not-allowed opacity-50"
                    >
                      <div className="w-5 h-5 mr-3 flex items-center justify-center">
                        {item.icon}
                      </div>
                      <span className="text-sm">{item.name}</span>
                      <Badge variant="outline" className="ml-auto text-xs">Soon</Badge>
                    </DropdownMenuItem>
                  );
                })}
                
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link 
                    to="/profile" 
                    className="flex items-center cursor-pointer touch-target focus-enhanced"
                  >
                    <User className="w-5 h-5 mr-3" />
                    <span className="text-sm">Profile</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem 
                  className="text-destructive focus:text-destructive touch-target"
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                >
                  <LogOut className="w-5 h-5 mr-3" />
                  <span className="text-sm">
                    {isLoggingOut ? "Signing out..." : "Sign Out"}
                  </span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

export default MobileNavigation;
