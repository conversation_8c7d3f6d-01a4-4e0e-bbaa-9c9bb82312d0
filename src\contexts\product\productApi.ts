
import { supabase } from '@/integrations/supabase/client';
import { Product } from '@/types/unified-product';
import { ProductFormValues } from '@/components/product/form/productSchema';
import { toast } from 'sonner';

// Helper function to convert DB data to Product format
export const formatProductFromDb = (product: any): Product => ({
  id: product.id,
  store_id: product.store_id,
  name: product.name,
  description: product.description,
  price: parseFloat(product.price),
  currency: product.currency || 'USD',
  sku: product.sku || `SKU-${product.id.slice(0, 8)}`,
  stock_quantity: product.stock_quantity,
  images: Array.isArray(product.images) ? product.images : [],
  category: product.category,
  tags: Array.isArray(product.tags) ? product.tags : [],
  is_active: product.is_active,
  created_at: new Date(product.created_at).toISOString(),
  updated_at: new Date(product.updated_at).toISOString(),
  available: product.available ?? true,
  sale_price: product.sale_price ?? null,
  specifications: product.specifications ?? null,
  views_count: product.views_count ?? 0,
});

// Fetch all products for a store
export const fetchStoreProducts = async (storeId: string): Promise<Product[]> => {
  try {
    // Check if storeId is a valid UUID
    if (!storeId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.log('⚠️ Invalid store ID format for Supabase query:', storeId);
      return [];
    }
    
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(formatProductFromDb);
  } catch (error) {
    console.error('Failed to load products', error);
    toast.error('Failed to load products');
    return [];
  }
};

// Fetch a single product by ID
export const fetchProductById = async (productId: string): Promise<Product | null> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select('*')
      .eq('id', productId)
      .maybeSingle();

    if (error) throw error;
    if (!data) return null;

    return formatProductFromDb(data);
  } catch (error) {
    console.error('Failed to load product', error);
    toast.error('Failed to load product');
    return null;
  }
};

// Create a new product
export const createProductInDb = async (
  productData: ProductFormValues, 
  storeId: string
): Promise<Product> => {
  try {
    const productToInsert = {
      store_id: storeId,
      name: productData.name,
      description: productData.description,
      price: productData.price,
      currency: productData.currency || 'KES',
      sku: productData.sku,
      stock_quantity: productData.stock_quantity || 0,
      images: productData.images || [],
      category: productData.category,
      tags: productData.tags || [],
      is_active: productData.is_active !== undefined ? productData.is_active : true,
      has_variants: productData.has_variants,
      variants: productData.variants
    };

    const { data, error } = await supabase
      .from('products')
      .insert(productToInsert)
      .select('*')
      .single();

    if (error) throw error;

    toast.success('Product created successfully');
    return formatProductFromDb(data);
  } catch (error: any) {
    console.error('Error creating product:', error);
    toast.error(error.message || 'Failed to create product');
    throw error;
  }
};

// Update an existing product
export const updateProductInDb = async (
  productId: string, 
  productData: Partial<ProductFormValues>
): Promise<Product> => {
  try {
    const { data, error } = await supabase
      .from('products')
      .update(productData)
      .eq('id', productId)
      .select('*')
      .single();

    if (error) throw error;

    toast.success('Product updated successfully');
    return formatProductFromDb(data);
  } catch (error: any) {
    console.error('Error updating product:', error);
    toast.error(error.message || 'Failed to update product');
    throw error;
  }
};

// Delete a product
export const deleteProductFromDb = async (productId: string): Promise<void> => {
  try {
    const { error } = await supabase
      .from('products')
      .delete()
      .eq('id', productId);

    if (error) throw error;
    
    toast.success('Product deleted successfully');
  } catch (error: any) {
    console.error('Error deleting product:', error);
    toast.error(error.message || 'Failed to delete product');
    throw error;
  }
};
