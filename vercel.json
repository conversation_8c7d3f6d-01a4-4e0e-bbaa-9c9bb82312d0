{"rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "buildCommand": "npm run build", "outputDirectory": "dist", "framework": "vite", "installCommand": "npm install", "headers": [{"source": "/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "X-Requested-With, Content-Type, Accept, Authorization"}, {"key": "Cache-Control", "value": "public, max-age=0, must-revalidate"}]}]}