import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/auth/AuthContext';
import { CartItem } from '../types';
import { Product, ProductVariant } from '@/types/unified-product';
import { addOrUpdateCartItem, removeCartItem, clearUserCart } from '../cartApi';

export const useCartItems = (
  items: CartItem[], 
  setItems: React.Dispatch<React.SetStateAction<CartItem[]>>, 
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();
  
  const addToCart = async (product: Product, quantity: number = 1, variant?: ProductVariant) => {
    setIsLoading(true);
    
    try {
      // Check if item already exists in cart
      const existingItemIndex = items.findIndex(item => {
        if (variant) {
          return item.product.id === product.id && item.variant?.id === variant.id;
        }
        return item.product.id === product.id && !item.variant;
      });
      
      if (isAuthenticated && user) {
        // If authenticated, save to Supabase
        if (existingItemIndex >= 0) {
          // Update existing item in Supabase
          const existingItem = items[existingItemIndex];
          const newQuantity = existingItem.quantity + quantity;
          
          await addOrUpdateCartItem({
            id: existingItem.id,
            quantity: newQuantity
          });
          
          // Update local state
          const updatedItems = [...items];
          updatedItems[existingItemIndex].quantity = newQuantity;
          setItems(updatedItems);
        } else {
          // Insert new item in Supabase
          const newItemId = await addOrUpdateCartItem({
            user_id: user.id,
            product,
            quantity,
            variant
          });
          
          // Add to local state with a product variant that matches the expected type
          const safeVariant = variant ? {
            ...variant,
            options: variant.options || {}
          } : undefined;
          
          setItems([...items, {
            id: newItemId,
            product,
            variant: safeVariant,
            quantity,
            user_id: user.id,
          }]);
        }
      } else {
        // If not authenticated, use local state only
        if (existingItemIndex >= 0) {
          // Update existing item
          const updatedItems = [...items];
          updatedItems[existingItemIndex].quantity += quantity;
          setItems(updatedItems);
        } else {
          // Add new item with a product variant that matches the expected type
          const safeVariant = variant ? {
            ...variant,
            options: variant.options || {}
          } : undefined;
          
          setItems([...items, {
            id: variant ? `${product.id}-${variant.id}` : `${product.id}`,
            product,
            variant: safeVariant,
            quantity
          }]);
        }
      }
      
      toast({
        title: "Item added",
        description: `${product.name} added to your cart.`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in addToCart:', error);
      toast({
        title: "Error",
        description: "Could not add item to cart.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const removeFromCart = async (itemId: string) => {
    setIsLoading(true);
    
    try {
      if (isAuthenticated && user) {
        // If authenticated, remove from Supabase
        await removeCartItem(itemId);
      }
      
      // Remove from local state
      const itemToRemove = items.find(item => item.id === itemId);
      setItems(items.filter(item => item.id !== itemId));
      
      if (itemToRemove) {
        toast({
          title: "Item removed",
          description: `${itemToRemove.product.name} removed from your cart.`,
          duration: 3000,
        });
      }
    } catch (error) {
      console.error('Error in removeFromCart:', error);
      toast({
        title: "Error",
        description: "Could not remove item from cart.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const updateQuantity = async (itemId: string, quantity: number) => {
    if (quantity < 1) return;
    setIsLoading(true);
    
    try {
      if (isAuthenticated && user) {
        // If authenticated, update in Supabase
        await addOrUpdateCartItem({ id: itemId, quantity });
      }
      
      // Update in local state
      setItems(prevItems => 
        prevItems.map(item => 
          item.id === itemId ? { ...item, quantity } : item
        )
      );
    } catch (error) {
      console.error('Error in updateQuantity:', error);
      toast({
        title: "Error",
        description: "Could not update quantity.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  const clearCart = async () => {
    setIsLoading(true);
    
    try {
      if (isAuthenticated && user) {
        // If authenticated, clear cart in database
        await clearUserCart(user.id);
      }
      
      // Clear local state
      setItems([]);
      
      toast({
        title: "Cart cleared",
        description: "All items have been removed from your cart.",
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in clearCart:', error);
      toast({
        title: "Error",
        description: "Could not clear cart.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart
  };
};
