
import React from 'react';
import { 
  Store, 
  Users, 
  Package, 
  ShoppingCart, 
  Settings, 
  CreditCard,
  Truck,
  UserRound,
  MessageCircle,
  Workflow,
  BarChart3,
  Megaphone
} from 'lucide-react';
import NavigationSection from './NavigationSection';
import useNavigationActive from './useNavigationActive';
import ProductsGroup from '../ProductsGroup';
import MarketingGroup from '../MarketingGroup';
import ReportsGroup from '../ReportsGroup';
import { NavigationItemType } from './NavigationSection';

const NavigationItems: React.FC = () => {
  const { isActive } = useNavigationActive();
  const [productsOpen, setProductsOpen] = React.useState(false);
  const [marketingOpen, setMarketingOpen] = React.useState(false);
  const [reportsOpen, setReportsOpen] = React.useState(false);
  
  const primaryNavItems: NavigationItemType[] = [
    { name: "Dashboard", path: "/dashboard", icon: <Store className="h-5 w-5" />, end: true },
  ];
  
  const managementNavItems: NavigationItemType[] = [
    { name: "Orders", path: "/orders", icon: <ShoppingCart className="h-5 w-5" /> },
    { name: "Customers", path: "/customers", icon: <Users className="h-5 w-5" /> },
    { name: "Payments", path: "/payments", icon: <CreditCard className="h-5 w-5" /> },
  ];
  
  const marketingNavItems: NavigationItemType[] = [
    { name: "Marketing", path: "/marketing", icon: <Megaphone className="h-5 w-5" /> },
  ];
  
  const reportsNavItems: NavigationItemType[] = [
    { name: "Reports & Analytics", path: "/reports", icon: <BarChart3 className="h-5 w-5" /> },
  ];
  
  const configNavItems: NavigationItemType[] = [
    { name: "Profile", path: "/profile", icon: <UserRound className="h-5 w-5" /> },
    { name: "Settings", path: "/settings", icon: <Settings className="h-5 w-5" /> },
    { name: "Delivery", path: "/settings/delivery", icon: <Truck className="h-5 w-5" /> },
    { name: "WhatsApp", path: "/settings/whatsapp", icon: <MessageCircle className="h-5 w-5" /> },
    { name: "Workflow", path: "/settings/workflow", icon: <Workflow className="h-5 w-5" /> },
    { name: "Storefront", path: "/shop", icon: <Store className="h-5 w-5" /> },
  ];

  return (
    <nav className="space-y-1">
      <NavigationSection items={primaryNavItems} isActiveFunc={isActive} />

      <ProductsGroup
        isOpen={productsOpen}
        onOpenChange={setProductsOpen}
        isActive={isActive('/products') || isActive('/categories') || isActive('/discounts')}
        isProductsActive={isActive('/products', true)}
        isCategoriesActive={isActive('/categories')}
        isDiscountsActive={isActive('/discounts')}
      />

      <NavigationSection items={managementNavItems} isActiveFunc={isActive} />
      
      <NavigationSection items={marketingNavItems} isActiveFunc={isActive} />
      
      <NavigationSection items={reportsNavItems} isActiveFunc={isActive} />

      <NavigationSection items={configNavItems} isActiveFunc={isActive} />
    </nav>
  );
};

export default NavigationItems;
