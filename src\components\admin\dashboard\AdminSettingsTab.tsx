
import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

const AdminSettingsTab: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Platform Settings</CardTitle>
        <CardDescription>
          Configure global platform settings
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-8 md:grid-cols-2">
          <SettingsCard 
            title="General Settings" 
            items={[
              { label: "Platform Name", description: "Configure the name of your platform" },
              { label: "Date & Time Format", description: "Set regional date and time preferences" },
              { label: "Default Language", description: "Set the default language for new users" }
            ]} 
          />
          
          <SettingsCard 
            title="Security Settings" 
            items={[
              { label: "Password Requirements", description: "Set minimum password strength requirements" },
              { label: "Two-Factor Authentication", description: "Configure 2FA enforcement policies" },
              { label: "Session Timeout", description: "Set how long users can stay logged in" }
            ]} 
          />
          
          <SettingsCard 
            title="Email Templates" 
            items={[
              { label: "Welcome Email", description: "Edit the email sent to new users" },
              { label: "Password Reset", description: "Edit password reset email template" },
              { label: "Order Confirmation", description: "Edit order confirmation emails" }
            ]} 
          />
          
          <SettingsCard 
            title="Payment Gateways" 
            items={[
              { label: "Payment Processors", description: "Configure available payment methods" },
              { label: "Commission Rates", description: "Set platform fee percentages" },
              { label: "Payout Schedule", description: "Configure when sellers receive payments" }
            ]} 
          />
        </div>
      </CardContent>
    </Card>
  );
};

interface SettingsCardProps {
  title: string;
  items: { label: string; description: string }[];
}

const SettingsCard: React.FC<SettingsCardProps> = ({ title, items }) => {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-base">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {items.map((item, index) => (
            <div key={index} className="space-y-2">
              <h4 className="font-medium">{item.label}</h4>
              <p className="text-sm text-muted-foreground">{item.description}</p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminSettingsTab;
