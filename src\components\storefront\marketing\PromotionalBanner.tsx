
import React, { useState, useEffect } from "react";
import { MessageCircle, Tag, Bell, X, Calendar, Info } from "lucide-react";
import { cn } from "@/lib/utils";

// Types of promotional banners
type BannerType = "whatsapp" | "discount" | "announcement" | "event" | "info";

interface BannerContent {
  type: BannerType;
  text: string;
  link?: string;
  backgroundColor: string;
  textColor: string;
  icon: React.ReactNode;
}

const bannerConfigs: Record<BannerType, Omit<BannerContent, "text" | "link">> = {
  whatsapp: {
    type: "whatsapp",
    backgroundColor: "bg-green-500",
    textColor: "text-white",
    icon: <MessageCircle className="h-3.5 w-3.5" />
  },
  discount: {
    type: "discount",
    backgroundColor: "bg-orange-500",
    textColor: "text-white",
    icon: <Tag className="h-3.5 w-3.5" />
  },
  announcement: {
    type: "announcement",
    backgroundColor: "bg-blue-500",
    textColor: "text-white",
    icon: <Bell className="h-3.5 w-3.5" />
  },
  event: {
    type: "event",
    backgroundColor: "bg-purple-500",
    textColor: "text-white",
    icon: <Calendar className="h-3.5 w-3.5" />
  },
  info: {
    type: "info",
    backgroundColor: "bg-gray-500",
    textColor: "text-white",
    icon: <Info className="h-3.5 w-3.5" />
  }
};

// Sample banner content - in a real app, this would come from the store settings
const defaultBanners: BannerContent[] = [
  {
    ...bannerConfigs.whatsapp,
    text: "Join our WhatsApp group for exclusive deals!",
    link: "https://whatsapp.com/channel/example"
  }
];

interface PromotionalBannerProps {
  banners?: BannerContent[];
  autoRotate?: boolean;
}

const PromotionalBanner: React.FC<PromotionalBannerProps> = ({ 
  banners = defaultBanners, 
  autoRotate = true 
}) => {
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  
  // Rotate banners every 7 seconds if there are multiple and autoRotate is true
  useEffect(() => {
    if (banners.length <= 1 || !autoRotate) return;
    
    const interval = setInterval(() => {
      setCurrentBannerIndex(prev => (prev + 1) % banners.length);
    }, 7000);
    
    return () => clearInterval(interval);
  }, [banners.length, autoRotate]);
  
  if (!isVisible || banners.length === 0) return null;
  
  const currentBanner = banners[currentBannerIndex];
  
  return (
    <div 
      className={cn(
        "w-full transition-colors duration-300",
        currentBanner.backgroundColor, 
        currentBanner.textColor
      )}
    >
      <div className="px-3 w-full">
        <div className="flex items-center justify-between py-2">
          {currentBanner.link ? (
            <a 
              href={currentBanner.link} 
              className="flex-1 flex items-center justify-center gap-2 hover:underline"
              target={currentBanner.link.startsWith("http") ? "_blank" : undefined}
              rel={currentBanner.link.startsWith("http") ? "noopener noreferrer" : undefined}
            >
              {currentBanner.icon}
              <span className="text-xs font-medium line-clamp-1">{currentBanner.text}</span>
            </a>
          ) : (
            <div className="flex-1 flex items-center justify-center gap-2">
              {currentBanner.icon}
              <span className="text-xs font-medium line-clamp-1">{currentBanner.text}</span>
            </div>
          )}
          
          <button 
            onClick={() => setIsVisible(false)} 
            className="p-1 hover:opacity-70 transition-opacity"
            aria-label="Dismiss"
          >
            <X className="h-3 w-3" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default PromotionalBanner;
