
import React, { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useOrder, useStore } from "@/contexts";
import { Order, OrderStatus, Transaction } from "@/types/order";
import { formatCurrency } from "@/utils/formatters";
import { fetchOrderById, fetchOrderTransactions, updateOrderStatusInDb } from "@/contexts/order/orderApi";
import { 
  ArrowLeft, 
  Package, 
  Calendar, 
  CreditCard, 
  User, 
  MapPin, 
  Mail,
  Phone,
  RefreshCw,
  Loader2
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import OrderStatusBadge from "@/components/orders/OrderStatusBadge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";

const OrderDetails: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();
  const navigate = useNavigate();
  const { currentStore } = useStore();
  const { updateOrderStatus } = useOrder();
  
  const [order, setOrder] = useState<Order | null>(null);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  
  useEffect(() => {
    const loadOrderDetails = async () => {
      if (!orderId) return;
      
      setIsLoading(true);
      try {
        const fetchedOrder = await fetchOrderById(orderId);
        if (fetchedOrder) {
          setOrder(fetchedOrder);
          const fetchedTransactions = await fetchOrderTransactions(orderId);
          setTransactions(fetchedTransactions);
        }
      } catch (error) {
        console.error('Error loading order details:', error);
        toast.error('Failed to load order details');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrderDetails();
  }, [orderId]);
  
  const handleStatusChange = async (newStatus: OrderStatus) => {
    if (!order) return;
    
    setIsUpdating(true);
    try {
      await updateOrderStatus(order.id, newStatus);
      setOrder({ ...order, status: newStatus });
      toast.success(`Order status updated to ${newStatus}`);
    } catch (error) {
      console.error('Error updating order status:', error);
    } finally {
      setIsUpdating(false);
    }
  };
  
  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <Loader2 className="w-10 h-10 text-primary animate-spin" />
      </div>
    );
  }
  
  if (!order) {
    return (
      <div className="p-6">
        <Button variant="ghost" onClick={() => navigate('/orders')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Orders
        </Button>
        <div className="mt-20 text-center">
          <h2 className="text-2xl font-bold">Order Not Found</h2>
          <p className="text-muted-foreground mt-2">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <Button variant="ghost" onClick={() => navigate('/orders')} className="mb-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Orders
          </Button>
          <h1 className="text-2xl font-bold">Order #{order.id.substring(0, 8)}</h1>
          <div className="flex items-center gap-2 mt-1">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">
              {new Date(order.created_at).toLocaleString()}
            </span>
          </div>
        </div>
        
        <div className="flex items-center gap-3">
          <OrderStatusBadge status={order.status} />
          
          <div className="flex items-center">
            <Select
              disabled={isUpdating}
              value={order.status}
              onValueChange={(value) => handleStatusChange(value as OrderStatus)}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Update Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="processing">Processing</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
                <SelectItem value="refunded">Refunded</SelectItem>
              </SelectContent>
            </Select>
            
            {isUpdating && (
              <RefreshCw className="ml-2 h-4 w-4 animate-spin" />
            )}
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Order Summary */}
        <div className="md:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Order Items</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-muted rounded-md flex items-center justify-center">
                        <Package className="h-6 w-6 text-muted-foreground" />
                      </div>
                      <div>
                        <p className="font-medium">{item.name}</p>
                        <p className="text-sm text-muted-foreground">
                          Qty: {item.quantity} × {formatCurrency(item.price, order.currency)}
                        </p>
                      </div>
                    </div>
                    <div className="font-medium">
                      {formatCurrency(item.price * item.quantity, order.currency)}
                    </div>
                  </div>
                ))}
              </div>
              
              <Separator className="my-4" />
              
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>{formatCurrency(order.total_amount, order.currency)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>Free</span>
                </div>
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span>{formatCurrency(order.total_amount, order.currency)}</span>
                </div>
              </div>
            </CardContent>
          </Card>
          
          {/* Shipping & Billing */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            {/* Shipping Address */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <MapPin className="mr-2 h-4 w-4" />
                  Shipping Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.shipping_address ? (
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">
                      {order.shipping_address.firstName} {order.shipping_address.lastName}
                    </p>
                    <p>{order.shipping_address.addressLine1}</p>
                    {order.shipping_address.addressLine2 && <p>{order.shipping_address.addressLine2}</p>}
                    <p>
                      {order.shipping_address.city}, {order.shipping_address.state} {order.shipping_address.postalCode}
                    </p>
                    <p>{order.shipping_address.country}</p>
                    {order.shipping_address.phone && (
                      <p className="flex items-center mt-2">
                        <Phone className="h-3 w-3 mr-1" />
                        {order.shipping_address.phone}
                      </p>
                    )}
                    {order.shipping_address.email && (
                      <p className="flex items-center">
                        <Mail className="h-3 w-3 mr-1" />
                        {order.shipping_address.email}
                      </p>
                    )}
                  </div>
                ) : (
                  <p className="text-muted-foreground text-sm">No shipping address provided</p>
                )}
              </CardContent>
            </Card>
            
            {/* Billing Address */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center">
                  <CreditCard className="mr-2 h-4 w-4" />
                  Billing Address
                </CardTitle>
              </CardHeader>
              <CardContent>
                {order.billing_address ? (
                  <div className="space-y-1 text-sm">
                    <p className="font-medium">
                      {order.billing_address.firstName} {order.billing_address.lastName}
                    </p>
                    <p>{order.billing_address.addressLine1}</p>
                    {order.billing_address.addressLine2 && <p>{order.billing_address.addressLine2}</p>}
                    <p>
                      {order.billing_address.city}, {order.billing_address.state} {order.billing_address.postalCode}
                    </p>
                    <p>{order.billing_address.country}</p>
                  </div>
                ) : (
                  <p className="text-muted-foreground text-sm">
                    {order.shipping_address ? 'Same as shipping address' : 'No billing address provided'}
                  </p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Customer & Payment Info */}
        <div className="space-y-6">
          {/* Customer Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <User className="mr-2 h-4 w-4" />
                Customer
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <p className="font-medium">
                  {order.shipping_address?.firstName 
                    ? `${order.shipping_address.firstName} ${order.shipping_address.lastName || ''}`
                    : 'Guest Customer'}
                </p>
                {order.shipping_address?.email && (
                  <div className="flex items-center text-sm">
                    <Mail className="h-3 w-3 mr-2 text-muted-foreground" />
                    <span>{order.shipping_address.email}</span>
                  </div>
                )}
                {order.shipping_address?.phone && (
                  <div className="flex items-center text-sm">
                    <Phone className="h-3 w-3 mr-2 text-muted-foreground" />
                    <span>{order.shipping_address.phone}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
          
          {/* Payment Info */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center">
                <CreditCard className="mr-2 h-4 w-4" />
                Payment Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Method</span>
                  <span>
                    {order.payment_method === 'pesapal' ? 'Pesapal' :
                    order.payment_method === 'credit-card' ? 'Credit Card' :
                    order.payment_method === 'paypal' ? 'PayPal' : 
                    order.payment_method === 'mpesa' ? 'M-Pesa' :
                    order.payment_method || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status</span>
                  <OrderStatusBadge status={order.status} />
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Total</span>
                  <span className="font-medium">{formatCurrency(order.total_amount, order.currency)}</span>
                </div>
                
                {transactions.length > 0 && (
                  <>
                    <Separator className="my-2" />
                    <div className="text-sm font-medium">Transaction Details</div>
                    {transactions.map((transaction) => (
                      <div key={transaction.id} className="text-sm space-y-1 bg-muted p-2 rounded-md">
                        {transaction.transactionId && (
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Transaction ID</span>
                            <span>{transaction.transactionId}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Date</span>
                          <span>{new Date(transaction.createdAt).toLocaleString()}</span>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default OrderDetails;
