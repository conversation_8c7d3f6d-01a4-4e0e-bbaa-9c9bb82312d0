
import { useCartLoading } from './hooks/useCartLoading';
import { useCartItems } from './hooks/useCartItems';
import { useCartCalculations } from './hooks/useCartCalculations';
import { CartContextType } from './types';

export const useCartProvider = (): CartContextType => {
  // Use the cart loading hook to manage cart loading state
  const { items, setItems, isLoading, setIsLoading } = useCartLoading();
  
  // Use the cart items hook to manage cart items
  const { addToCart, removeFromCart, updateQuantity, clearCart } = useCartItems(
    items,
    setItems,
    setIsLoading
  );
  
  // Use the cart calculations hook to calculate cart totals
  const { subtotal, shipping, total, itemCount } = useCartCalculations(items);
  
  return {
    items,
    itemCount,
    subtotal,
    shipping,
    total,
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart,
    isLoading
  };
};
