
/**
 * Creates an object with the same keys as object and values generated by iteratee
 *
 * @param {Object} object - The object to iterate over
 * @param {Function} iteratee - The function invoked per iteration
 * @returns {Object} Returns the new mapped object
 */
function mapValues(object, iteratee) {
  if (!object) return {};
  
  const result = {};
  
  for (const key in object) {
    if (Object.prototype.hasOwnProperty.call(object, key)) {
      result[key] = iteratee(object[key], key, object);
    }
  }
  
  return result;
}

// Support both ESM and CJS
export default mapValues;
