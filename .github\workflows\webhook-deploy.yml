name: Trigger Deployment Webhook

on:
  push:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Install dependencies
        run: bun install

      - name: Build
        run: bun run build
        env:
          VITE_PUBLIC_SUPABASE_URL: ${{ secrets.VITE_PUBLIC_SUPABASE_URL }}
          VITE_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.VITE_PUBLIC_SUPABASE_ANON_KEY }}
          
      # Trigger deployment webhook
      - name: Trigger lovable.dev webhook
        run: |
          curl -X POST ${{ secrets.LOVABLE_WEBHOOK_URL }} \
            -H "Content-Type: application/json" \
            -d '{"project_id": "d6edfe1f-9b57-457c-b66b-98d94e822107", "trigger": "github-action"}'