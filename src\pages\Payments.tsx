
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Container } from '@/components/ui/container';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { CreditCard } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

const Payments: React.FC = () => {
  return (
    <>
      <Helmet>
        <title>Payments - m-duka</title>
      </Helmet>
      <Container>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <CreditCard className="h-6 w-6" />
            <span>Payments</span>
          </h1>
          <Button asChild variant="outline">
            <Link to="/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Payment Settings</CardTitle>
            <CardDescription>
              Configure your store's payment methods and options.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              You can manage your payment settings including payment providers, currencies, and 
              checkout options in the settings area.
            </p>
            <Button asChild>
              <Link to="/settings/payments">
                Go to Payment Settings
              </Link>
            </Button>
          </CardContent>
        </Card>
      </Container>
    </>
  );
};

export default Payments;
