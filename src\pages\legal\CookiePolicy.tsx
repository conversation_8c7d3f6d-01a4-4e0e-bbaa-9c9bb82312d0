
import React from 'react';
import { Container } from "@/components/ui/container";
import Footer from "@/components/Footer";
import { Helmet } from "react-helmet-async";
import { Separator } from "@/components/ui/separator";
import { Link } from "react-router-dom";

const CookiePolicy = () => {
  return (
    <>
      <Helmet>
        <title>Cookie Policy - M-Duka</title>
        <meta name="description" content="M-Duka cookie policy and usage information." />
      </Helmet>
      <div className="min-h-screen bg-background">
        <main className="py-12 space-y-8">
          <Container>
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold tracking-tight mb-6">Cookie Policy</h1>
              <p className="text-muted-foreground mb-8">Last updated: April 25, 2025</p>

              <div className="prose prose-gray max-w-none space-y-8">
                <section>
                  <h2 className="text-2xl font-semibold mb-4">1. About Cookies</h2>
                  <p className="text-base leading-7">
                    Cookies are small text files stored on your device when you visit our website. 
                    They help us provide you with a better experience and understand how you use our services.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">2. Types of Cookies We Use</h2>
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-xl font-medium mb-2">Essential Cookies</h3>
                      <p className="text-base leading-7">
                        Required for the website to function properly. They enable basic functions like page 
                        navigation and access to secure areas.
                      </p>
                    </div>
                    <div>
                      <h3 className="text-xl font-medium mb-2">Functionality Cookies</h3>
                      <p className="text-base leading-7">
                        Help us remember your preferences and customize your experience.
                      </p>
                    </div>
                    <div>
                      <h3 className="text-xl font-medium mb-2">Analytics Cookies</h3>
                      <p className="text-base leading-7">
                        Collect information about how you use our website, helping us improve our services.
                      </p>
                    </div>
                    <div>
                      <h3 className="text-xl font-medium mb-2">Marketing Cookies</h3>
                      <p className="text-base leading-7">
                        Track your browsing habits to deliver targeted advertising.
                      </p>
                    </div>
                  </div>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">3. Cookie Management</h2>
                  <p className="text-base leading-7 mb-4">
                    You can manage cookie preferences through your browser settings. You can:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Delete all cookies from your browser</li>
                    <li>Set your browser to prevent cookies from being stored</li>
                    <li>Receive notifications when cookies are being set</li>
                    <li>Review and delete individual cookies</li>
                  </ul>
                  <p className="text-base leading-7 mt-4">
                    Note: Blocking cookies may impact website functionality.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">4. Third-Party Cookies</h2>
                  <p className="text-base leading-7">
                    Some cookies are placed by third-party services that appear on our pages. 
                    We do not control these third parties but have ensured they handle your 
                    data in compliance with regulations.
                  </p>
                </section>

                <section className="mt-12 p-6 bg-muted rounded-lg">
                  <h2 className="text-xl font-semibold mb-4">Questions About Cookies?</h2>
                  <p className="text-base leading-7">
                    If you have questions about how we use cookies, please contact <NAME_EMAIL>
                  </p>
                </section>
              </div>
              
              <div className="mt-8 flex justify-center">
                <Link to="/" className="text-primary hover:underline">
                  Return to Home
                </Link>
              </div>
            </div>
          </Container>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default CookiePolicy;
