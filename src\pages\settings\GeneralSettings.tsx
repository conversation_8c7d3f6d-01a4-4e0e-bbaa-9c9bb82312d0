
import React from 'react';
import { useAuth } from '@/contexts';
import { useNavigate } from 'react-router-dom';
import StoreTab from '@/components/settings/general/StoreTab';
import OrganizationTab from '@/components/settings/general/OrganizationTab';
import StoreGeneralTab from '@/components/settings/general/StoreGeneralTab';
import ProfileTab from '@/components/settings/general/ProfileTab';
import SettingsLayout from '@/components/settings/SettingsLayout';

const GeneralSettings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Redirect if no user
  React.useEffect(() => {
    if (!user) {
      navigate('/signin');
    }
  }, [user, navigate]);

  return (
    <SettingsLayout
      title="General Settings"
      description="Configure basic settings for your store"
      defaultTab="store"
      tabs={[
        { value: "store", label: "Store", content: <StoreTab /> },
        { value: "store-general", label: "Store General", content: <StoreGeneralTab /> },
        { value: "organization", label: "Organization", content: <OrganizationTab /> },
        { value: "profile", label: "Profile", content: <ProfileTab /> },
      ]}
    />
  );
};

export default GeneralSettings;
