
import React, { useState, useEffect } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2, Upload, User2 } from "lucide-react";
import { Separator } from "@/components/ui/separator"; 
import { Textarea } from "@/components/ui/textarea";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuth } from "@/contexts";
import { toast } from "sonner";
import { updateUserProfile, getUserProfile, uploadProfileAvatar } from "@/contexts/auth/api/profileApi";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ProfileFormValues, profileFormSchema } from "./schemas";

const ProfileTab: React.FC = () => {
  const { user } = useAuth();
  const [isUpdatingProfile, setIsUpdatingProfile] = useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = useState(false);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [userPhone, setUserPhone] = useState('');
  const [userBio, setUserBio] = useState('');
  const [avatarUrl, setAvatarUrl] = useState('');

  // Profile form
  const profileForm = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
    },
  });

  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        try {
          const profile = await getUserProfile(user.id);
          if (profile) {
            profileForm.setValue('name', profile.name || '');
            setUserPhone(''); // Phone not available in current schema
            setUserBio(''); // Bio not available in current schema
            setAvatarUrl(profile.avatar_url || '');
          }
        } catch (error) {
          console.error('Error fetching profile:', error);
        }
      }
    };
    
    fetchProfile();
  }, [user?.id]);

  const onProfileSubmit = async (data: ProfileFormValues) => {
    if (isUpdatingProfile) return;
    
    setIsUpdatingProfile(true);
    setProfileError(null);
    
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Updating profile...");
      
      // Call API to update profile
      await updateUserProfile(user?.id as string, {
        name: data.name,
        phone: userPhone,
        bio: userBio
      });
      
      if (toastId) toast.dismiss(toastId);
      toast.success("Profile updated successfully!");
    } catch (error: any) {
      console.error("Error updating profile:", error);
      if (toastId) toast.dismiss(toastId);
      setProfileError(error.message || "Failed to update profile");
      toast.error(error.message || "Failed to update profile");
    } finally {
      setIsUpdatingProfile(false);
    }
  };

  const handleAvatarUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0 || !user?.id) return;
    
    setIsUploadingAvatar(true);
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Uploading avatar...");
      const result = await uploadProfileAvatar(user.id, files[0]);
      setAvatarUrl(result.avatarUrl);
      
      if (toastId) toast.dismiss(toastId);
      toast.success("Avatar uploaded successfully!");
    } catch (error: any) {
      console.error("Error uploading avatar:", error);
      if (toastId) toast.dismiss(toastId);
      toast.error(error.message || "Failed to upload avatar");
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User2 className="h-5 w-5 text-primary" />
            Personal Information
          </CardTitle>
          <CardDescription>
            Update your personal information and contact details
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row md:items-start gap-8 mb-6">
            <div className="flex flex-col items-center">
              <Avatar className="h-24 w-24 border-2 border-primary/20">
                <AvatarImage src={avatarUrl} alt={user?.name || "User"} />
                <AvatarFallback className="text-lg bg-primary/10 text-primary">
                  {getInitials(user?.name || "User")}
                </AvatarFallback>
              </Avatar>
              <div className="relative mt-4">
                <input
                  type="file"
                  id="avatar-upload"
                  className="hidden"
                  accept="image/*"
                  onChange={handleAvatarUpload}
                  disabled={isUploadingAvatar}
                />
                <label 
                  htmlFor="avatar-upload" 
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80 cursor-pointer"
                >
                  {isUploadingAvatar ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-3 w-3 mr-1" /> Change Photo
                    </>
                  )}
                </label>
              </div>
            </div>
            
            <div className="flex-1">
              <Form {...profileForm}>
                <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                  {profileError && (
                    <div className="bg-destructive/15 text-destructive p-3 rounded-md mb-6">
                      {profileError}
                    </div>
                  )}
                  
                  <div className="grid gap-4 sm:grid-cols-2">
                    <FormField
                      control={profileForm.control}
                      name="name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Full Name</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={isUpdatingProfile} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    
                    <FormField
                      control={profileForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Email</FormLabel>
                          <FormControl>
                            <Input {...field} disabled={true} />
                          </FormControl>
                          <FormDescription>
                            Email cannot be changed
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel>Phone Number</FormLabel>
                    <Input
                      value={userPhone}
                      onChange={(e) => setUserPhone(e.target.value)}
                      placeholder="Enter your phone number"
                      disabled={isUpdatingProfile}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <FormLabel>Bio</FormLabel>
                    <Textarea
                      value={userBio}
                      onChange={(e) => setUserBio(e.target.value)}
                      placeholder="Tell us a little about yourself"
                      disabled={isUpdatingProfile}
                      rows={3}
                    />
                    <p className="text-muted-foreground text-sm">
                      Brief description for your profile
                    </p>
                  </div>
                  
                  <Separator className="my-6" />
                  
                  <Button 
                    type="submit" 
                    className="w-full md:w-auto"
                    disabled={isUpdatingProfile}
                  >
                    {isUpdatingProfile && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    {isUpdatingProfile ? "Updating..." : "Update Profile"}
                  </Button>
                </form>
              </Form>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileTab;
