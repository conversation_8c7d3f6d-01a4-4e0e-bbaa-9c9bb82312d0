/**
 * Environment-aware logging utility
 * Reduces console pollution in production
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  isDevelopment: boolean;
  enabledLevels: LogLevel[];
  prefix?: string;
}

class Logger {
  private config: LoggerConfig;

  constructor(config: LoggerConfig) {
    this.config = config;
  }

  private shouldLog(level: LogLevel): boolean {
    return this.config.isDevelopment || this.config.enabledLevels.includes(level);
  }

  private formatMessage(level: LogLevel, message: string, ...args: any[]): [string, ...any[]] {
    const timestamp = new Date().toISOString();
    const prefix = this.config.prefix ? `[${this.config.prefix}]` : '';
    return [`${timestamp} ${prefix} [${level.toUpperCase()}] ${message}`, ...args];
  }

  debug(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.debug(...this.formatMessage('debug', message, ...args));
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.info(...this.formatMessage('info', message, ...args));
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.shouldLog('warn')) {
      console.warn(...this.formatMessage('warn', message, ...args));
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.shouldLog('error')) {
      console.error(...this.formatMessage('error', message, ...args));
    }
  }

  // Special method for authentication logs
  auth(message: string, ...args: any[]): void {
    if (this.shouldLog('info')) {
      console.log(`🔐 ${message}`, ...args);
    }
  }

  // Special method for API logs
  api(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.log(`🌐 ${message}`, ...args);
    }
  }

  // Special method for database logs
  db(message: string, ...args: any[]): void {
    if (this.shouldLog('debug')) {
      console.log(`🗄️ ${message}`, ...args);
    }
  }
}

// Create default logger instance
const isDevelopment = import.meta.env.DEV || import.meta.env.MODE === 'development';

export const logger = new Logger({
  isDevelopment,
  enabledLevels: ['warn', 'error'], // Always log warnings and errors
  prefix: 'M-Duka'
});

// Create specialized loggers
export const authLogger = new Logger({
  isDevelopment,
  enabledLevels: ['warn', 'error'],
  prefix: 'Auth'
});

export const apiLogger = new Logger({
  isDevelopment,
  enabledLevels: ['warn', 'error'],
  prefix: 'API'
});

export const dbLogger = new Logger({
  isDevelopment,
  enabledLevels: ['warn', 'error'],
  prefix: 'DB'
});

// Legacy console replacement for gradual migration
export const devConsole = {
  log: (message: string, ...args: any[]) => logger.debug(message, ...args),
  info: (message: string, ...args: any[]) => logger.info(message, ...args),
  warn: (message: string, ...args: any[]) => logger.warn(message, ...args),
  error: (message: string, ...args: any[]) => logger.error(message, ...args),
};

export default logger;
