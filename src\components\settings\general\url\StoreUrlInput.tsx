
import React from 'react';
import { Input } from '@/components/ui/input';
import UrlAvailabilityIndicator from './UrlAvailabilityIndicator';
import UrlSuggestion from './UrlSuggestion';

type UrlCheckStatus = 'checking' | 'available' | 'unavailable' | 'error' | null;

interface StoreUrlInputProps {
  storeUrl: string;
  onUrlChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  urlCheckStatus: UrlCheckStatus;
  urlSuggestion: string | null;
  onUseSuggestion: () => void;
}

const StoreUrlInput = ({
  storeUrl,
  onUrlChange,
  urlCheckStatus,
  urlSuggestion,
  onUseSuggestion
}: StoreUrlInputProps) => {
  return (
    <div className="space-y-2">
      <div className="flex items-center">
        <span className="bg-muted px-3 py-2 rounded-l-md text-muted-foreground border border-r-0 border-input">
          m-duka.app /
        </span>
        <div className="flex-1 relative">
          <Input 
            id="store-link"
            value={storeUrl}
            onChange={onUrlChange}
            placeholder="Enter your store URL"
            className="rounded-l-none"
          />
          {urlCheckStatus && (
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <UrlAvailabilityIndicator status={urlCheckStatus} />
            </div>
          )}
        </div>
      </div>
      
      {urlCheckStatus === 'unavailable' && (
        <UrlSuggestion 
          urlSuggestion={urlSuggestion} 
          onUseSuggestion={onUseSuggestion} 
        />
      )}
      
      <p className="text-xs text-muted-foreground">
        Link to your store. Changing this will break your existing QR code and shared links.
      </p>
    </div>
  );
};

export default StoreUrlInput;
