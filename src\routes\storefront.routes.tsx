
import React from 'react';
import { 
  AccountDashboard, 
  AccountWishlist, 
  AccountProfile, 
  AccountSettings, 
  AccountOrders, 
  AccountAddresses 
} from '@/pages/storefront/Account/index';
import Account from '@/pages/storefront/Account';
import Shop from '@/pages/storefront/Shop';
import ProductDetails from '@/pages/storefront/ProductDetails';
import ProductListing from '@/pages/storefront/ProductListing';
import { UserRole } from '@/constants/roles';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import StorefrontLayout from '@/components/storefront/StorefrontLayout';
import NotFound from '@/pages/NotFound';
import FeaturedProducts from '@/pages/storefront/FeaturedProducts';

// Debug the route configuration
console.log("Loading storefront routes");

// Export the storefront routes with a fixed structure
export const storefrontRoutes = [
  {
    path: "/",
    element: <StorefrontLayout />,
    children: [
      { 
        index: true, 
        element: <Shop /> 
      },
      { 
        path: "products", 
        element: <ProductListing /> 
      },
      { 
        path: "product/:id", 
        element: <ProductDetails /> 
      },
      { 
        path: "category/:category", 
        element: <ProductListing /> 
      },
      { 
        path: "search", 
        element: <ProductListing /> 
      },
      { 
        path: "featured", 
        element: <FeaturedProducts /> 
      },
      { 
        path: "new", 
        element: <ProductListing /> 
      },
      { 
        path: "categories", 
        element: <ProductListing /> 
      },
      {
        path: "cart",
        element: <div>Cart Page (Coming Soon)</div>
      }
    ]
  },
  {
    path: "/account",
    element: (
      <ProtectedRoute requiredRoles={[UserRole.Customer]}>
        <Account />
      </ProtectedRoute>
    ),
    children: [
      { 
        index: true, 
        element: <AccountDashboard /> 
      },
      { 
        path: "wishlist", 
        element: <AccountWishlist /> 
      },
      { 
        path: "profile", 
        element: <AccountProfile /> 
      },
      { 
        path: "settings", 
        element: <AccountSettings /> 
      },
      { 
        path: "orders", 
        element: <AccountOrders /> 
      },
      { 
        path: "addresses", 
        element: <AccountAddresses /> 
      }
    ]
  }
];

console.log("Storefront routes loaded:", storefrontRoutes.length);
