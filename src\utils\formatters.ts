/**
 * Format a number as currency.
 *
 * @param amount The number to format.
 * @param currency The currency code (e.g., 'USD', 'EUR', 'KES'). Defaults to 'TZS'.
 * @param locale The locale to use for formatting. Defaults to 'sw-TZ'.
 * @returns The formatted currency string.
 */
export const formatCurrency = (
  amount: number,
  currency: string = 'TZS',
  locale: string = 'sw-TZ'
) => {
  let currencyFormat = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });

  // Override the locale if KES is selected, since it is not well supported by default
  if (currency === 'KES') {
    currencyFormat = new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
  }

  return currencyFormat.format(amount);
};
