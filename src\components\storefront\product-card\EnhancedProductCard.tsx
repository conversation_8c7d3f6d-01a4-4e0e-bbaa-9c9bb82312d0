
import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Product } from '@/types/unified-product';
import { formatCurrency } from "@/utils/formatters";
import { ShoppingCart, Heart, Star, ExternalLink } from "lucide-react";
import { useCart, useWishlist } from "@/contexts";
import { toast } from "sonner";

interface EnhancedProductCardProps {
  product: Product;
  viewMode: "grid" | "list";
}

const EnhancedProductCard: React.FC<EnhancedProductCardProps> = ({ product, viewMode }) => {
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const productInWishlist = product.id ? isInWishlist(product.id) : false;

  const handleViewProduct = () => {
    navigate(`/shop/product/${product.id}`);
  };

  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addToCart(product, 1);
    toast.success(`Added ${product.name} to cart`);
  };

  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (productInWishlist && product.id) {
      removeFromWishlist(product.id);
      toast.success(`Removed ${product.name} from wishlist`);
    } else {
      addToWishlist(product);
      toast.success(`Added ${product.name} to wishlist`);
    }
  };

  // Rating component (mock rating for display purposes)
  const RatingStars = () => {
    const rating = 4.5; // Mock rating
    return (
      <div className="flex items-center gap-1">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={`h-3 w-3 ${i < Math.floor(rating) ? 'text-yellow-400 fill-yellow-400' : 'text-gray-300'}`} 
          />
        ))}
        <span className="text-xs text-gray-500 ml-1">{rating}</span>
      </div>
    );
  };

  // Grid mode card
  if (viewMode === "grid") {
    return (
      <div 
        className="group bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 transition-all duration-300 hover:shadow-md cursor-pointer h-full flex flex-col"
        onClick={handleViewProduct}
      >
        {/* Product Image */}
        <div className="relative pt-[100%] overflow-hidden bg-gray-100">
          {product.images && product.images.length > 0 ? (
            <img 
              src={product.images[0]} 
              alt={product.name} 
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              No image
            </div>
          )}
          
          {/* Quick action button */}
          <Button 
            onClick={handleToggleWishlist}
            size="icon"
            variant={productInWishlist ? "default" : "outline"}
            className="absolute top-2 right-2 h-8 w-8 bg-white/80 backdrop-blur-sm border-gray-200 shadow-sm transition-transform duration-300 opacity-0 group-hover:opacity-100 hover:scale-110"
          >
            <Heart className={`h-4 w-4 ${productInWishlist ? 'fill-current' : ''}`} />
          </Button>
          
          {/* Tags */}
          <div className="absolute top-2 left-2 flex flex-col gap-1">
            {product.tags?.includes('new') && (
              <Badge className="bg-purple-500 hover:bg-purple-600">New</Badge>
            )}
            {product.tags?.includes('sale') && (
              <Badge className="bg-red-500 hover:bg-red-600">Sale</Badge>
            )}
            {product.stock_quantity !== undefined && product.stock_quantity <= 5 && product.stock_quantity > 0 && (
              <Badge variant="outline" className="bg-white/80 backdrop-blur-sm text-xs">
                Only {product.stock_quantity} left
              </Badge>
            )}
          </div>
        </div>
        
        {/* Product Info */}
        <div className="p-4 flex-grow flex flex-col">
          <div className="mb-1">
            {product.category && (
              <p className="text-xs text-purple-600 font-medium mb-1">{product.category}</p>
            )}
            <h3 className="font-medium text-sm mb-1 line-clamp-2 group-hover:text-purple-700 transition-colors">
              {product.name}
            </h3>
            <RatingStars />
          </div>
          
          <div className="mt-auto pt-3">
            <p className="font-bold text-lg mb-3">
              {formatCurrency(product.price, product.currency)}
            </p>
            
            <Button 
              onClick={handleAddToCart}
              className="w-full group-hover:bg-purple-700 transition-colors"
              disabled={product.stock_quantity !== undefined && product.stock_quantity <= 0}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add to Cart
            </Button>
          </div>
        </div>
      </div>
    );
  }
  
  // List mode card
  return (
    <div 
      className="group bg-white rounded-lg shadow-sm overflow-hidden border border-gray-100 transition-all duration-300 hover:shadow-md cursor-pointer"
      onClick={handleViewProduct}
    >
      <div className="flex flex-col sm:flex-row">
        {/* Product Image */}
        <div className="relative sm:w-1/4 pt-[60%] sm:pt-0 overflow-hidden bg-gray-100">
          {product.images && product.images.length > 0 ? (
            <img 
              src={product.images[0]} 
              alt={product.name} 
              className="absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110 sm:relative"
            />
          ) : (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400 sm:relative">
              No image
            </div>
          )}
          
          {/* Tags */}
          <div className="absolute top-2 left-2 flex gap-1">
            {product.tags?.includes('new') && (
              <Badge className="bg-purple-500 hover:bg-purple-600">New</Badge>
            )}
            {product.tags?.includes('sale') && (
              <Badge className="bg-red-500 hover:bg-red-600">Sale</Badge>
            )}
          </div>
        </div>
        
        {/* Product Info */}
        <div className="p-4 sm:w-3/4 flex flex-col">
          <div className="flex justify-between">
            <div>
              {product.category && (
                <p className="text-xs text-purple-600 font-medium mb-1">{product.category}</p>
              )}
              <h3 className="font-medium text-lg mb-1 group-hover:text-purple-700 transition-colors">
                {product.name}
              </h3>
              <RatingStars />
            </div>
            <p className="font-bold text-lg">
              {formatCurrency(product.price, product.currency)}
            </p>
          </div>
          
          {product.description && (
            <p className="text-sm text-gray-600 my-2 line-clamp-2">{product.description}</p>
          )}
          
          {product.stock_quantity !== undefined && product.stock_quantity <= 5 && product.stock_quantity > 0 && (
            <p className="text-amber-600 text-xs font-medium">Only {product.stock_quantity} left in stock</p>
          )}
          
          <div className="mt-auto pt-3 flex gap-2 justify-end">
            <Button 
              variant="outline"
              size="sm"
              onClick={handleToggleWishlist}
            >
              <Heart className={`h-4 w-4 mr-2 ${productInWishlist ? 'fill-current' : ''}`} />
              {productInWishlist ? 'Saved' : 'Save'}
            </Button>
            
            <Button 
              onClick={handleAddToCart}
              size="sm"
              disabled={product.stock_quantity !== undefined && product.stock_quantity <= 0}
            >
              <ShoppingCart className="h-4 w-4 mr-2" />
              Add to Cart
            </Button>
            
            <Button variant="outline" size="icon" onClick={handleViewProduct}>
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedProductCard;
