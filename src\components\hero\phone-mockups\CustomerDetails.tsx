
import { motion } from "framer-motion";
import { User, Phone, MapPin } from "lucide-react";

const CustomerDetails = () => {
  return (
    <div className="p-4 border-b">
      <div className="text-sm font-medium mb-2 flex items-center">
        <span className="bg-green-100 p-1 rounded-md text-green-600 mr-2">
          <User className="h-3.5 w-3.5" />
        </span>
        Customer Details
      </div>
      <div className="text-xs text-gray-600 space-y-1">
        <motion.div 
          className="flex items-center"
          initial={{ opacity: 0, x: -5 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          <User className="h-3 w-3 mr-1.5 text-gray-400" />
          <PERSON><PERSON> Said
        </motion.div>
        <motion.div 
          className="flex items-center"
          initial={{ opacity: 0, x: -5 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.1 }}
        >
          <Phone className="h-3 w-3 mr-1.5 text-gray-400" />
          +255 712 345 678
        </motion.div>
        <motion.div 
          className="flex items-center"
          initial={{ opacity: 0, x: -5 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3, delay: 0.2 }}
        >
          <MapPin className="h-3 w-3 mr-1.5 text-gray-400" />
          123 Mbezi Road, Dar es Salaam
        </motion.div>
      </div>
    </div>
  );
};

export default CustomerDetails;
