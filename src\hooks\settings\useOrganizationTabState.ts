
import { useState } from 'react';

export const useOrganizationTabState = () => {
  const [companyName, setCompanyName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [companyEmail, setCompanyEmail] = useState('');
  const [companyPhone, setCompanyPhone] = useState('');
  const [companyAddress, setCompanyAddress] = useState('');
  const [registrationNumber, setRegistrationNumber] = useState('');
  const [taxIdentificationNumber, setTaxIdentificationNumber] = useState('');

  return {
    companyName,
    setCompanyName,
    businessType,
    setBusinessType,
    companyEmail,
    setCompanyEmail,
    companyPhone,
    setCompanyPhone,
    companyAddress,
    setCompanyAddress,
    registrationNumber,
    setRegistrationNumber,
    taxIdentificationNumber,
    setTaxIdentificationNumber,
  };
};
