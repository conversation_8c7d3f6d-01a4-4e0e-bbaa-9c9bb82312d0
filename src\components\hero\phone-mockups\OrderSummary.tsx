
import { motion } from "framer-motion";

interface OrderItem {
  name: string;
  qty: number;
  price: string;
}

interface OrderSummaryProps {
  items: OrderItem[];
}

const OrderSummary = ({ items }: OrderSummaryProps) => {
  return (
    <div className="p-4 border-b">
      <div className="text-sm font-medium mb-2">Order Items</div>
      <div className="space-y-2">
        {items.map((item, index) => (
          <motion.div 
            key={index} 
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.6 + (index * 0.1) }}
            className="flex justify-between text-xs"
          >
            <div>
              <span className="font-medium">{item.name}</span>
              <span className="text-gray-500"> x{item.qty}</span>
            </div>
            <div>{item.price}</div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default OrderSummary;
