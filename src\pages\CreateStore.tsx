
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Container } from '@/components/ui/container';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { useStore } from '@/contexts';
import StoreWizard from '@/components/store/StoreWizard';
import { Loader2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Drawer, DrawerContent, DrawerTrigger } from '@/components/ui/drawer';
import { useIsMobile } from '@/hooks/use-mobile';
import { StoreWizardProvider, useStoreWizard, steps } from '@/components/store/wizard/StoreWizardContext';
import WizardProgress from '@/components/store/wizard/WizardProgress';

const CreateStore: React.FC = () => {
  const { stores, isLoading } = useStore();
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <Container className="max-w-4xl">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Create a New Store</h1>
          <Button asChild variant="outline">
            <Link to="/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <p className="text-muted-foreground">Loading...</p>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <>
      <Helmet>
        <title>Create Store - m-duka</title>
      </Helmet>
      <Container className="max-w-4xl">
        <div className="flex justify-between items-center mb-4">
          <h1 className="text-2xl font-bold">Create a New Store</h1>
          <Button asChild variant="outline">
            <Link to="/dashboard">Back to Dashboard</Link>
          </Button>
        </div>
        
        <StoreWizardProvider>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Mobile Tab Drawer */}
            {isMobile && (
              <Drawer>
                <DrawerTrigger asChild>
                  <Button variant="outline" className="mb-4 w-full flex items-center justify-between">
                    <span>Navigate Steps</span>
                    <span className="ml-2">↓</span>
                  </Button>
                </DrawerTrigger>
                <DrawerContent>
                  <div className="p-4">
                    <StoreWizardSidebar />
                  </div>
                </DrawerContent>
              </Drawer>
            )}

            {/* Desktop Sidebar */}
            {!isMobile && (
              <div className="w-full md:w-64 shrink-0">
                <StoreWizardSidebar />
              </div>
            )}

            {/* Main Content */}
            <div className="flex-1">
              <WizardProgress />
              <StoreWizard />
            </div>
          </div>
        </StoreWizardProvider>
      </Container>
    </>
  );
};

// Sidebar component with wizard steps
const StoreWizardSidebar = () => {
  const { currentStep } = useStoreWizard();
  
  return (
    <Card className="h-full">
      <CardContent className="p-3">
        <h3 className="text-sm font-medium mb-2">Store Setup</h3>
        <Tabs 
          defaultValue={currentStep.toString()} 
          orientation="vertical" 
          className="w-full" 
          onValueChange={(value) => {
            // This is read-only in the sidebar, actual navigation happens in the wizard
          }}
        >
          <TabsList className="flex flex-col h-auto bg-transparent space-y-1 p-0">
            {steps.map((step, index) => (
              <TabsTrigger 
                key={step.id}
                value={index.toString()} 
                className={`justify-start text-left ${currentStep === index ? 'bg-primary/10 text-primary' : ''}`}
                disabled
              >
                <div className="flex items-center">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center mr-2 ${currentStep >= index ? 'bg-green-500 text-white' : 'bg-muted'}`}>
                    {index + 1}
                  </div>
                  {step.label}
                </div>
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CreateStore;
