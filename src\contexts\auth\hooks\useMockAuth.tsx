
import { useCallback } from 'react';
import { User } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { UserRole } from '@/constants/roles';

export const useMockAuth = (
  setUser: React.Dispatch<React.SetStateAction<User | null>>,
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
) => {
  // Mock a store owner user for development purposes
  const mockUser = useCallback(() => {
    setIsLoading(true);
    const mockUserId = uuidv4();
    setUser({
      id: mockUserId,
      email: '<EMAIL>',
      name: 'Mock Store Owner',
      role: UserRole.StoreOwner,
      avatar_url: null
    });
    setIsLoading(false);
    
    console.log('Created mock store owner user with ID:', mockUserId);
  }, [setUser, setIsLoading]);
  
  // Mock an admin user for development purposes
  const loginAsAdmin = useCallback(() => {
    setIsLoading(true);
    const mockAdminId = uuidv4();
    setUser({
      id: mockAdminId,
      email: '<EMAIL>',
      name: 'Mock Admin',
      role: UserRole.Admin,
      avatar_url: null
    });
    setIsLoading(false);
    
    console.log('Created mock admin user with ID:', mockAdminId);
  }, [setUser, setIsLoading]);
  
  return { mockUser, loginAsAdmin };
};
