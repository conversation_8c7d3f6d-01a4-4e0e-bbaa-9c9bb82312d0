
interface OrderHeaderProps {
  orderNumber: string;
}

const OrderHeader = ({ orderNumber }: OrderHeaderProps) => {
  return (
    <div className="bg-green-600 text-white p-4 rounded-t-lg">
      <div className="flex items-center justify-between mb-1">
        <div className="text-xl font-bold">Order #{orderNumber}</div>
        <span className="bg-white text-green-600 text-xs px-2 py-0.5 rounded-full">Paid</span>
      </div>
      <div className="text-xs flex items-center">
        <span>Order Confirmed</span>
      </div>
    </div>
  );
};

export default OrderHeader;
