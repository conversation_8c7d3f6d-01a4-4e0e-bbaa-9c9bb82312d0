
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ShoppingCart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Product } from '@/types/unified-product';
import { formatCurrency } from '@/utils/formatters';
import { useCart } from '@/contexts';

interface LightweightProductItemProps {
  product: Product;
}

const LightweightProductItem: React.FC<LightweightProductItemProps> = ({ product }) => {
  const navigate = useNavigate();
  const { addToCart } = useCart();
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addToCart(product, 1);
  };
  
  const handleClick = () => {
    navigate(`/shop/product/${product.id}`);
  };
  
  // Skip rendering inactive or out-of-stock products
  if (!product.is_active || product.stock_quantity <= 0) {
    return null;
  }

  return (
    <div 
      className="group border rounded-md overflow-hidden hover:shadow-md transition-shadow cursor-pointer flex flex-col"
      onClick={handleClick}
    >
      {/* Product Image (simplified) */}
      <div className="h-40 overflow-hidden bg-gray-100">
        {product.images && product.images.length > 0 ? (
          <img 
            src={product.images[0]} 
            alt={product.name} 
            className="w-full h-full object-cover transition-transform group-hover:scale-105"
            loading="lazy" // Added for performance
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center text-gray-400">
            No Image
          </div>
        )}
      </div>
      
      {/* Product Info (simplified) */}
      <div className="p-3 flex-grow flex flex-col">
        <h3 className="font-medium text-base mb-1 line-clamp-1">{product.name}</h3>
        
        {product.category && (
          <span className="text-xs text-gray-500 mb-1">{product.category}</span>
        )}
        
        <div className="mt-auto pt-2 flex justify-between items-center">
          <p className="font-semibold">
            {formatCurrency(product.price, product.currency)}
          </p>
          <Button 
            size="sm" 
            variant="ghost"
            className="h-8 w-8 p-0" 
            onClick={handleAddToCart}
          >
            <ShoppingCart className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default LightweightProductItem;
