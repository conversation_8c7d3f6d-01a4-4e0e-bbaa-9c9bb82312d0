
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON><PERSON>, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { MessageSquare, Bell, RefreshCw } from 'lucide-react';
import { Form, FormField, FormItem, FormLabel, FormControl, FormDescription } from '@/components/ui/form';
import { useForm } from 'react-hook-form';

interface WhatsAppSettingsCardProps {
  whatsappNumber: string;
  setWhatsappNumber: (value: string) => void;
  phoneNumber: string;
  enableOrderNotifications?: boolean;
  setEnableOrderNotifications?: (value: boolean) => void;
  enableCustomerUpdates?: boolean;
  setEnableCustomerUpdates?: (value: boolean) => void;
  customMessage?: string;
  setCustomMessage?: (value: string) => void;
  autoReply?: boolean;
  setAutoReply?: (value: boolean) => void;
  onSave?: (e?: React.FormEvent) => Promise<void>;
  isSaving?: boolean;
}

const WhatsAppSettingsCard = ({
  whatsappNumber,
  setWhatsappNumber,
  phoneNumber,
  enableOrderNotifications = false,
  setEnableOrderNotifications = () => {},
  enableCustomerUpdates = false,
  setEnableCustomerUpdates = () => {},
  customMessage = '',
  setCustomMessage = () => {},
  autoReply = false,
  setAutoReply = () => {},
  onSave = async () => {},
  isSaving = false
}: WhatsAppSettingsCardProps) => {
  const form = useForm({
    defaultValues: {
      whatsappNumber,
      enableOrderNotifications,
      enableCustomerUpdates,
      customMessage,
      autoReply
    }
  });

  const handleSubmit = form.handleSubmit(async (data) => {
    setWhatsappNumber(data.whatsappNumber);
    setEnableOrderNotifications(data.enableOrderNotifications);
    setEnableCustomerUpdates(data.enableCustomerUpdates);
    setCustomMessage(data.customMessage);
    setAutoReply(data.autoReply);
    await onSave();
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" /> 
          WhatsApp Business Settings
        </CardTitle>
        <CardDescription>
          Configure WhatsApp integration for order notifications and customer communication
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="whatsappNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>WhatsApp Business Number</FormLabel>
                    <FormDescription>
                      Customers will send order messages to this number. To receive automatic order notifications, 
                      please enable notifications below.
                    </FormDescription>
                    <div className="flex items-center gap-2">
                      <Input value="+255" className="w-20" disabled />
                      <FormControl>
                        <Input 
                          placeholder="Enter your WhatsApp business number"
                          {...field}
                        />
                      </FormControl>
                    </div>
                  </FormItem>
                )}
              />
              
              <div className="pt-2">
                <Label>Phone number (from profile)</Label>
                <Input 
                  value={phoneNumber} 
                  disabled 
                  className="bg-muted"
                  placeholder="Your business phone number" 
                />
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4">Workflow Configuration</h3>
              
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="enableOrderNotifications"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base flex items-center gap-2">
                          <Bell className="h-4 w-4" /> 
                          Order Notifications
                        </FormLabel>
                        <FormDescription>
                          Receive WhatsApp notifications when new orders are placed
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="enableCustomerUpdates"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base flex items-center gap-2">
                          <RefreshCw className="h-4 w-4" /> 
                          Customer Updates
                        </FormLabel>
                        <FormDescription>
                          Send order status updates to customers via WhatsApp
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="autoReply"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                      <div className="space-y-0.5">
                        <FormLabel className="text-base">Auto-Reply</FormLabel>
                        <FormDescription>
                          Send automatic replies to customer messages
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="customMessage"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Custom Message Template</FormLabel>
                      <FormDescription>
                        Customize the message sent to customers when order status changes
                      </FormDescription>
                      <FormControl>
                        <Textarea 
                          placeholder="Hello {customer}, your order #{order_id} status has been updated to {status}. Thank you for shopping with us!"
                          className="h-24"
                          {...field}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </div>
            
            <CardFooter className="px-0 pt-4">
              <Button type="submit" disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save WhatsApp Settings'}
              </Button>
            </CardFooter>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default WhatsAppSettingsCard;
