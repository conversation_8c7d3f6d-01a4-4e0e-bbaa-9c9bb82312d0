
import React from "react";
import { Link } from "react-router-dom";

const DesktopNavigation: React.FC = () => {
  return (
    <nav className="hidden md:block">
      <ul className="flex space-x-6">
        <li>
          <Link to="/shop" className="hover:text-primary">
            Home
          </Link>
        </li>
        <li>
          <Link to="/shop/categories" className="hover:text-primary">
            Categories
          </Link>
        </li>
        <li>
          <Link to="/shop/products" className="hover:text-primary">
            All Products
          </Link>
        </li>
        <li>
          <Link to="/shop/search?sort=newest" className="hover:text-primary">
            New Arrivals
          </Link>
        </li>
      </ul>
    </nav>
  );
};

export default DesktopNavigation;
