
import React from 'react';
import { <PERSON>, <PERSON><PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LucideIcon, Calendar, Bell, Clock, Lightbulb, ExternalLink, Zap, ArrowRight } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ComingSoonPlaceholderProps {
  title: string;
  description: string;
  icon: LucideIcon;
  expectedDate?: string;
  actionLabel?: string;
  onAction?: () => void;
}

const ComingSoonPlaceholder = ({
  title,
  description,
  icon: Icon,
  expectedDate,
  actionLabel,
  onAction
}: ComingSoonPlaceholderProps) => {
  // Select a random tip to display
  const tips = [
    "Use automation to receive instant alerts when inventory reaches critical levels",
    "Track stock levels across multiple locations with centralized inventory management",
    "Set up automatic reorder notifications to maintain optimal stock levels",
    "Monitor high-value product stock levels with priority notifications",
    "Create custom alert thresholds based on seasonal demand patterns",
    "Automate inventory reports to be sent to your team on a schedule",
    "Get notified when new shipments arrive with instant stock updates",
    "Identify slow-moving inventory before it impacts your bottom line"
  ];
  
  const randomTip = tips[Math.floor(Math.random() * tips.length)];
  
  return (
    <Card className="border-dashed">
      <CardHeader className="text-center pb-2">
        <div className="mx-auto bg-primary/10 p-3 rounded-full w-fit mb-4">
          <Icon className="h-8 w-8 text-primary" />
        </div>
        <CardTitle className="text-xl">{title}</CardTitle>
        <CardDescription className="text-base max-w-md mx-auto">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-2 space-y-6">
        {expectedDate && (
          <div className="flex items-center justify-center gap-2 text-sm bg-secondary/50 py-2 px-4 rounded-md">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span>Expected release: {expectedDate}</span>
          </div>
        )}
        
        <div className="bg-blue-50 border border-blue-100 rounded-md p-4">
          <div className="flex gap-2">
            <Zap className="h-5 w-5 text-blue-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="font-medium text-blue-800 mb-1">Pubbly Connect Integration</p>
              <p className="text-sm text-blue-700">This feature will be available as part of our Pubbly Connect integration.</p>
              <Button variant="link" className="h-auto p-0 text-blue-600 mt-1 flex items-center">
                Learn about Pubbly Connect
                <ExternalLink className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </div>
        </div>
        
        <div className="bg-amber-50 border border-amber-100 rounded-md p-4">
          <div className="flex gap-2">
            <Lightbulb className="h-5 w-5 text-amber-500 flex-shrink-0 mt-0.5" />
            <div>
              <p className="font-medium text-amber-800 mb-1">Pro Tip</p>
              <p className="text-sm text-amber-700">{randomTip}</p>
            </div>
          </div>
        </div>
        
        {actionLabel && onAction && (
          <Button 
            onClick={onAction} 
            variant="outline" 
            className="w-full"
          >
            {actionLabel}
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        )}
        
        {expectedDate && (
          <div className="flex justify-center items-center gap-2 text-xs text-muted-foreground">
            <Bell className="h-3 w-3" />
            <span>We'll notify you when this feature is released</span>
          </div>
        )}
        
        <div className="flex justify-center">
          <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
            <Clock className="mr-1 h-3 w-3" />
            Powered by Pubbly Connect
          </Badge>
        </div>
      </CardContent>
    </Card>
  );
};

export default ComingSoonPlaceholder;
