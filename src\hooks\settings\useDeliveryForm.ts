import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { 
  deliveryFormSchema, 
  DeliveryFormValues,
  defaultValues,
} from "@/components/settings/delivery/DeliveryFormSchema";
import { isAfricanCountry } from "@/components/settings/delivery/utils/africanRegions";

export const useDeliveryForm = () => {
  const [selectedCountry, setSelectedCountry] = useState<string>(defaultValues.country || "");
  const [allAfricanRegions, setAllAfricanRegions] = useState<Record<string, string[]>>({});
  const [isLoadingRegions, setIsLoadingRegions] = useState<boolean>(false);
  const [errorLoadingRegions, setErrorLoadingRegions] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    const fetchRegions = async () => {
      setIsLoadingRegions(true);
      setErrorLoadingRegions(null);
      try {
        const response = await fetch('/data/africanRegions.json');
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data: Record<string, string[]> = await response.json();
        setAllAfricanRegions(data);
      } catch (error) {
        console.error("Failed to fetch African regions:", error);
        setErrorLoadingRegions("Failed to load region data. Please try again later.");
        toast({
          title: "Error",
          description: "Could not load region data.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingRegions(false);
      }
    };

    fetchRegions();
  }, [toast]); // Added toast dependency
  
  const form = useForm<DeliveryFormValues>({
    resolver: zodResolver(deliveryFormSchema),
    defaultValues,
  });

  const handleCountryChange = (country: string) => {
    setSelectedCountry(country);
    
    // Reset delivery zones when country changes
    if (isAfricanCountry(country)) {
      // Only initialize if empty
      const currentZones = form.getValues("deliveryZones");
      if (!currentZones || currentZones.length === 0) {
        form.setValue("deliveryZones", []); 
      }
    } else {
      // Clear zones for non-African countries
      form.setValue("deliveryZones", []);
    }
  };

  function onSubmit(data: DeliveryFormValues) {
    console.log("Delivery settings updated:", data);
    // Here you would save the data to your backend
    toast({
      title: "Settings Updated",
      description: "Your delivery settings have been saved successfully.",
    });
  }

  return {
    form,
    selectedCountry,
    handleCountryChange,
    onSubmit,
    allAfricanRegions, // Provide the fetched regions
    isLoadingRegions, // Provide loading state
    errorLoadingRegions, // Provide error state
    isAfricanCountry: isAfricanCountry(selectedCountry)
  };
};