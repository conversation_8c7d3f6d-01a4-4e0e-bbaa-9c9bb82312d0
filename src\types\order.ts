
// Address type for shipping and billing addresses
export interface Address {
  firstName?: string;
  lastName?: string;
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  postalCode?: string;
  country?: string;
  phone?: string;
  email?: string;
}

// Allow string status to match database
export type OrderStatus = 'pending' | 'processing' | 'paid' | 'shipped' | 'delivered' | 'cancelled' | 'refunded' | 'payment_failed' | string;

// Order type
export interface Order {
  id: string;
  user_id: string;
  store_id: string;
  items: OrderItem[];
  status: OrderStatus;
  total_amount: number;
  shipping_address?: Address;
  billing_address?: Address;
  payment_method?: string;
  currency: string;
  created_at: string | Date;
  updated_at?: string | Date;
}

// Order item type
export interface OrderItem {
  product_id: string;
  variant_id?: string | null;
  name: string;
  price: number;
  quantity: number;
}

// Payment transaction type
export interface Transaction {
  id: string;
  orderId: string;
  userId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  status: string;
  transactionId?: string | null;
  transactionReference?: string | null;
  paymentData?: any;
  createdAt: Date;
  updatedAt: Date;
}
