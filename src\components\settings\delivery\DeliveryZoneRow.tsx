
import React from 'react';
import { 
  FormField, 
  FormControl, 
  FormItem, 
  FormLabel 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface DeliveryZoneRowProps {
  index: number;
  regions: string[];
  form: any;
  onRemove: (index: number) => void;
}

const DeliveryZoneRow: React.FC<DeliveryZoneRowProps> = ({ 
  index, 
  regions, 
  form, 
  onRemove 
}) => {
  return (
    <div className="grid grid-cols-12 gap-4 items-end">
      <div className="col-span-4">
        <FormField
          control={form.control}
          name={`deliveryZones.${index}.name`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Zone Name</FormLabel>
              <FormControl>
                <Input placeholder="e.g., Urban Area" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      
      <div className="col-span-4">
        <FormField
          control={form.control}
          name={`deliveryZones.${index}.region`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Region</FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {regions.map(region => (
                    <SelectItem key={region} value={region}>{region}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
      </div>
      
      <div className="col-span-3">
        <FormField
          control={form.control}
          name={`deliveryZones.${index}.rate`}
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rate ($)</FormLabel>
              <FormControl>
                <Input placeholder="5.00" type="number" step="0.01" {...field} />
              </FormControl>
            </FormItem>
          )}
        />
      </div>
      
      <div className="col-span-1">
        <Button 
          type="button" 
          variant="ghost" 
          size="icon"
          onClick={() => onRemove(index)}
        >
          <Trash2 className="h-4 w-4 text-destructive" />
        </Button>
      </div>
    </div>
  );
};

export default DeliveryZoneRow;
