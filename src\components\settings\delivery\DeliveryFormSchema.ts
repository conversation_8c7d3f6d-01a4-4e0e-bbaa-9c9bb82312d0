
import * as z from "zod";

export const deliveryZoneSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Zone name is required"),
  region: z.string().min(1, "Region is required"),
  rate: z.string().min(1, "Rate is required"),
});

export const deliveryFormSchema = z.object({
  country: z.string().min(1, "Country is required"),
  freeShippingThreshold: z.string().optional(),
  enableFreeShipping: z.boolean().default(false),
  flatRateShipping: z.string().optional(),
  estimatedDeliveryDays: z.string().optional(),
  enableLocalPickup: z.boolean().default(false),
  localPickupAddress: z.string().optional(),
  enableInternationalShipping: z.boolean().default(false),
  internationalShippingRate: z.string().optional(),
  deliveryZones: z.array(deliveryZoneSchema).optional(),
});

export type DeliveryFormValues = z.infer<typeof deliveryFormSchema>;

export const defaultValues: Partial<DeliveryFormValues> = {
  country: "Kenya", // Default to Kenya as an example
  enableFreeShipping: false,
  freeShippingThreshold: "50",
  flatRateShipping: "5",
  estimatedDeliveryDays: "3-5",
  enableLocalPickup: false,
  enableInternationalShipping: false,
  deliveryZones: [],
};

// List of African countries for reference
export const africanCountries = [
  'Kenya', 'Tanzania', 'Uganda', 
  'Nigeria', 'Ghana', 'South Africa', 
  'Egypt', 'Morocco', 'Ethiopia', 
  'Rwanda', 'Senegal'
];

// Helper function to check if a country is in the list of African countries
export const isAfricanCountry = (country: string): boolean => {
  return africanCountries.includes(country);
};
