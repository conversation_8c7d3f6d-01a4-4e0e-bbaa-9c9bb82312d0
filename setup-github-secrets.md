# 🔐 GitHub Secrets Setup for Lovable.dev Deployment

## Required Secrets

To deploy your M-Duka app to Lovable.dev, you need to set up the following secrets in your GitHub repository:

### 1. Supabase Environment Variables
```
VITE_PUBLIC_SUPABASE_URL=https://nheycjpozywomwscplcz.supabase.co
VITE_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g
```

### 2. Lovable.dev API Key
```
LOVABLE_API_KEY=your_lovable_api_key_here
```

### 3. Lovable Git URL (for push-to-lovable workflow)
```
LOVABLE_GIT_URL=your_lovable_git_url_here
```

## How to Set Up GitHub Secrets

1. **Go to your GitHub repository**
   - Navigate to: `https://github.com/your-username/m-duka-app`

2. **Access Repository Settings**
   - Click on the "Settings" tab
   - In the left sidebar, click "Secrets and variables"
   - Click "Actions"

3. **Add New Repository Secrets**
   - Click "New repository secret"
   - Add each secret one by one:

### Secret 1: VITE_PUBLIC_SUPABASE_URL
- **Name**: `VITE_PUBLIC_SUPABASE_URL`
- **Value**: `https://nheycjpozywomwscplcz.supabase.co`

### Secret 2: VITE_PUBLIC_SUPABASE_ANON_KEY
- **Name**: `VITE_PUBLIC_SUPABASE_ANON_KEY`
- **Value**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g`

### Secret 3: LOVABLE_API_KEY
- **Name**: `LOVABLE_API_KEY`
- **Value**: Get this from your Lovable.dev account settings

### Secret 4: LOVABLE_GIT_URL (Optional)
- **Name**: `LOVABLE_GIT_URL`
- **Value**: Get this from your Lovable.dev project settings

## 🚀 Deployment Process

Once secrets are set up:

1. **Automatic Deployment**
   ```bash
   git add .
   git commit -m "Deploy to Lovable.dev"
   git push origin main
   ```

2. **Manual Deployment**
   - Go to GitHub Actions tab
   - Click "Deploy to lovable.dev"
   - Click "Run workflow"

## 🔍 Verify Deployment

1. **Check GitHub Actions**
   - Go to the "Actions" tab in your repository
   - Look for the "Deploy to lovable.dev" workflow
   - Ensure it completes successfully

2. **Access Your App**
   - **Lovable.dev URL**: Check your Lovable.dev dashboard
   - **Custom Domains**: 
     - `https://m-duka.app`
     - `https://viomify.com`

## 🛠️ Troubleshooting

### Common Issues:

1. **Deployment Fails**
   - Check that all secrets are correctly set
   - Verify the LOVABLE_API_KEY is valid
   - Check GitHub Actions logs for specific errors

2. **Supabase Connection Issues**
   - Verify the Supabase URL and key are correct
   - Check that your Supabase project is active
   - Test the connection locally first

3. **Domain Issues**
   - Ensure domains are properly configured in Lovable.dev
   - Check DNS settings for custom domains

## 📞 Support

If you encounter issues:
1. Check the GitHub Actions logs
2. Verify all secrets are correctly set
3. Test the connection locally using the development server
4. Contact Lovable.dev support if deployment issues persist

Your Supabase + Lovable.dev connection is ready! 🎉 