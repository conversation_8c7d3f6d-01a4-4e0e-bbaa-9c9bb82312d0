
import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BarChart4 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import SidebarGroup from './SidebarGroup';

interface ReportsGroupProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const ReportsGroup: React.FC<ReportsGroupProps> = ({
  isOpen,
  onOpenChange
}) => {
  return (
    <SidebarGroup
      title="Reports"
      icon={<BarChart4 className="h-5 w-5 mr-2" />}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      isActive={false}
    >
      <Button variant="ghost" className="w-full justify-start px-3 py-2 h-auto" asChild disabled>
        <Link to="/reports/sales">Sales</Link>
      </Button>
      <Button variant="ghost" className="w-full justify-start px-3 py-2 h-auto" asChild disabled>
        <Link to="/reports/customers">Customers</Link>
      </Button>
      <Button variant="ghost" className="w-full justify-start px-3 py-2 h-auto" asChild disabled>
        <Link to="/reports/inventory">Inventory</Link>
      </Button>
    </SidebarGroup>
  );
};

export default ReportsGroup;
