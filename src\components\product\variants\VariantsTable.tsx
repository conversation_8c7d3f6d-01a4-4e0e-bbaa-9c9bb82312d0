
import React from 'react';
import { ProductVariant } from '@/types/unified-product';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import VariantRow from './VariantRow';

interface VariantsTableProps {
  variants: ProductVariant[];
  onUpdateVariant: (index: number, field: keyof ProductVariant, value: any) => void;
  onRemoveVariant: (index: number) => void;
}

const VariantsTable: React.FC<VariantsTableProps> = ({
  variants,
  onUpdateVariant,
  onRemoveVariant
}) => {
  return (
    <div className="border rounded-md overflow-hidden">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Size</TableHead>
            <TableHead>Color</TableHead>
            <TableHead>Price</TableHead>
            <TableHead>SKU</TableHead>
            <TableHead>Stock</TableHead>
            <TableHead>Active</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {variants.map((variant, index) => (
            <VariantRow
              key={index}
              variant={variant}
              index={index}
              onUpdate={onUpdateVariant}
              onRemove={onRemoveVariant}
            />
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default VariantsTable;
