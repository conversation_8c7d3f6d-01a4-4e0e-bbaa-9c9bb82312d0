
import React from 'react';
import { cn } from '@/lib/utils';

interface StoreFooterProps {
  storeName: string;
  darkMode: boolean;
  secondaryColor: string;
  cornerRadius: number;
}

const StoreFooter: React.FC<StoreFooterProps> = ({
  storeName,
  darkMode,
  secondaryColor,
  cornerRadius
}) => {
  return (
    <footer className={cn(
      "mt-auto p-4 text-center",
      darkMode ? "bg-gray-800" : secondaryColor,
      `rounded-t-[${cornerRadius}px]`
    )}>
      {/* Modified for Omitech Group Co. Ltd */}
      <p className="text-sm">
        &copy; {new Date().getFullYear()} Omitech Group Co. Ltd. All rights reserved.
      </p>
    </footer>
  );
};

export default StoreFooter;

