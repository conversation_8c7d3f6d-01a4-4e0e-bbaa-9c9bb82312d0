
import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts';
import MobileNavMenu from './navigation/MobileNavMenu';
import DesktopNav from './navigation/DesktopNav';
import HeaderActions from './navigation/HeaderActions';
import UserProfileDropdown from './navigation/UserProfileDropdown';

const AdminHeader: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [notificationCount] = useState(3); // Sample notification count
  
  const handleSignOut = async () => {
    await logout();
    navigate('/signin');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const userDisplayName = user?.name || 'Admin';
  const userInitials = user?.name ? getInitials(user.name) : 'A';
  const userEmail = user?.email || '<EMAIL>';
  const userAvatarUrl = user?.avatar_url || undefined;

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  return (
    <header className="sticky top-0 z-30 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-16 items-center px-4 md:px-6">
        {/* Mobile menu */}
        <MobileNavMenu isActive={isActive} />

        {/* Desktop navigation */}
        <DesktopNav isActive={isActive} />

        <div className="ml-auto flex items-center gap-4">
          {/* Header actions (search, notifications, dark mode) */}
          <HeaderActions notificationCount={notificationCount} />

          {/* User profile dropdown */}
          <UserProfileDropdown 
            userDisplayName={userDisplayName}
            userInitials={userInitials}
            userEmail={userEmail}
            userAvatarUrl={userAvatarUrl}
            onSignOut={handleSignOut}
          />
        </div>
      </div>
    </header>
  );
};

export default AdminHeader;
