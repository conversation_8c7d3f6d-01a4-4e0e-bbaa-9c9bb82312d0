
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useStore, useProduct } from '@/contexts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProductForm from '@/components/product/ProductForm';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Store } from '@/types/database';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Store as StoreIcon } from 'lucide-react';
import { databaseToUIStore } from '@/utils/typeConverters';

const CreateProduct: React.FC = () => {
  const navigate = useNavigate();
  const { stores, currentStore, setCurrentStore, isLoading: storesLoading } = useStore();
  const { createProduct } = useProduct();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Convert database stores to UI stores for compatibility
  const uiStores = stores.map(databaseToUIStore).filter(Boolean) as any[];
  const uiCurrentStore = currentStore ? databaseToUIStore(currentStore) : null;

  // Set the first store as current if none is selected and stores are available
  useEffect(() => {
    if (!currentStore && stores.length > 0) {
      setCurrentStore(stores[0]);
    }
  }, [stores, currentStore, setCurrentStore]);

  const handleStoreChange = (storeId: string) => {
    setError(null);
    const selectedStore = stores.find(store => store.id === storeId);
    if (selectedStore) {
      setCurrentStore(selectedStore);
    }
  };

  const handleSubmit = async (data: any) => {
    if (!currentStore?.id) {
      setError('Please select a store first');
      return;
    }

    setIsSubmitting(true);
    setError(null);
    
    try {
      await createProduct(data, currentStore.id);
      toast.success('Product created successfully', {
        description: `${data.name} has been added to your store inventory.`,
        duration: 5000,
      });
      navigate('/products');
    } catch (error: any) {
      console.error('Failed to create product:', error);
      setError(error.message || 'Failed to create product. Please try again.');
      toast.error('Failed to create product', {
        description: error.message || 'Please check your input and try again.',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStoreSelection = () => {
    if (uiStores.length === 0) return null;
    
    if (uiStores.length === 1) {
      return (
        <div className="mb-6 p-4 bg-secondary/20 rounded-lg">
          <div className="flex items-center">
            <StoreIcon className="mr-2 h-5 w-5 text-muted-foreground" />
            <span className="text-sm font-medium">Creating product for:</span>
            <Badge className="ml-2" variant="secondary">{uiStores[0].name}</Badge>
          </div>
        </div>
      );
    }
    
    return (
      <div className="mb-6">
        <div className="flex items-start mb-2">
          <StoreIcon className="mr-2 h-5 w-5 text-muted-foreground mt-1" />
          <div>
            <label className="block text-sm font-medium">
              Select Store
            </label>
            <p className="text-sm text-muted-foreground mb-2">Choose which store this product will be added to</p>
          </div>
        </div>
        <Select 
          value={uiCurrentStore?.id || ''} 
          onValueChange={handleStoreChange}
        >
          <SelectTrigger className="w-full max-w-md">
            <SelectValue placeholder="Select a store" />
          </SelectTrigger>
          <SelectContent>
            {uiStores.map((store) => (
              <SelectItem key={store.id} value={store.id || ''}>
                <div className="flex items-center">
                  {store.name}
                  {store.is_active === false && (
                    <Badge variant="outline" className="ml-2">Inactive</Badge>
                  )}
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  };

  return (
    <DashboardLayout>
      <Helmet>
        <title>Create Product - m-duka</title>
      </Helmet>
      <div className="p-6">
        <h1 className="text-3xl font-bold mb-6">Create New Product</h1>

        {storesLoading ? (
          <div className="animate-pulse p-8 rounded-lg bg-muted">
            <div className="h-8 w-1/3 bg-muted-foreground/20 rounded mb-4"></div>
            <div className="h-4 w-2/3 bg-muted-foreground/20 rounded"></div>
          </div>
        ) : stores.length === 0 ? (
          <Card>
            <CardHeader>
              <CardTitle>No Stores Available</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="mb-4">You need to create a store before you can add products.</p>
              <button
                onClick={() => navigate('/create-store')}
                className="bg-primary text-white px-4 py-2 rounded"
              >
                Create Your First Store
              </button>
            </CardContent>
          </Card>
        ) : (
          <>
            {renderStoreSelection()}

            {error && (
              <Alert variant="destructive" className="mb-6">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Product Information</CardTitle>
              </CardHeader>
              <CardContent>
                <ProductForm 
                  onSubmit={handleSubmit}
                  isSubmitting={isSubmitting}
                />
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </DashboardLayout>
  );
};

export default CreateProduct;
