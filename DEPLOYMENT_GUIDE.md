# 🚀 Supabase + Lovable.dev Deployment Guide

## 📋 Overview
Your M-Duka app is now fully connected between Supabase and Lovable.dev! This guide explains how everything works together.

## 🔗 Current Connection Status

### ✅ Supabase Setup
- **Database URL**: `https://nheycjpozywomwscplcz.supabase.co`
- **Client Configuration**: Located in `src/integrations/supabase/client.ts`
- **Backup Configuration**: Also in `src/lib/supabase.ts`
- **Environment Variables**: Configured for all environments

### ✅ Lovable.dev Setup
- **Site ID**: `d6edfe1f-9b57-457c-b66b-98d94e822107`
- **Custom Domains**: `m-duka.app`, `viomify.com`
- **Auto-deployment**: Enabled on every push to `main` branch

## 🛠️ How It Works

### 1. Local Development
```bash
npm run dev
# Runs on http://localhost:8081/
# Uses environment variables with fallback to hardcoded values
```

### 2. Supabase Connection
Your app connects to Supabase in two ways:
- **Primary**: `src/integrations/supabase/client.ts` (Auto-generated)
- **Secondary**: `src/lib/supabase.ts` (Custom operations)

### 3. Environment Variables
The app uses these environment variables:
- `VITE_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `VITE_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### 4. Deployment to Lovable.dev
When you push to the `main` branch:
1. GitHub Actions triggers the workflow
2. Bun builds the project with Supabase environment variables
3. Lovable deploys to your site ID
4. App becomes available on your custom domains

## 🎯 Available Deployment Options

### Option 1: Lovable.dev (Current Setup)
- ✅ **Status**: Active
- 🌐 **Domains**: `m-duka.app`, `viomify.com`
- 🔄 **Auto-deploy**: On every push to `main`

### Option 2: Vercel
- 📁 **Config**: `vercel.json` and setup scripts available
- 🔧 **Setup**: Run `./vercel-env-setup.sh`
- 🌐 **URL**: `https://m-duka-app.vercel.app/`

### Option 3: Netlify
- 📁 **Config**: Ready for Netlify deployment
- 🔧 **Setup**: Connect GitHub repo to Netlify

## 🚀 Quick Deployment Commands

### Deploy to Lovable.dev
```bash
# Automatic on git push
git add .
git commit -m "Update app"
git push origin main
# GitHub Actions will automatically deploy
```

### Deploy to Vercel
```bash
# First time setup
./vercel-env-setup.sh

# Deploy
vercel --prod
```

### Set Supabase Secrets
```bash
# Configure reCAPTCHA and other secrets
./set-supabase-secrets.sh
```

## 📊 Database Operations

Your app includes comprehensive database operations:
- **Stores**: Create, read, update, delete stores
- **Products**: Manage product catalog
- **Orders**: Handle customer orders
- **Customers**: Customer management
- **Wishlists**: Product wishlists
- **Categories**: Product categorization

All operations are in `src/lib/supabase.ts` with full TypeScript support.

## 🔐 Security & Environment

### Production
- Environment variables stored in GitHub Secrets
- Supabase RLS (Row Level Security) recommended
- API keys properly configured

### Development
- Fallback values in code for quick development
- Local environment variables supported
- Test connection with your running dev server

## 🌐 Access Your App

### Development
- Local: http://localhost:8081/
- Hot reload enabled

### Production
- Lovable.dev: Available on your configured domains
- Vercel: https://m-duka-app.vercel.app/ (if configured)

## 🛠️ Troubleshooting

### Connection Issues
1. Check your Supabase project is active
2. Verify environment variables are set
3. Test database connection in browser dev tools

### Deployment Issues
1. Check GitHub Actions for deployment status
2. Verify secrets are configured in repository settings
3. Ensure Lovable API key is valid

## 📝 Next Steps

1. **Test the connection**: Your app should already be working with Supabase
2. **Push to deploy**: Any changes to `main` branch will auto-deploy
3. **Monitor**: Check GitHub Actions for deployment status
4. **Scale**: Add more database tables as needed

Your Supabase + Lovable.dev connection is fully operational! 🎉 