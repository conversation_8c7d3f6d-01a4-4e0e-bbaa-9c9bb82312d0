
import React from "react";
import { Badge } from "@/components/ui/badge";

// Format date to display in a readable format
export const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

// Format currency
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};

// Get initials for avatar fallback
export const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase();
};

export const CustomerStatusBadge = ({ status }: { status: string }) => {
  if (status === 'active') {
    return <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200">Active</Badge>;
  }
  return <Badge variant="outline" className="bg-gray-50 text-gray-600 border-gray-200">Inactive</Badge>;
};
