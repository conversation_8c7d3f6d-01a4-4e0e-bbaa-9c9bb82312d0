
import React from 'react';
import { toast } from 'sonner';
import OrganizationSettingsContent from '@/components/settings/organization/OrganizationSettingsContent';
import { useOrganizationTabState } from '@/hooks/settings/useOrganizationTabState';
import { useStore } from '@/contexts';

const OrganizationTab = () => {
  const { currentStore, updateStore } = useStore();
  const [loading, setLoading] = React.useState(false);
  
  const organizationState = useOrganizationTabState();
  
  const handleOrganizationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      // Update organization information
      await updateStore(currentStore?.id || '', {
        name: organizationState.companyName,
        description: `${organizationState.businessType} - ${organizationState.registrationNumber}`
      });
      
      toast.success('Organization settings updated successfully!');
    } catch (error) {
      toast.error('Failed to update organization settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <OrganizationSettingsContent
      {...organizationState}
      loading={loading}
      onSubmit={handleOrganizationSubmit}
    />
  );
};

export default OrganizationTab;
