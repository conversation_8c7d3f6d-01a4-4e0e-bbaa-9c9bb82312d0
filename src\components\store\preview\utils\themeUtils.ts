
import { cn } from '@/lib/utils';

// Map color scheme to actual colors
export const getColorClasses = (colorScheme: string) => {
  switch (colorScheme) {
    case 'blue':
      return { primary: 'bg-blue-500', secondary: 'bg-blue-100', text: 'text-blue-500' };
    case 'purple':
      return { primary: 'bg-purple-500', secondary: 'bg-purple-100', text: 'text-purple-500' };
    case 'amber':
      return { primary: 'bg-amber-500', secondary: 'bg-amber-100', text: 'text-amber-500' };
    case 'red':
      return { primary: 'bg-red-500', secondary: 'bg-red-100', text: 'text-red-500' };
    default: // green
      return { primary: 'bg-green-500', secondary: 'bg-green-100', text: 'text-green-500' };
  }
};

// Map button style to classes
export const getButtonClasses = (buttonStyle: string, cornerRadius: number) => {
  switch (buttonStyle) {
    case 'square':
      return 'rounded-none';
    case 'pill':
      return 'rounded-full';
    default: // rounded
      return `rounded-[${cornerRadius}px]`;
  }
};

// Get responsive container classes based on the selected view mode
export const getContainerClasses = (viewMode: 'desktop' | 'tablet' | 'mobile') => {
  switch (viewMode) {
    case 'mobile':
      return 'w-full max-w-[375px]';
    case 'tablet':
      return 'w-full max-w-[768px]';
    default: // desktop
      return 'w-full max-w-[1200px]';
  }
};

// Get grid layout classes
export const getLayoutClasses = (layoutType: string, productsPerRow: number) => {
  if (layoutType === 'list') {
    return 'flex flex-col gap-4';
  } else if (layoutType === 'masonry') {
    return `grid grid-cols-${productsPerRow} gap-4 auto-rows-max`;
  } else { // grid (default)
    return `grid grid-cols-1 sm:grid-cols-2 md:grid-cols-${Math.min(productsPerRow, 4)} gap-4`;
  }
};

// Get image style classes
export const getImageClasses = (productImageStyle: string) => {
  return productImageStyle === 'cover' ? 'object-cover' : 'object-contain';
};
