
import { 
  MessageCircle, 
  ShoppingCart, 
  Users, 
  Truck, 
  Calendar, 
  Bell, 
  Package, 
  RefreshCw, 
  AlertTriangle, 
  CheckSquare, 
  BarChart3, 
  Store 
} from 'lucide-react';

export interface WorkflowTypeData {
  icon: typeof MessageCircle;
  title: string;
  description: string;
  expectedDate: string;
}

export const workflowTypeData: Record<string, WorkflowTypeData> = {
  whatsapp: {
    icon: MessageCircle,
    title: 'WhatsApp Automations',
    description: 'Configure automated WhatsApp messages for various events',
    expectedDate: 'Available now'
  },
  orders: {
    icon: ShoppingCart,
    title: 'Order Notifications',
    description: 'Automate messages for order status changes',
    expectedDate: 'Available now'
  },
  customers: {
    icon: Users,
    title: 'Customer Journey',
    description: 'Create automated workflows for the customer lifecycle',
    expectedDate: 'Q3 2023'
  },
  delivery: {
    icon: Truck,
    title: 'Delivery Updates',
    description: 'Automated shipping and delivery notifications',
    expectedDate: 'Q3 2023'
  },
  inventory: {
    icon: Package,
    title: 'Inventory Alerts',
    description: 'Low stock warnings and product notifications',
    expectedDate: 'Q4 2023'
  },
  returns: {
    icon: RefreshCw,
    title: 'Returns & Refunds',
    description: 'Streamline return request and approval workflows',
    expectedDate: 'Q4 2023'
  },
  staff: {
    icon: AlertTriangle,
    title: 'Staff Alerts',
    description: 'Internal team notifications and escalations',
    expectedDate: 'Q1 2024'
  },
  events: {
    icon: Bell,
    title: 'Event Triggers',
    description: 'Custom events and notification rules',
    expectedDate: 'Q1 2024'
  },
  approvals: {
    icon: CheckSquare,
    title: 'Approval Workflows',
    description: 'Multi-step approval processes',
    expectedDate: 'Q1 2024'
  },
  reports: {
    icon: BarChart3,
    title: 'Performance Reports',
    description: 'Scheduled reports and analytics',
    expectedDate: 'Q2 2024'
  },
  scheduled: {
    icon: Calendar,
    title: 'Scheduled Messages',
    description: 'Timed and recurring communications',
    expectedDate: 'Q2 2024'
  },
  'store-events': {
    icon: Store,
    title: 'Store Events',
    description: 'Sales, promotions and special events',
    expectedDate: 'Q2 2024'
  }
};

export default workflowTypeData;
