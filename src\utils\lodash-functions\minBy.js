
/**
 * Returns the minimum value of an array by a given iteratee.
 *
 * @param {Array} array - The array to iterate over
 * @param {Function|string} iteratee - The function/property invoked per iteration
 * @returns {*} Returns the minimum value
 */
function minBy(array, iteratee) {
  if (!array || !array.length) return undefined;
  
  const isIterateeFunction = typeof iteratee === 'function';
  
  let minValue = array[0];
  let minComputed = isIterateeFunction 
    ? iteratee(minValue) 
    : (minValue != null && minValue[iteratee] !== undefined ? minValue[iteratee] : minValue);
  
  for (let i = 1; i < array.length; i++) {
    const value = array[i];
    const computed = isIterateeFunction 
      ? iteratee(value) 
      : (value != null && value[iteratee] !== undefined ? value[iteratee] : value);
    
    if (computed < minComputed) {
      minValue = value;
      minComputed = computed;
    }
  }
  
  return minValue;
}

// Support both ESM and CJS
export default minBy;
