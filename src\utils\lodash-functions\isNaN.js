
/**
 * Checks if a value is NaN.
 *
 * @param {*} value - The value to check
 * @returns {boolean} Returns true if value is NaN, else false
 */
function isNaN(value) {
  // Use the native isNaN but handle its quirks
  // Native isNaN coerces non-numbers to numbers and returns true for non-numeric strings
  // lodash's isNaN checks if the value is the numeric value NaN
  return typeof value === 'number' && value !== value;
}

// Support both ESM and CJS
export default isNaN;
