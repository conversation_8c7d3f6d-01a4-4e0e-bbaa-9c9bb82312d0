#!/bin/bash

# Script to set up Vercel environment variables for m-duka-app
# This helps ensure proper deployment to https://m-duka-app.vercel.app/

# Colors for better readability
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}===== M-Duka App Vercel Environment Setup =====${NC}"
echo ""

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo -e "${RED}Vercel CLI not found. Please install it first:${NC}"
    echo "npm install -g vercel"
    exit 1
fi

echo -e "${BLUE}This script will help you set up the required environment variables for your Vercel deployment.${NC}"
echo ""

# Ask for confirmation before proceeding
read -p "Do you want to proceed with setting up Vercel environment variables? (y/n): " -n 1 -r
echo ""
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${RED}Setup cancelled.${NC}"
    exit 1
fi

# Check if user is logged in to Vercel
echo -e "${BLUE}Checking Vercel login status...${NC}"
vercel whoami &> /dev/null
VERCEL_LOGIN_STATUS=$?

if [ $VERCEL_LOGIN_STATUS -ne 0 ]; then
    echo -e "${RED}You are not logged in to Vercel. Please login:${NC}"
    vercel login
fi

echo ""
echo -e "${BLUE}Setting up environment variables for m-duka-app...${NC}"
echo ""

# Set up the pre-configured Supabase values
SUPABASE_URL="https://nheycjpozywomwscplcz.supabase.co"
SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g"


# Set environment variables in Vercel
echo -e "${BLUE}Setting VITE_PUBLIC_SUPABASE_URL...${NC}"
vercel env add VITE_PUBLIC_SUPABASE_URL production <<< "$SUPABASE_URL"
vercel env add VITE_PUBLIC_SUPABASE_URL preview <<< "$SUPABASE_URL"
vercel env add VITE_PUBLIC_SUPABASE_URL development <<< "$SUPABASE_URL"

echo -e "${BLUE}Setting VITE_PUBLIC_SUPABASE_ANON_KEY...${NC}"
vercel env add VITE_PUBLIC_SUPABASE_ANON_KEY production <<< "$SUPABASE_ANON_KEY"
vercel env add VITE_PUBLIC_SUPABASE_ANON_KEY preview <<< "$SUPABASE_ANON_KEY"
vercel env add VITE_PUBLIC_SUPABASE_ANON_KEY development <<< "$SUPABASE_ANON_KEY"

echo ""
echo -e "${GREEN}Environment variables have been set successfully!${NC}"
echo ""
echo -e "${BLUE}Now redeploy your application with:${NC}"
echo "vercel --prod"
echo ""
echo -e "${BLUE}After deployment, your app should be accessible at:${NC} https://m-duka-app.vercel.app/"
echo ""
echo -e "${BLUE}Important Notes:${NC}"
echo "- Add your Vercel domain to Supabase Auth URL configuration"
echo "- Test the authentication flow after deployment"