
import React from "react";
import { Container } from "@/components/ui/container";
import { MapPin, Info, Search, Home, ShoppingCart, Menu } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";
import { useUIStore } from "@/hooks/useUIStore";
import { Link, useNavigate } from "react-router-dom";
import { isStoreSubdomain } from "@/utils/authRedirects";

interface StoreHeaderProps {
  name?: string;
  description?: string;
  address?: string;
  location?: string;
  about?: string;
}

const StorefrontHeader: React.FC<StoreHeaderProps> = ({ 
  name, 
  description, 
  address, 
  location,
  about
}) => {
  const isMobile = useIsMobile();
  const { currentStore } = useUIStore();
  const navigate = useNavigate();
  const hostname = window.location.hostname;
  
  console.log("Rendering StorefrontHeader", { 
    currentStore,
    hostname
  });
  
  // If props are not provided, use data from currentStore
  const storeName = name || currentStore?.name || "Store Name";
  const storeDescription = description || currentStore?.description || "";
  
  // Handle both camelCase and snake_case property names
  const storeAddress = address || currentStore?.address || "";
  const storeLocation = location || currentStore?.location || "";
  const storeAbout = about || currentStore?.about_text || "";
  
  // Determine correct base path based on domain context
  const isSubdomain = isStoreSubdomain();
  // Always force use basePath for preview--m-duka-app
  const forceBasePath = hostname.includes('preview--') && hostname.includes('m-duka-app');
  const basePath = isSubdomain && !forceBasePath ? "" : "/shop";
  
  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const searchInput = (e.target as HTMLFormElement).elements.namedItem('search') as HTMLInputElement;
    if (searchInput?.value) {
      navigate(`${basePath}/search?q=${encodeURIComponent(searchInput.value)}`);
    }
  };
  
  return (
    <div className="bg-white shadow-sm">
      {/* Store Name / Logo */}
      <div className="py-3 border-b">
        <Container>
          <div className="flex justify-center">
            <Link to={basePath || "/"}>
              <h1 className="text-2xl font-bold text-center">{storeName}</h1>
            </Link>
          </div>
        </Container>
      </div>
      
      {/* Location */}
      {(storeLocation || storeAddress) && (
        <div className="py-2 border-b bg-gray-50">
          <Container>
            <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
              <MapPin className="h-3.5 w-3.5" />
              <span>{storeLocation}{storeLocation && storeAddress ? ", " : ""}{storeAddress}</span>
            </div>
          </Container>
        </div>
      )}
      
      {/* About Us */}
      {storeAbout && (
        <div className="py-3 border-b">
          <Container>
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <p className="text-sm text-gray-600 line-clamp-2">{storeAbout}</p>
            </div>
          </Container>
        </div>
      )}
      
      {/* Search */}
      <div className="py-3 border-b">
        <Container>
          <form onSubmit={handleSearchSubmit}>
            <div className="relative">
              <Input 
                name="search"
                type="search" 
                placeholder="Search products..." 
                className="w-full pl-9 pr-4 py-2 rounded-lg"
              />
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            </div>
          </form>
        </Container>
      </div>
      
      {/* Only show navigation on mobile */}
      {isMobile && (
        <div className="py-3">
          <Container>
            <div className="flex justify-around">
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1" asChild>
                <Link to={basePath || "/"}>
                  <Home className="h-5 w-5" />
                  <span className="text-xs">Home</span>
                </Link>
              </Button>
              
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1" asChild>
                <Link to={`${basePath}/search`}>
                  <Search className="h-5 w-5" />
                  <span className="text-xs">Search</span>
                </Link>
              </Button>
              
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1" asChild>
                <Link to={`${basePath}/cart`}>
                  <ShoppingCart className="h-5 w-5" />
                  <span className="text-xs">Cart</span>
                </Link>
              </Button>
              
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1" asChild>
                <Link to={`${basePath}/categories`}>
                  <Menu className="h-5 w-5" />
                  <span className="text-xs">Menu</span>
                </Link>
              </Button>
            </div>
          </Container>
        </div>
      )}
    </div>
  );
};

export default StorefrontHeader;
