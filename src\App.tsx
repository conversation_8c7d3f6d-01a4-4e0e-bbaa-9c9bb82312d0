
import React from 'react';
import { RouterProvider } from 'react-router-dom';
import { router } from '@/routes';
import { AppProviders } from '@/components/providers/AppProviders';
import ErrorBoundary from '@/components/ErrorBoundary';
import { logger } from '@/utils/logger';
import './App.css';

const App: React.FC = () => {
  // Log app initialization
  React.useEffect(() => {
    logger.info('M-Duka App initialized', {
      version: '1.0.0',
      environment: import.meta.env.MODE,
      timestamp: new Date().toISOString()
    });
  }, []);

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        logger.error('App-level error caught', {
          error: error.message,
          stack: error.stack,
          componentStack: errorInfo.componentStack
        });
      }}
    >
      <AppProviders>
        <RouterProvider router={router} />
      </AppProviders>
    </ErrorBoundary>
  );
};

export default App;
