import { useState, useEffect } from 'react';
import { Store } from '@/types/store';
import { fetchUserStores } from '../api/fetchStores';
import { toast } from 'sonner';

type UseStoreLoadingProps = {
  user: any;
  isAuthenticated: boolean;
  setStores: React.Dispatch<React.SetStateAction<Store[]>>;
  setCurrentStore: React.Dispatch<React.SetStateAction<Store | null>>;
};

export const useStoreLoading = ({
  user,
  isAuthenticated,
  setStores,
  setCurrentStore
}: UseStoreLoadingProps) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    const loadStores = async () => {
      if (!isAuthenticated || !user) {
        setStores([]);
        setCurrentStore(null);
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const storeData = await fetchUserStores(user.id);

        // Filter out ALL mock stores
        const realStores = storeData.filter(
          s => !s.id.startsWith("mock-store") && /^[0-9a-fA-F-]{36}$/.test(s.id)
        );

        setStores(realStores);

        // Only set currentStore if at least one real store exists
        if (realStores.length > 0) {
          setCurrentStore(realStores[0]);
          localStorage.setItem('m-duka-current-store', realStores[0].id || '');
        } else {
          setCurrentStore(null);
          localStorage.removeItem('m-duka-current-store');
        }
      } catch (error) {
        console.error('Error loading stores:', error);
        setStores([]);
        setCurrentStore(null);
        setIsLoading(false);
      } finally {
        setIsLoading(false);
      }
    };

    loadStores();
  }, [user, isAuthenticated, setStores, setCurrentStore]);

  return { isLoading };
};
