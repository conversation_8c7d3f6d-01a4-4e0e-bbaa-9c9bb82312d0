
import React from 'react';
import { ExternalLink } from 'lucide-react';

interface DefaultDomainProps {
  storeUrl: string | null;
}

export const DefaultDomain: React.FC<DefaultDomainProps> = ({ storeUrl }) => {
  const getDefaultStoreUrl = () => {
    if (!storeUrl) return null;
    return `${storeUrl}.m-duka.app`;
  };

  return (
    <div className="bg-muted p-4 rounded-md border mb-6">
      <h3 className="font-medium mb-2 flex items-center justify-between">
        <span>Default Domain</span>
        {storeUrl && (
          <a 
            href={`https://${getDefaultStoreUrl()}`} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-green-500 hover:text-green-600 flex items-center text-sm font-normal"
          >
            <span>View Store</span>
            <ExternalLink className="ml-1 h-3 w-3" />
          </a>
        )}
      </h3>
      <div className="flex items-center justify-between">
        <span className="font-mono text-sm">
          {getDefaultStoreUrl() || 'No store URL configured'}
        </span>
        {storeUrl && (
          <a 
            href={`https://${getDefaultStoreUrl()}`}
            target="_blank"
            rel="noopener noreferrer"
            className="bg-green-500 text-white p-1 rounded hover:bg-green-600"
          >
            <ExternalLink className="h-4 w-4" />
          </a>
        )}
      </div>
      {!storeUrl && (
        <p className="text-xs text-amber-600 mt-2">
          To set up your default domain, go to General Settings and configure your store URL.
        </p>
      )}
    </div>
  );
};
