import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [isProcessing, setIsProcessing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        console.log('Processing auth callback...');
        
        // Check for error parameters in URL
        const errorParam = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        
        if (errorParam) {
          console.error('OAuth error:', errorParam, errorDescription);
          setError(errorDescription || errorParam);
          setIsProcessing(false);
          
          // Show user-friendly error message
          if (errorParam === 'server_error' && errorDescription?.includes('Unable to exchange external code')) {
            toast.error('Google sign-in failed. Please try again or contact support.');
          } else {
            toast.error('Authentication failed. Please try again.');
          }
          
          // Redirect to sign-in page after a delay
          setTimeout(() => {
            navigate('/signin', { replace: true });
          }, 3000);
          return;
        }

        // Handle successful OAuth callback
        const { data, error: sessionError } = await supabase.auth.getSession();
        
        if (sessionError) {
          console.error('Session error:', sessionError);
          setError(sessionError.message);
          toast.error('Failed to establish session. Please try signing in again.');
          setTimeout(() => {
            navigate('/signin', { replace: true });
          }, 2000);
          return;
        }

        if (data.session) {
          console.log('Auth callback successful, user authenticated');
          toast.success('Successfully signed in!');
          
          // Redirect to dashboard
          navigate('/dashboard', { replace: true });
        } else {
          console.log('No session found, redirecting to sign-in');
          navigate('/signin', { replace: true });
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        setError('An unexpected error occurred during authentication.');
        toast.error('Authentication failed. Please try again.');
        setTimeout(() => {
          navigate('/signin', { replace: true });
        }, 2000);
      } finally {
        setIsProcessing(false);
      }
    };

    handleAuthCallback();
  }, [navigate, searchParams]);

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="text-red-500 mb-4">
            <svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Authentication Failed</h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <p className="text-sm text-gray-500">Redirecting to sign-in page...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
        <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          {isProcessing ? 'Completing sign-in...' : 'Redirecting...'}
        </h2>
        <p className="text-gray-600">Please wait while we complete your authentication.</p>
      </div>
    </div>
  );
};

export default AuthCallback;
