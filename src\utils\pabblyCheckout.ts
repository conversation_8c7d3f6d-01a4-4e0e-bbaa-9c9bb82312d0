
declare global {
  interface Window {
    PabblyCheckout?: {
      checkout: (options: { embeded: string }) => void;
    };
    Pabbly?: {
      open: (options: { type: string; plan_id: string }) => void;
    };
  }
}

// Track loading state
let isScriptLoading = false;
let isScriptLoaded = false;

/**
 * Loads the Pabbly checkout script if it's not already loaded
 * @returns A promise that resolves when the script is loaded or rejects if it fails
 */
export const loadPabblyScript = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    // If already loaded, resolve immediately
    if (isScriptLoaded) {
      resolve();
      return;
    }
    
    // If currently loading, wait for it
    if (isScriptLoading) {
      const checkInterval = setInterval(() => {
        if (isScriptLoaded) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
      return;
    }
    
    // Start loading
    isScriptLoading = true;
    
    // Create both script elements - we need both popup and embed scripts
    const embedScript = document.createElement('script');
    embedScript.src = 'https://payments.pabbly.com/api/checkout/embed.js';
    embedScript.async = true;
    
    const popupScript = document.createElement('script');
    popupScript.src = 'https://payments.pabbly.com/api/checkout/popup-checkout.js';
    popupScript.async = true;
    
    // Handle load errors
    const handleError = () => {
      isScriptLoading = false;
      reject(new Error('Failed to load Pabbly scripts'));
    };
    
    embedScript.onerror = handleError;
    popupScript.onerror = handleError;
    
    // Set up the completion handler for when both scripts are loaded
    let scriptsLoaded = 0;
    const handleLoad = () => {
      scriptsLoaded++;
      if (scriptsLoaded === 2) {
        isScriptLoading = false;
        isScriptLoaded = true;
        resolve();
      }
    };
    
    embedScript.onload = handleLoad;
    popupScript.onload = handleLoad;
    
    // Add scripts to document
    document.body.appendChild(embedScript);
    document.body.appendChild(popupScript);
    
    // Set a timeout to prevent hanging if scripts don't load
    setTimeout(() => {
      if (!isScriptLoaded) {
        isScriptLoading = false;
        reject(new Error('Timed out loading Pabbly scripts'));
      }
    }, 10000); // 10 seconds timeout
  });
};

/**
 * Initiates the Pabbly checkout process for embedded checkout
 */
export const initiatePabblyCheckout = async (planId: string): Promise<void> => {
  try {
    await loadPabblyScript();
    
    if (window.PabblyCheckout) {
      window.PabblyCheckout.checkout({ embeded: planId });
    } else {
      console.error('Pabbly Checkout not initialized');
      throw new Error('Unable to load payment popup');
    }
  } catch (error) {
    console.error('Error initiating Pabbly checkout:', error);
    throw error;
  }
};

/**
 * Opens the Pabbly popup checkout
 */
export const openPabblyPopup = async (planId: string): Promise<void> => {
  try {
    await loadPabblyScript();
    
    if (window.Pabbly) {
      window.Pabbly.open({
        type: "popup",
        plan_id: planId
      });
    } else {
      console.error('Pabbly popup not initialized');
      throw new Error('Unable to load payment popup');
    }
  } catch (error) {
    console.error('Error opening Pabbly popup:', error);
    throw error;
  }
};
