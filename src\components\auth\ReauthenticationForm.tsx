
import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, ShieldCheck } from "lucide-react";
import { useAuth } from "@/contexts";

const formSchema = z.object({
  password: z.string().min(8, { message: "Password must be at least 8 characters" }),
});

type FormValues = z.infer<typeof formSchema>;

interface ReauthenticationFormProps {
  onSuccess: () => void;
  description?: string;
}

const ReauthenticationForm: React.FC<ReauthenticationFormProps> = ({ 
  onSuccess,
  description = "For security reasons, please verify your identity by entering your password.",
}) => {
  const { reauthenticate } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      password: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (isSubmitting) return;
    
    setFormError(null);
    setIsSubmitting(true);
    
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Verifying your identity...");
      
      const { success, error } = await reauthenticate(data.password);
      
      if (toastId) toast.dismiss(toastId);
      
      if (!success) {
        throw error;
      }
      
      toast.success("Identity verified successfully!");
      onSuccess();
    } catch (error: any) {
      console.error("Reauthentication error:", error);
      if (toastId) toast.dismiss(toastId);
      setFormError(error.message || "Failed to verify identity");
      toast.error(error.message || "Failed to verify identity");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <div className="flex flex-col items-center justify-center mb-6">
        <div className="w-12 h-12 bg-blue-50 rounded-full flex items-center justify-center mb-4">
          <ShieldCheck className="h-6 w-6 text-blue-600" />
        </div>
        <h3 className="text-lg font-medium">Verify Your Identity</h3>
        <p className="text-sm text-muted-foreground text-center mt-1">
          {description}
        </p>
      </div>
      
      {formError && (
        <Alert variant="destructive" className="mb-4">
          <AlertDescription>{formError}</AlertDescription>
        </Alert>
      )}
      
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Your Password</FormLabel>
                <FormControl>
                  <Input 
                    type="password" 
                    placeholder="********" 
                    {...field} 
                    disabled={isSubmitting}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button 
            type="submit" 
            className="w-full" 
            disabled={isSubmitting}
          >
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSubmitting ? "Verifying..." : "Verify Identity"}
          </Button>
        </form>
      </Form>
    </>
  );
};

export default ReauthenticationForm;
