
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { useStoreWizard } from './wizard/StoreWizardContext';
import WizardNavigation from './wizard/WizardNavigation';

export const StoreWizard: React.FC = () => {
  const { renderStep } = useStoreWizard();
  
  return (
    <div className="w-full">
      <Card className="mb-4">
        <CardContent className="pt-6">
          {renderStep()}
          <WizardNavigation />
        </CardContent>
      </Card>
    </div>
  );
};

export default StoreWizard;
