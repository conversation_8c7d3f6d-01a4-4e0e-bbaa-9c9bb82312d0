
import React from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
} from '@/components/ui/table';

import StoreTableHeader from './stores/StoreTableHeader';
import StoreTableSkeleton from './stores/StoreTableSkeleton';
import StoreTableRow from './stores/StoreTableRow';
import StoreSearchBar from './stores/StoreSearchBar';
import StoreEmptyState from './stores/StoreEmptyState';
import { useStores } from './stores/hooks/useStores';

const AdminStoresTab: React.FC = () => {
  const {
    stores,
    isLoading,
    searchQuery,
    setSearchQuery,
    formatDate,
    handleStoreAction
  } = useStores();
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Store Management</CardTitle>
        <CardDescription>
          View and manage all stores on your platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex justify-between items-center mb-4">
          <StoreSearchBar 
            searchQuery={searchQuery} 
            setSearchQuery={setSearchQuery} 
          />
        </div>

        <div className="rounded-md border">
          <Table>
            <StoreTableHeader />
            <TableBody>
              {isLoading ? (
                <StoreTableSkeleton />
              ) : stores.length > 0 ? (
                stores.map((store) => (
                  <StoreTableRow 
                    key={store.id}
                    store={store} 
                    onStoreAction={handleStoreAction}
                    formatDate={formatDate}
                  />
                ))
              ) : (
                <StoreEmptyState searchQuery={searchQuery} colSpan={8} />
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminStoresTab;
