import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { motion } from "framer-motion";
import { ArrowRight, Smartphone, MessageSquare, ShoppingBag, CreditCard, Check } from "lucide-react";
import { formatCurrency } from "@/utils/formatters";
const HeroText = () => {
  return <div className="flex flex-col gap-6">
      <motion.div initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      duration: 0.5
    }} className="inline-flex items-center rounded-full bg-gradient-to-r from-green-100 to-green-50 px-4 py-1.5 text-sm font-medium text-green-700 shadow-sm border border-green-200/50">
        <span className="flex h-2 w-2 rounded-full bg-green-500 mr-1.5 animate-pulse"></span>
        <span>Transform Your WhatsApp into a Mobile Shop</span>
      </motion.div>
      
      <motion.h1 initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      duration: 0.5,
      delay: 0.1
    }} className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight text-balance relative z-20">
        Turn Your WhatsApp into a <span className="text-gradient relative">
          Fully-Functional
          <span className="absolute -bottom-1 left-0 w-full h-1 bg-gradient-to-r from-green-400 to-blue-500 rounded-full"></span>
        </span> Store
      </motion.h1>
      
      <motion.p initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      duration: 0.5,
      delay: 0.2
    }} className="text-xl text-muted-foreground max-w-lg text-pretty text-center mx-auto">M-Duka empowers  entrepreneurs to transform their WhatsApp into a professional mobile shop, enabling you to showcase products, receive orders, and deliver with ease across the world</motion.p>
      
      <motion.div initial={{
      opacity: 0,
      y: 20
    }} animate={{
      opacity: 1,
      y: 0
    }} transition={{
      duration: 0.5,
      delay: 0.3
    }} className="flex flex-col sm:flex-row gap-4 mt-4">
        <Button size="lg" className="bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white rounded-full h-12 px-8 text-base font-medium shadow-lg hover:shadow-xl transition-all group" asChild>
          <Link to="/signup" className="flex items-center gap-2">
            Start Your Free Store Today
            <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
          </Link>
        </Button>
        
        <Button variant="outline" size="lg" className="rounded-full h-12 px-8 text-base font-medium border-muted hover:bg-accent/50 transition-all backdrop-blur-sm" asChild>
          <a href="#how-it-works">See How It Works</a>
        </Button>
      </motion.div>
      
      <motion.div initial={{
      opacity: 0
    }} animate={{
      opacity: 1
    }} transition={{
      duration: 0.5,
      delay: 0.4
    }} className="flex flex-col sm:flex-row flex-wrap justify-center gap-4 text-sm text-muted-foreground mt-4">
        {[{
        icon: <Smartphone className="h-4 w-4 text-green-500" />,
        text: "No technical skills required"
      }, {
        icon: <MessageSquare className="h-4 w-4 text-green-500" />,
        text: "Works with your WhatsApp"
      }, {
        icon: <ShoppingBag className="h-4 w-4 text-green-500" />,
        text: "Unlimited products"
      }, {
        icon: <CreditCard className="h-4 w-4 text-green-500" />,
        text: "Multiple payment methods"
      }].map((item, index) => <div key={index} className="flex items-center gap-2 bg-white/50 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm">
            {item.icon}
            <span>{item.text}</span>
          </div>)}
      </motion.div>
      
      {/* Social Proof */}
      <motion.div initial={{
      opacity: 0
    }} animate={{
      opacity: 1
    }} transition={{
      duration: 0.5,
      delay: 0.5
    }} className="mt-6 bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg border border-gray-100 max-w-md mx-auto">
        <div className="flex items-center">
          <div className="flex -space-x-3 mr-4">
            {[1, 2, 3].map(i => <div key={i} className={`w-10 h-10 rounded-full ring-2 ring-white bg-gradient-to-br from-green-${i * 100} to-blue-${i * 100} flex items-center justify-center text-white font-medium text-xs`}>
                {String.fromCharCode(64 + i)}
              </div>)}
          </div>
          <div className="text-sm">
            <p className="font-medium">Trusted by businesses across Africa</p>
            <div className="flex items-center text-yellow-500 mt-1">
              {[1, 2, 3, 4, 5].map(i => <svg key={i} className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>)}
              <span className="ml-1 text-gray-600">4.9/5</span>
            </div>
          </div>
        </div>
      </motion.div>
    </div>;
};
export default HeroText;