
import React from 'react';
import PaymentMethodSection, { PaymentMethod } from './PaymentMethodSection';
import { Smartphone, Phone, Wallet } from 'lucide-react';

interface EastAfricanPaymentMethodsProps {
  onToggleMethod: (methodId: string, enabled: boolean) => void;
  isMethodEnabled: (methodId: string) => boolean;
}

const EastAfricanPaymentMethods: React.FC<EastAfricanPaymentMethodsProps> = ({
  onToggleMethod,
  isMethodEnabled
}) => {
  const paymentMethods: PaymentMethod[] = [
    {
      id: 'mpesa',
      name: 'M-Pes<PERSON>',
      description: 'Mobile money payment system popular in Kenya, Tanzania, and other East African countries.',
      icon: <Smartphone className="h-5 w-5 text-green-600" />,
      region: 'East Africa',
      popular: true,
      fields: [
        {
          id: 'consumer-key',
          name: 'Consumer Key',
          placeholder: 'Enter your M-Pesa consumer key',
          type: 'text',
          required: true
        },
        {
          id: 'consumer-secret',
          name: 'Consumer Secret',
          placeholder: 'Enter your M-Pesa consumer secret',
          type: 'password',
          required: true
        },
        {
          id: 'shortcode',
          name: 'Shortcode',
          placeholder: 'Enter your business shortcode',
          type: 'text',
          required: true
        },
        {
          id: 'passkey',
          name: 'Passkey',
          placeholder: 'Enter your passkey',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://developer.safaricom.co.ke/'
    },
    {
      id: 'airtel-money',
      name: 'Airtel Money',
      description: 'Mobile money service from Airtel available across East Africa.',
      icon: <Phone className="h-5 w-5 text-red-600" />,
      region: 'East Africa',
      fields: [
        {
          id: 'merchant-id',
          name: 'Merchant ID',
          placeholder: 'Enter your Airtel Money merchant ID',
          type: 'text',
          required: true
        },
        {
          id: 'api-key',
          name: 'API Key',
          placeholder: 'Enter your Airtel Money API key',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://www.airtel.co.ke/airtel-money/merchant'
    },
    {
      id: 'tigo-pesa',
      name: 'Tigo Pesa',
      description: 'Mobile money service by Tigo available in Tanzania and other East African countries.',
      icon: <Wallet className="h-5 w-5 text-blue-600" />,
      region: 'East Africa',
      fields: [
        {
          id: 'merchant-id',
          name: 'Merchant ID',
          placeholder: 'Enter your Tigo Pesa merchant ID',
          type: 'text',
          required: true
        },
        {
          id: 'api-key',
          name: 'API Key',
          placeholder: 'Enter your Tigo Pesa API key',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://www.tigo.co.tz/tigo-pesa'
    },
    {
      id: 'eazzy-pay',
      name: 'Eazzy Pay',
      description: 'Mobile payment solution from Equity Bank available in East Africa.',
      icon: <Smartphone className="h-5 w-5 text-purple-600" />,
      region: 'East Africa',
      fields: [
        {
          id: 'merchant-id',
          name: 'Merchant ID',
          placeholder: 'Enter your Eazzy Pay merchant ID',
          type: 'text',
          required: true
        },
        {
          id: 'api-key',
          name: 'API Key',
          placeholder: 'Enter your Eazzy Pay API key',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://equitygroupholdings.com/ke/business/eazzy-pay'
    }
  ];

  return (
    <div className="space-y-5">
      {paymentMethods.map(method => (
        <PaymentMethodSection
          key={method.id}
          method={method}
          enabled={isMethodEnabled(method.id)}
          onToggle={onToggleMethod}
        />
      ))}
    </div>
  );
};

export default EastAfricanPaymentMethods;
