
import React, { useState, useEffect } from "react";
import { Container } from "@/components/ui/container";
import { useProduct } from "@/contexts";
import { Heading } from "@/components/ui/heading";
import { ShoppingBag, SlidersHorizontal, ChevronDown, Grid3X3, List } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Separator } from "@/components/ui/separator";
import EnhancedProductCard from "@/components/storefront/product-card/EnhancedProductCard";
import { Product } from "@/types/unified-product";

const FeaturedProducts: React.FC = () => {
  const { products, isLoading } = useProduct();
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortOption, setSortOption] = useState("featured");
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  
  // Get featured products (products with the "featured" tag)
  useEffect(() => {
    // Filter active products with stock
    const availableProducts = products.filter(p => p.is_active && (p.stock_quantity || 0) > 0);
    
    // Get products tagged as featured
    const featuredItems = availableProducts.filter(p => p.tags?.includes('featured'));
    
    // If no featured products, show some products anyway
    const itemsToShow = featuredItems.length > 0 ? featuredItems : availableProducts.slice(0, 8);
    
    // Apply sorting
    let sortedItems = [...itemsToShow];
    
    switch(sortOption) {
      case "price-asc":
        sortedItems.sort((a, b) => (a.price || 0) - (b.price || 0));
        break;
      case "price-desc":
        sortedItems.sort((a, b) => (b.price || 0) - (a.price || 0));
        break;
      case "newest":
        sortedItems.sort((a, b) => {
          const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
          const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
          return dateB - dateA;
        });
        break;
      // "featured" is default, no additional sorting needed
    }
    
    setFilteredProducts(sortedItems);
  }, [products, sortOption]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <Container>
          <div className="space-y-8">
            <div className="w-1/3 h-8 bg-gray-200 rounded-md animate-pulse"></div>
            <div className="w-2/3 h-6 bg-gray-200 rounded-md animate-pulse"></div>
            
            <div className="h-12 bg-gray-200 rounded-md animate-pulse"></div>
            
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="bg-white rounded-lg overflow-hidden shadow-sm h-[300px] animate-pulse">
                  <div className="h-[180px] bg-gray-200"></div>
                  <div className="p-4 space-y-3">
                    <div className="h-5 bg-gray-200 rounded w-2/3"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                    <div className="h-8 bg-gray-200 rounded w-full"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </div>
    );
  }

  // Render empty state if no products
  if (filteredProducts.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <Container>
          <div className="text-center py-16">
            <ShoppingBag className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h2 className="text-2xl font-bold mb-2">No Featured Products</h2>
            <p className="text-gray-500 mb-8">We don't have any featured products at the moment.</p>
            <Button onClick={() => window.location.href = "/shop"}>
              Browse All Products
            </Button>
          </div>
        </Container>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 animate-fade-in">
      <Container>
        <div className="space-y-8">
          {/* Header */}
          <div className="space-y-2">
            <Heading 
              title="Featured Products" 
              description="Discover our handpicked selection of featured products"
              icon={<ShoppingBag className="h-5 w-5 text-purple-500" />}
              className="animate-slide-up"
            />
          </div>
          
          {/* Filter Bar */}
          <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-100 animate-slide-up" style={{ animationDelay: "100ms" }}>
            <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
              <div className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Filters:</span>
                
                <Button variant="outline" size="sm" className="hidden md:flex items-center text-sm">
                  In Stock
                  <ChevronDown className="ml-1 h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center justify-between md:justify-end gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-600">Sort by:</span>
                  <Select value={sortOption} onValueChange={setSortOption}>
                    <SelectTrigger className="w-[140px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="featured">Featured</SelectItem>
                      <SelectItem value="price-asc">Price: Low to High</SelectItem>
                      <SelectItem value="price-desc">Price: High to Low</SelectItem>
                      <SelectItem value="newest">Newest First</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <Separator orientation="vertical" className="h-6 hidden md:block" />
                
                <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as "grid" | "list")}>
                  <ToggleGroupItem value="grid" aria-label="Grid view">
                    <Grid3X3 className="h-4 w-4" />
                  </ToggleGroupItem>
                  <ToggleGroupItem value="list" aria-label="List view">
                    <List className="h-4 w-4" />
                  </ToggleGroupItem>
                </ToggleGroup>
              </div>
            </div>
          </div>
          
          {/* Products Grid */}
          <div className={`
            ${viewMode === "grid" 
              ? "grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6" 
              : "flex flex-col gap-4"}
          `}>
            {filteredProducts.map((product, index) => (
              <div 
                key={product.id} 
                className="animate-slide-up" 
                style={{ animationDelay: `${index * 50}ms` }}
              >
                <EnhancedProductCard 
                  product={product} 
                  viewMode={viewMode}
                />
              </div>
            ))}
          </div>
        </div>
      </Container>
    </div>
  );
};

export default FeaturedProducts;
