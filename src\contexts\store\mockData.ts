
import { Store } from '@/types/store';

export const MOCK_STORES_FOR_DEVELOPMENT = false;

export const MOCK_STORES: Store[] = [
  {
    id: 'mock-store-1',
    name: 'Mock Store',
    description: 'This is a mock store for development',
    store_url: 'mock-store',
    owner_id: '00000000-0000-4000-a000-000000000000', // Match the mock user ID
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    category: 'General',
    theme_id: 'default',
    store_type: 'physical',
    payment_methods: ['credit-card', 'mpesa'],
    notificationsEmail: '<EMAIL>',
    notifications: {
      email: true,
      sms: false
    },
    theme_options: {
      primaryColor: '#10b981',
      secondaryColor: '#f59e0b',
      darkMode: false,
      customHeader: false,
      customFooter: false,
      colorScheme: 'default'
    },
    theme: {
      primaryColor: '#10b981',
      secondaryColor: '#f59e0b',
    },
    is_active: true,
    logo_url: null,
    whatsapp_settings: {
      enabled: true,
      number: '+254712345678',
      message: 'Thank you for your order!',
      businessNumber: '+254712345678',
      enableOrderNotifications: true,
      enableCustomerUpdates: true,
      customMessage: '',
      autoReply: false
    },
    // Additional properties for UI compatibility
    phone: '+254712345678',
    email: '<EMAIL>',
    business_hours: ['Mon-Fri: 9am-5pm', 'Sat: 10am-4pm']
  }
];
