
import React from 'react';
import WorkflowSidebarHeader from './sidebar/WorkflowSidebarHeader';
import WorkflowNavItems from './sidebar/WorkflowNavItems';

const WorkflowSidebar: React.FC = () => {
  return (
    <aside className="w-64 border-r h-full overflow-y-auto">
      <WorkflowSidebarHeader />
      <div className="p-4">
        <WorkflowNavItems />
      </div>
    </aside>
  );
};

export default WorkflowSidebar;
