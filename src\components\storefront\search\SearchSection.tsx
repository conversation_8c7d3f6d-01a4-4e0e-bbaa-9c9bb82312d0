
import React from "react";
import { Container } from "@/components/ui/container";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search } from "lucide-react";
import { useNavigate } from "react-router-dom";

const SearchSection: React.FC = () => {
  const [query, setQuery] = React.useState("");
  const navigate = useNavigate();

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (query.trim()) {
      navigate(`/shop/search?q=${encodeURIComponent(query)}`);
    }
  };

  return (
    <div className="bg-white py-3 px-4 border-b">
      <Container>
        <form onSubmit={handleSearch} className="relative">
          <Input
            type="search"
            placeholder="Search products..."
            className="w-full pr-12"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
          <Button 
            type="submit"
            size="sm"
            variant="ghost"
            className="absolute right-0 top-0 h-full"
          >
            <Search className="h-4 w-4" />
          </Button>
        </form>
      </Container>
    </div>
  );
};

export default SearchSection;
