import React from 'react';
import { Helmet } from 'react-helmet-async';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, Heart, Star, ArrowLeft, Package } from 'lucide-react';
import { Link } from 'react-router-dom';

const DemoStore: React.FC = () => {
  const demoProducts = [
    {
      id: 1,
      name: "Premium Wireless Headphones",
      price: 4500,
      originalPrice: 6000,
      image: "/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png",
      rating: 4.8,
      reviews: 120,
      category: "Electronics",
      inStock: true
    },
    {
      id: 2,
      name: "Organic Coffee Beans",
      price: 800,
      originalPrice: 1200,
      image: "/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png",
      rating: 4.6,
      reviews: 85,
      category: "Food & Beverages",
      inStock: true
    },
    {
      id: 3,
      name: "Handcrafted Leather Wallet",
      price: 2500,
      originalPrice: 3500,
      image: "/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png",
      rating: 4.9,
      reviews: 67,
      category: "Fashion",
      inStock: false
    },
    {
      id: 4,
      name: "Smartphone Stand",
      price: 1200,
      originalPrice: 1800,
      image: "/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png",
      rating: 4.5,
      reviews: 234,
      category: "Accessories",
      inStock: true
    }
  ];

  const handleAddToCart = (productName: string) => {
    alert(`✅ "${productName}" added to cart!\n\nThis is a demo - in a real store, this would be added to your WhatsApp cart.`);
  };

  const handleWhatsAppOrder = (productName: string) => {
    const message = `Hi! I'm interested in ordering "${productName}". Can you please provide more details?`;
    const whatsappUrl = `https://wa.me/254700000000?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <>
      <Helmet>
        <title>Demo Store - Experience M-Duka</title>
      </Helmet>
      
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white shadow-sm">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <div className="flex items-center">
                <Button asChild variant="ghost" className="mr-4">
                  <Link to="/dashboard">
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Dashboard
                  </Link>
                </Button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900">Demo Electronics Store</h1>
                  <p className="text-sm text-gray-500">Experience M-Duka in action</p>
                </div>
              </div>
              <div className="flex items-center space-x-4">
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  ✨ Demo Mode
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Demo Banner */}
        <div className="bg-gradient-to-r from-blue-600 to-green-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="text-center">
              <h2 className="text-3xl font-bold mb-2">Welcome to the M-Duka Demo Store! 🛍️</h2>
              <p className="text-lg opacity-90 mb-4">
                This is how your customers will experience shopping through WhatsApp
              </p>
              <div className="flex flex-wrap justify-center gap-4 text-sm">
                <span className="bg-white/20 px-3 py-1 rounded-full">📱 WhatsApp Integration</span>
                <span className="bg-white/20 px-3 py-1 rounded-full">💳 M-Pesa Payments</span>
                <span className="bg-white/20 px-3 py-1 rounded-full">🚚 Order Tracking</span>
              </div>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="mb-8">
            <h3 className="text-2xl font-bold text-gray-900 mb-2">Featured Products</h3>
            <p className="text-gray-600">Try adding products to cart and see how the WhatsApp integration works</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {demoProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-lg transition-shadow">
                <CardHeader className="p-0">
                  <div className="relative">
                    <img 
                      src={product.image} 
                      alt={product.name}
                      className="w-full h-48 object-cover rounded-t-lg"
                    />
                    <div className="absolute top-2 right-2">
                      <Button variant="ghost" size="icon" className="bg-white/80 hover:bg-white">
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>
                    {product.originalPrice > product.price && (
                      <Badge className="absolute top-2 left-2 bg-red-500">
                        -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                      </Badge>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="p-4">
                  <div className="mb-2">
                    <Badge variant="outline" className="text-xs">
                      {product.category}
                    </Badge>
                  </div>
                  <CardTitle className="text-lg mb-2 line-clamp-2">{product.name}</CardTitle>
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="ml-1 text-sm font-medium">{product.rating}</span>
                    </div>
                    <span className="text-sm text-gray-500 ml-2">({product.reviews} reviews)</span>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-green-600">
                        KES {product.price.toLocaleString()}
                      </span>
                      {product.originalPrice > product.price && (
                        <span className="text-sm text-gray-500 line-through">
                          KES {product.originalPrice.toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <Button 
                      onClick={() => handleAddToCart(product.name)}
                      disabled={!product.inStock}
                      className="w-full bg-green-600 hover:bg-green-700"
                    >
                      <ShoppingCart className="h-4 w-4 mr-2" />
                      {product.inStock ? 'Add to Cart' : 'Out of Stock'}
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={() => handleWhatsAppOrder(product.name)}
                      className="w-full border-green-600 text-green-600 hover:bg-green-50"
                    >
                      💬 Order via WhatsApp
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Features Showcase */}
        <div className="bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <h3 className="text-2xl font-bold text-center mb-8">What You Just Experienced</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="p-3 bg-green-100 rounded-full w-fit mx-auto mb-4">
                  <Package className="h-8 w-8 text-green-600" />
                </div>
                <h4 className="text-xl font-semibold mb-2">Product Catalog</h4>
                <p className="text-gray-600">
                  Beautiful product displays with images, prices, and descriptions - all integrated with WhatsApp
                </p>
              </div>
              <div className="text-center">
                <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-4">
                  <ShoppingCart className="h-8 w-8 text-blue-600" />
                </div>
                <h4 className="text-xl font-semibold mb-2">Cart & Checkout</h4>
                <p className="text-gray-600">
                  Seamless shopping experience that connects directly to your WhatsApp Business account
                </p>
              </div>
              <div className="text-center">
                <div className="p-3 bg-purple-100 rounded-full w-fit mx-auto mb-4">
                  <Heart className="h-8 w-8 text-purple-600" />
                </div>
                <h4 className="text-xl font-semibold mb-2">Customer Experience</h4>
                <p className="text-gray-600">
                  Professional storefront that builds trust and makes shopping easy for your customers
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 text-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
            <h3 className="text-3xl font-bold mb-4">Ready to Create Your Own Store?</h3>
            <p className="text-lg mb-8 opacity-90">
              Set up your M-Duka store in under 3 minutes and start selling through WhatsApp today!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-white text-green-600 hover:bg-gray-100">
                <Link to="/create-store">
                  Create Your Store Now →
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white/10">
                <Link to="/dashboard">
                  Back to Dashboard
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default DemoStore; 