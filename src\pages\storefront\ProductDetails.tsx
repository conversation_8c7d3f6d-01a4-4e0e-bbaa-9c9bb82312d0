
import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { ChevronLeft } from "lucide-react";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { useProduct, useStore } from "@/contexts";
import ProductImageGallery from "@/components/storefront/product/ProductImageGallery";
import ProductInfo from "@/components/storefront/product/ProductInfo";
import ProductPurchaseActions from "@/components/storefront/product/ProductPurchaseActions";
import ProductShippingInfo from "@/components/storefront/product/ProductShippingInfo";
import ProductNotFound from "@/components/storefront/product/ProductNotFound";
import ProductDetailsLoading from "@/components/storefront/product/ProductDetailsLoading";
import WhatsAppContact from "@/components/storefront/WhatsAppContact";
import { databaseToUIStore } from "@/utils/typeConverters";
import { Store } from "@/types/store";

const ProductDetails: React.FC = () => {
  const { id: productId } = useParams<{ id: string }>();
  const { getProduct, isLoading } = useProduct();
  const { currentStore } = useStore();
  const navigate = useNavigate();
  
  const [product, setProduct] = useState<any>(null);
  
  useEffect(() => {
    if (productId) {
      const fetchProduct = async () => {
        const productData = await getProduct(productId);
        if (productData) {
          setProduct(productData);
        }
      };
      
      fetchProduct();
    }
  }, [productId, getProduct]);
  
  const uiStore = currentStore ? databaseToUIStore(currentStore) as Store : null;
  
  if (isLoading) {
    return <ProductDetailsLoading />;
  }
  
  if (!product) {
    return <ProductNotFound />;
  }
  
  return (
    <Container className="py-12">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/shop">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          {product.category && (
            <>
              <BreadcrumbItem>
                <BreadcrumbLink href={`/shop/category/${encodeURIComponent(product.category)}`}>
                  {product.category}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
            </>
          )}
          <BreadcrumbItem>
            <BreadcrumbPage>{product.name}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <ProductImageGallery 
          images={product.images || []} 
          productName={product.name} 
        />
        
        <div className="space-y-6">
          <ProductInfo product={product} />
          
          <div className="flex items-center gap-4 my-4">
            <WhatsAppContact 
              store={uiStore}
              productName={product.name}
            />
            <p className="text-sm text-muted-foreground">
              ← Order this product directly through WhatsApp
            </p>
          </div>
          
          <ProductPurchaseActions product={product} />
          <ProductShippingInfo />
        </div>
      </div>
    </Container>
  );
};

export default ProductDetails;
