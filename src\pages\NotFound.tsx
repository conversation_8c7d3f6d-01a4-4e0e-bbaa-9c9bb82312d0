
import { Link, useLocation } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { motion } from "framer-motion";
import { ArrowLeft, Home, ShoppingBag } from "lucide-react";

const NotFound = () => {
  const location = useLocation();
  const isShopPath = location.pathname.startsWith('/shop');
  const isLegalPath = location.pathname.match(/\/(terms|privacy|cookies)/); 
  
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-background to-accent/20 px-4">
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="text-center max-w-md"
      >
        <div className="mb-8 relative">
          <div className="text-9xl font-bold text-mduka-600/20">404</div>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-2xl font-semibold">Page Not Found</div>
          </div>
        </div>
        
        <motion.p 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-muted-foreground mb-8 text-pretty"
        >
          The page you're looking for doesn't exist or has been moved. Let's get you back on track.
        </motion.p>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="flex flex-col sm:flex-row gap-3 justify-center"
        >
          {isShopPath ? (
            <>
              <Button 
                className="bg-mduka-600 hover:bg-mduka-700 text-white"
                asChild
                variant="default"
              >
                <Link to="/shop">
                  <ShoppingBag className="mr-2 h-4 w-4" />
                  Return to Shop
                </Link>
              </Button>
              <Button 
                variant="outline"
                asChild
              >
                <Link to="/">
                  <Home className="mr-2 h-4 w-4" />
                  Go to Homepage
                </Link>
              </Button>
            </>
          ) : isLegalPath ? (
            // For legal pages, provide links to all legal pages
            <div className="space-y-3">
              <Button 
                className="bg-mduka-600 hover:bg-mduka-700 text-white w-full"
                asChild
              >
                <Link to="/">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Return to Home
                </Link>
              </Button>
              <div className="flex flex-wrap gap-2 justify-center mt-4">
                <Button variant="outline" asChild size="sm">
                  <Link to="/terms">Terms of Service</Link>
                </Button>
                <Button variant="outline" asChild size="sm">
                  <Link to="/privacy">Privacy Policy</Link>
                </Button>
                <Button variant="outline" asChild size="sm">
                  <Link to="/cookies">Cookie Policy</Link>
                </Button>
              </div>
            </div>
          ) : (
            <Button 
              className="bg-mduka-600 hover:bg-mduka-700 text-white"
              asChild
            >
              <Link to="/">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Return to Home
              </Link>
            </Button>
          )}
        </motion.div>
      </motion.div>
    </div>
  );
};

export default NotFound;
