
import React from 'react';
import ProfileSection from './ProfileSection';
import NotificationSection from './NotificationSection';
import SecuritySection from './SecuritySection';

interface ProfileContentProps {
  userName: string;
  setUserName: (value: string) => void;
  userEmail: string;
  userPhone: string;
  setUserPhone: (value: string) => void;
  userBio: string;
  setUserBio: (value: string) => void;
  receiveMarketingEmails: boolean;
  setReceiveMarketingEmails: (value: boolean) => void;
  receiveOrderUpdates: boolean;
  setReceiveOrderUpdates: (value: boolean) => void;
  twoFactorEnabled: boolean;
  setTwoFactorEnabled: (value: boolean) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
  onPasswordChange: () => void;
  avatarUrl: string;
  onAvatarUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploadingAvatar: boolean;
}

const ProfileContent: React.FC<ProfileContentProps> = (props) => {
  return (
    <div className="space-y-6">
      <ProfileSection
        userName={props.userName}
        setUserName={props.setUserName}
        userEmail={props.userEmail}
        userPhone={props.userPhone}
        setUserPhone={props.setUserPhone}
        userBio={props.userBio}
        setUserBio={props.setUserBio}
        loading={props.loading}
        onSubmit={props.onSubmit}
        avatarUrl={props.avatarUrl}
        onAvatarUpload={props.onAvatarUpload}
        isUploadingAvatar={props.isUploadingAvatar}
      />
      
      <NotificationSection
        receiveMarketingEmails={props.receiveMarketingEmails}
        setReceiveMarketingEmails={props.setReceiveMarketingEmails}
        receiveOrderUpdates={props.receiveOrderUpdates}
        setReceiveOrderUpdates={props.setReceiveOrderUpdates}
      />
      
      <SecuritySection
        twoFactorEnabled={props.twoFactorEnabled}
        setTwoFactorEnabled={props.setTwoFactorEnabled}
        onPasswordChange={props.onPasswordChange}
      />
    </div>
  );
};

export default ProfileContent;
