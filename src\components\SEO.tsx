
import React from 'react';
import { Helmet } from 'react-helmet-async';

interface SEOProps {
  title?: string;
  description?: string;
  canonical?: string;
  ogImage?: string;
  noIndex?: boolean;
  keywords?: string;
  author?: string;
  type?: 'website' | 'article' | 'product';
  publishedAt?: string;
  modifiedAt?: string;
  locale?: string;
  twitterCard?: 'summary' | 'summary_large_image' | 'app' | 'player';
}

export const SEO: React.FC<SEOProps> = ({
  title = 'M-duka | Turn Your WhatsApp Business into a Digital Store',
  description = 'Transform your WhatsApp Business into a powerful digital storefront. Create your online store in minutes, manage orders, and grow your business effortlessly.',
  canonical = 'https://m-duka.app/',
  ogImage = '/og-image.png',
  noIndex = false,
  keywords = 'whatsapp store, online business, mobile commerce, digital storefront, m-duka, m-commerce, whatsapp business, e-commerce',
  author = 'M-duka',
  type = 'website',
  publishedAt,
  modifiedAt,
  locale = 'en_US',
  twitterCard = 'summary_large_image',
}) => {
  const siteTitle = title;
  const siteDescription = description;
  const absoluteOgImage = ogImage.startsWith('http') ? ogImage : `https://m-duka.app${ogImage}`;

  return (
    <Helmet>
      {/* Basic metadata */}
      <title>{siteTitle}</title>
      <meta name="description" content={siteDescription} />
      {canonical && <link rel="canonical" href={canonical} />}
      <meta name="keywords" content={keywords} />
      <meta name="author" content={author} />
      
      {/* Open Graph / Facebook */}
      <meta property="og:type" content={type} />
      <meta property="og:url" content={canonical} />
      <meta property="og:title" content={siteTitle} />
      <meta property="og:description" content={siteDescription} />
      <meta property="og:image" content={absoluteOgImage} />
      <meta property="og:site_name" content="M-duka" />
      <meta property="og:locale" content={locale} />
      {publishedAt && <meta property="article:published_time" content={publishedAt} />}
      {modifiedAt && <meta property="article:modified_time" content={modifiedAt} />}

      {/* Twitter */}
      <meta property="twitter:card" content={twitterCard} />
      <meta property="twitter:url" content={canonical} />
      <meta property="twitter:title" content={siteTitle} />
      <meta property="twitter:description" content={siteDescription} />
      <meta property="twitter:image" content={absoluteOgImage} />
      <meta property="twitter:site" content="@mduka_app" />
      
      {/* No index if specified */}
      {noIndex && <meta name="robots" content="noindex,nofollow" />}
      {!noIndex && <meta name="robots" content="index,follow" />}
      
      {/* Favicon links */}
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      
      {/* Schema.org JSON-LD structured data */}
      <script type="application/ld+json">
        {JSON.stringify({
          "@context": "https://schema.org",
          "@type": "WebSite",
          "url": canonical,
          "name": "M-duka",
          "description": siteDescription,
          "potentialAction": {
            "@type": "SearchAction",
            "target": "https://m-duka.app/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
          }
        })}
      </script>
    </Helmet>
  );
};
