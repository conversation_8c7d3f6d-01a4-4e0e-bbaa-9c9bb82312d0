
import React from 'react';
import Index from '@/pages/Index';
import SignIn from '@/pages/SignIn';
import SignUp from '@/pages/SignUp';
import ForgotPassword from '@/pages/ForgotPassword';
import ResetPassword from '@/pages/ResetPassword';
import ConfirmSignUp from '@/pages/ConfirmSignUp';
import AuthCallback from '@/pages/AuthCallback';
import DevEmailBypass from '@/pages/DevEmailBypass';
import EmailTestPage from '@/pages/EmailTestPage';
import ProfileFixPage from '@/pages/ProfileFixPage';
import TermsOfService from '@/pages/legal/TermsOfService';
import PrivacyPolicy from '@/pages/legal/PrivacyPolicy';
import CookiePolicy from '@/pages/legal/CookiePolicy';
import Pricing from '@/pages/Pricing';
import DevNavigation from '@/pages/DevNavigation';

export const publicRoutes = [
  {
    path: "/",
    element: <Index />,
  },
  {
    path: "/signin",
    element: <SignIn />,
  },
  {
    path: "/signup/*",
    element: <SignUp />,
  },
  {
    path: "/forgot-password",
    element: <ForgotPassword />,
  },
  {
    path: "/reset-password",
    element: <ResetPassword />,
  },
  {
    path: "/confirm-signup",
    element: <ConfirmSignUp />,
  },
  {
    path: "/auth/callback",
    element: <AuthCallback />,
  },
  {
    path: "/terms",
    element: <TermsOfService />,
  },
  {
    path: "/privacy",
    element: <PrivacyPolicy />,
  },
  {
    path: "/cookies",
    element: <CookiePolicy />,
  },
  {
    path: "/pricing",
    element: <Pricing />,
  },
  {
    path: "/dev",
    element: <DevNavigation />,
  },
  {
    path: "/dev/email-bypass",
    element: <DevEmailBypass />,
  },
  {
    path: "/email-fix",
    element: <EmailTestPage />,
  },
  {
    path: "/profile-fix",
    element: <ProfileFixPage />,
  },
];

console.log("Public routes loaded:", publicRoutes.length);
