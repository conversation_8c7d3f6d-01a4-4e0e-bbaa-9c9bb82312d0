
import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, Link } from "react-router-dom";
import { ArrowLeft, User, Calendar, ShoppingBag, CreditCard } from "lucide-react";
import { useCustomer } from "@/contexts";
import { CustomerType } from "@/types/customer";
import { formatCurrency } from "@/utils/formatters";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

const CustomerDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { customers } = useCustomer();
  const [customer, setCustomer] = useState<CustomerType | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Find the customer in the customers array
    if (customers.length > 0 && id) {
      const foundCustomer = customers.find(c => c.id === id);
      setCustomer(foundCustomer || null);
    }
    
    // Simulate loading in case no customers are loaded yet
    const timer = setTimeout(() => setLoading(false), 500);
    return () => clearTimeout(timer);
  }, [customers, id]);

  // Get initials for avatar
  const getInitials = (name: string): string => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  // Format date helper
  const formatDate = (dateString: string): string => {
    if (!dateString) return 'Never';
    
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }).format(new Date(dateString));
  };

  if (loading) {
    return (
      <div className="container max-w-4xl py-8">
        <div className="flex items-center mb-6">
          <Skeleton className="h-10 w-10 rounded-full mr-4" />
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Skeleton key={i} className="h-48 rounded-md" />
          ))}
        </div>
      </div>
    );
  }

  if (!customer) {
    return (
      <div className="container max-w-4xl py-8">
        <Link to="/customers">
          <Button variant="ghost" className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Customers
          </Button>
        </Link>
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-8 text-center">
          <User className="h-16 w-16 text-amber-500 mx-auto mb-4" />
          <h2 className="text-2xl font-bold text-gray-800 mb-2">Customer Not Found</h2>
          <p className="text-gray-600 mb-6">
            The customer you're looking for doesn't exist or has been removed.
          </p>
          <Button asChild>
            <Link to="/customers">View All Customers</Link>
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="container max-w-4xl py-8">
      <Link to="/customers">
        <Button variant="ghost" className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Customers
        </Button>
      </Link>

      <div className="flex flex-col md:flex-row items-start md:items-center justify-between mb-6">
        <div className="flex items-center mb-4 md:mb-0">
          <Avatar className="h-12 w-12 mr-4 border-2 border-primary/10">
            <AvatarImage src={customer.avatar || undefined} />
            <AvatarFallback>{getInitials(customer.name || '?')}</AvatarFallback>
          </Avatar>
          <div>
            <h1 className="text-2xl font-bold">{customer.name}</h1>
            <p className="text-muted-foreground">{customer.email}</p>
          </div>
        </div>
        <Badge variant="outline" className={getStatusColor(customer.status)}>
          {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
        </Badge>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <ShoppingBag className="h-5 w-5 mr-2 text-primary/80" />
              Order Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Total Orders</dt>
                <dd className="font-medium">{customer.totalOrders}</dd>
              </div>
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Total Spent</dt>
                <dd className="font-medium">{formatCurrency(customer.totalSpent, 'USD')}</dd>
              </div>
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Last Order</dt>
                <dd className="font-medium">{formatDate(customer.lastOrderDate)}</dd>
              </div>
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Average Order Value</dt>
                <dd className="font-medium">
                  {customer.totalOrders > 0 
                    ? formatCurrency(customer.totalSpent / customer.totalOrders, 'USD') 
                    : '$0.00'}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <User className="h-5 w-5 mr-2 text-primary/80" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <dl className="space-y-2">
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Customer ID</dt>
                <dd className="font-medium font-mono text-xs">{customer.id}</dd>
              </div>
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Status</dt>
                <dd>
                  <Badge variant="outline" className={getStatusColor(customer.status)}>
                    {customer.status.charAt(0).toUpperCase() + customer.status.slice(1)}
                  </Badge>
                </dd>
              </div>
              <div className="flex justify-between py-1">
                <dt className="text-muted-foreground">Email</dt>
                <dd className="font-medium">{customer.email}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Calendar className="h-5 w-5 mr-2 text-primary/80" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            {customer.lastOrderDate ? (
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <div className="bg-primary/10 p-2 rounded-full">
                    <ShoppingBag className="h-4 w-4 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Made a purchase</p>
                    <p className="text-sm text-muted-foreground">
                      {formatDate(customer.lastOrderDate)}
                    </p>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No recent activity to show</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <CreditCard className="h-5 w-5 mr-2 text-primary/80" />
              Payment Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Payment information is not available in this view.
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 flex justify-end">
        <Button variant="outline" className="mr-2">
          Edit Customer
        </Button>
        <Button variant="destructive">
          Delete Customer
        </Button>
      </div>
    </div>
  );
};

export default CustomerDetails;
