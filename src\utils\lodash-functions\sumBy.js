
/**
 * Iterates over elements of collection, returning the sum of the results of invoking
 * iteratee on each element.
 *
 * @param {Array} array - The array to iterate over
 * @param {Function|string} iteratee - The function/property invoked per iteration
 * @returns {number} Returns the sum
 */
function sumBy(array, iteratee) {
  if (!array || !array.length) return 0;
  
  let sum = 0;
  
  // Handle function iteratee
  if (typeof iteratee === 'function') {
    for (let i = 0; i < array.length; i++) {
      sum += iteratee(array[i]);
    }
    return sum;
  }
  
  // Handle string property iteratee
  if (typeof iteratee === 'string') {
    for (let i = 0; i < array.length; i++) {
      if (array[i] != null && array[i][iteratee] != null) {
        sum += array[i][iteratee];
      }
    }
    return sum;
  }
  
  // If no iteratee is provided, assume array of numbers
  for (let i = 0; i < array.length; i++) {
    sum += array[i];
  }
  
  return sum;
}

// Support both ESM and CJS
export default sumBy;
