
import React, { useEffect, useState, useMemo } from "react";
import { useSearchParams, useParams, Link } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { useProduct } from "@/contexts";
import ProductCard from "@/components/storefront/ProductCard";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Search, Filter, SlidersHorizontal } from "lucide-react";
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { Separator } from "@/components/ui/separator";
import ProductListingBreadcrumb from "@/components/storefront/product/ProductListingBreadcrumb";
import { LoadingFallback } from "@/components/ui/loading-fallback";

const ProductListing: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { category } = useParams();
  const { 
    filteredProducts, 
    isLoading, 
    searchProducts, 
    setFilters, 
    clearFilters,
    searchTerm,
    filters,
    categories 
  } = useProduct();

  const [localSearchTerm, setLocalSearchTerm] = useState(searchTerm);
  const [sortBy, setSortBy] = useState("name");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("asc");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [selectedCategory, setSelectedCategory] = useState(category || "");

  // Initialize filters from URL params
  useEffect(() => {
    const urlSearch = searchParams.get('search') || '';
    const urlCategory = searchParams.get('category') || category || '';
    const urlMinPrice = searchParams.get('minPrice') || '';
    const urlMaxPrice = searchParams.get('maxPrice') || '';

    setLocalSearchTerm(urlSearch);
    setSelectedCategory(urlCategory);
    setPriceRange({ min: urlMinPrice, max: urlMaxPrice });

    // Apply filters
    const newFilters: any = {};
    if (urlCategory) newFilters.category = urlCategory;
    if (urlMinPrice) newFilters.minPrice = parseFloat(urlMinPrice);
    if (urlMaxPrice) newFilters.maxPrice = parseFloat(urlMaxPrice);

    setFilters(newFilters);
    if (urlSearch) searchProducts(urlSearch);
  }, [searchParams, category, setFilters, searchProducts]);

  // Sort products
  const sortedProducts = useMemo(() => {
    const sorted = [...filteredProducts].sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (sortBy) {
        case "price":
          aValue = a.price || 0;
          bValue = b.price || 0;
          break;
        case "name":
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
          break;
        case "created_at":
          aValue = new Date(a.created_at || 0).getTime();
          bValue = new Date(b.created_at || 0).getTime();
          break;
        default:
          aValue = a.name?.toLowerCase() || '';
          bValue = b.name?.toLowerCase() || '';
      }

      if (aValue < bValue) return sortOrder === "asc" ? -1 : 1;
      if (aValue > bValue) return sortOrder === "asc" ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [filteredProducts, sortBy, sortOrder]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    searchProducts(localSearchTerm);
    updateUrlParams({ search: localSearchTerm });
  };

  const handleCategoryFilter = (categoryValue: string) => {
    setSelectedCategory(categoryValue);
    setFilters({ category: categoryValue || undefined });
    updateUrlParams({ category: categoryValue });
  };

  const handlePriceFilter = () => {
    const minPrice = priceRange.min ? parseFloat(priceRange.min) : undefined;
    const maxPrice = priceRange.max ? parseFloat(priceRange.max) : undefined;
    
    setFilters({ 
      minPrice: minPrice,
      maxPrice: maxPrice
    });
    updateUrlParams({ 
      minPrice: priceRange.min, 
      maxPrice: priceRange.max 
    });
  };

  const updateUrlParams = (params: Record<string, string>) => {
    const newSearchParams = new URLSearchParams(searchParams);
    
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });
    
    setSearchParams(newSearchParams);
  };

  const clearAllFilters = () => {
    clearFilters();
    setLocalSearchTerm("");
    setSelectedCategory("");
    setPriceRange({ min: "", max: "" });
    setSearchParams({});
  };

  const activeFiltersCount = [
    searchTerm,
    filters.category,
    filters.minPrice,
    filters.maxPrice
  ].filter(Boolean).length;

  if (isLoading) {
    return <LoadingFallback size="large" message="Loading products..." className="py-12" />;
  }

  return (
    <div className="min-h-screen bg-gray-50 py-4">
      <Container>
        <ProductListingBreadcrumb category={category} />
        
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl md:text-3xl font-bold mb-2">
            {category ? `${category} Products` : 'All Products'}
          </h1>
          <p className="text-gray-600">
            {sortedProducts.length} product{sortedProducts.length === 1 ? '' : 's'} found
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-2 mb-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search products..."
                value={localSearchTerm}
                onChange={(e) => setLocalSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button type="submit">Search</Button>
          </form>

          {/* Filter Controls */}
          <div className="flex flex-wrap gap-4 items-center">
            {/* Category Filter */}
            <Select value={selectedCategory} onValueChange={handleCategoryFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All Categories</SelectItem>
                {categories.map((cat) => (
                  <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Price Range Filter */}
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                placeholder="Min price"
                value={priceRange.min}
                onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                className="w-24"
              />
              <span className="text-gray-500">-</span>
              <Input
                type="number"
                placeholder="Max price"
                value={priceRange.max}
                onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                className="w-24"
              />
              <Button variant="outline" onClick={handlePriceFilter}>
                Apply
              </Button>
            </div>

            {/* Sort Controls */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="price">Price</SelectItem>
                <SelectItem value="created_at">Newest</SelectItem>
              </SelectContent>
            </Select>

            <Select value={sortOrder} onValueChange={(value: "asc" | "desc") => setSortOrder(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">Ascending</SelectItem>
                <SelectItem value="desc">Descending</SelectItem>
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            {activeFiltersCount > 0 && (
              <Button variant="outline" onClick={clearAllFilters}>
                <X className="h-4 w-4 mr-1" />
                Clear ({activeFiltersCount})
              </Button>
            )}
          </div>

          {/* Active Filters */}
          {activeFiltersCount > 0 && (
            <div className="flex gap-2 mt-4 flex-wrap">
              {searchTerm && (
                <Badge variant="secondary">
                  Search: {searchTerm}
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => {
                    searchProducts("");
                    setLocalSearchTerm("");
                    updateUrlParams({ search: "" });
                  }} />
                </Badge>
              )}
              {filters.category && (
                <Badge variant="secondary">
                  Category: {filters.category}
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => handleCategoryFilter("")} />
                </Badge>
              )}
              {filters.minPrice && (
                <Badge variant="secondary">
                  Min: ${filters.minPrice}
                </Badge>
              )}
              {filters.maxPrice && (
                <Badge variant="secondary">
                  Max: ${filters.maxPrice}
                </Badge>
              )}
            </div>
          )}
        </div>

        {/* Products Grid */}
        {sortedProducts.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No products found</h3>
            <p className="text-gray-600 mb-4">
              {activeFiltersCount > 0 
                ? "Try adjusting your filters or search terms"
                : "No products are available at the moment"
              }
            </p>
            {activeFiltersCount > 0 && (
              <Button onClick={clearAllFilters}>Clear all filters</Button>
            )}
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
            {sortedProducts.map((product) => (
              <div key={product.id} className="animate-fade-in">
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        )}
      </Container>
    </div>
  );
};

export default ProductListing;
