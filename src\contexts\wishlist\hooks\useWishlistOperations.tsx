
import { useCallback } from 'react';
import { Product } from '@/types/unified-product';
import { WishlistItem } from '../types';
import { useAuth } from '@/contexts/auth/AuthContext';
import { addWishlistItem, removeWishlistItem, clearUserWishlist } from '../wishlistApi';
import { useToast } from '@/hooks/use-toast';

export const useWishlistOperations = (
  wishlistItems: WishlistItem[],
  setWishlistItems: (items: WishlistItem[]) => void,
  setIsLoading: (loading: boolean) => void,
  setIsAddingToWishlist: (adding: boolean) => void
) => {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();

  const addToWishlist = useCallback(async (product: Product) => {
    setIsAddingToWishlist(true);
    
    try {
      const existingItem = wishlistItems.find(item => item.product.id === product.id);
      
      if (existingItem) {
        toast({
          title: "Already in wishlist",
          description: `${product.name} is already in your wishlist.`,
          duration: 3000,
        });
        return;
      }
      
      let newItemId = `local-${product.id}`;
      
      if (isAuthenticated && user) {
        try {
          newItemId = await addWishlistItem(user.id, product.id!);
        } catch (error) {
          console.error('Error adding to Supabase wishlist:', error);
          toast({
            title: "Saved locally",
            description: "Item added to local wishlist due to server error.",
            variant: "default",
          });
        }
      }
      
      setWishlistItems([...wishlistItems, {
        id: newItemId,
        product,
        user_id: user?.id,
        product_id: product.id
      }]);
      
      toast({
        title: "Added to wishlist",
        description: `${product.name} added to your wishlist.`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in addToWishlist:', error);
      toast({
        title: "Error",
        description: "Could not add item to wishlist.",
        variant: "destructive",
      });
    } finally {
      setIsAddingToWishlist(false);
    }
  }, [wishlistItems, isAuthenticated, user, setWishlistItems, setIsAddingToWishlist, toast]);

  const removeFromWishlist = useCallback(async (productId: string) => {
    setIsLoading(true);
    
    try {
      const itemToRemove = wishlistItems.find(item => item.product.id === productId);
      
      if (!itemToRemove) {
        return;
      }
      
      if (isAuthenticated && user && !itemToRemove.id?.startsWith('local-')) {
        try {
          await removeWishlistItem(itemToRemove.id!);
        } catch (error) {
          console.error('Error removing from Supabase wishlist:', error);
          toast({
            title: "Sync error",
            description: "Item removed locally but could not sync with server.",
            variant: "default",
          });
        }
      }
      
      setWishlistItems(wishlistItems.filter(item => item.product.id !== productId));
      
      toast({
        title: "Removed from wishlist",
        description: `${itemToRemove.product.name} removed from your wishlist.`,
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in removeFromWishlist:', error);
      toast({
        title: "Error",
        description: "Could not remove item from wishlist.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [wishlistItems, isAuthenticated, user, setWishlistItems, setIsLoading, toast]);

  const clearWishlist = useCallback(async () => {
    setIsLoading(true);
    
    try {
      if (isAuthenticated && user) {
        try {
          await clearUserWishlist(user.id);
        } catch (error) {
          console.error('Error clearing Supabase wishlist:', error);
          toast({
            title: "Sync error",
            description: "Wishlist cleared locally but could not sync with server.",
            variant: "default",
          });
        }
      }
      
      setWishlistItems([]);
      
      toast({
        title: "Wishlist cleared",
        description: "All items have been removed from your wishlist.",
        duration: 3000,
      });
    } catch (error) {
      console.error('Error in clearWishlist:', error);
      toast({
        title: "Error",
        description: "Could not clear wishlist.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isAuthenticated, user, setWishlistItems, setIsLoading, toast]);

  const isInWishlist = useCallback((productId: string) => {
    return wishlistItems.some(item => item.product.id === productId);
  }, [wishlistItems]);

  return {
    addToWishlist,
    removeFromWishlist,
    clearWishlist,
    isInWishlist
  };
};
