
/**
 * Plan categories and their features.
 * Use to easily map which features belong to Basic, Premium, or Business plans.
 */

export type PlanCategory = "basic" | "premium" | "business";

export interface PlanFeature {
  name: string;
  tier: PlanCategory;
  description?: string;
}

export const PLAN_FEATURES: PlanFeature[] = [
  // BASIC PLAN FEATURES
  { name: "Unlimited WhatsApp orders", tier: "basic" },
  { name: "No commissions", tier: "basic" },
  { name: "Manual payments", tier: "basic" },
  { name: "Upload up to 20 images", tier: "basic" },

  // PREMIUM PLAN FEATURES - inherits all basic
  { name: "Unlimited images", tier: "premium" },
  { name: "Custom domain and email", tier: "premium" },
  { name: "Card payments (Stripe and more)", tier: "premium" },
  { name: "Payment proof and processing fee", tier: "premium" },
  { name: "Analytics, SEO and Meta Pixel", tier: "premium" },
  { name: "Invoice settings and PDF", tier: "premium" },
  { name: "CSV export/import", tier: "premium" },
  { name: "Delivery distance calculation", tier: "premium" },
  { name: "Customer reviews", tier: "premium" },
  { name: "Live chat support", tier: "premium" },

  // BUSINESS PLAN FEATURES - inherits all premium & basic
  { name: "5 stores and 5 staff accounts", tier: "business" },
  { name: "M-Duka.app logo removal", tier: "business" },
  { name: "WhatsApp Workflow and Catalog", tier: "business" },
  { name: "Shared Team WhatsApp Inbox", tier: "business" },
  { name: "Membership rewards", tier: "business" },
  { name: "Membership exclusive access", tier: "business" },
  { name: "Wholesale pricing", tier: "business" },
  { name: "Webhooks and API", tier: "business" },
  { name: "3rd-party apps integrations", tier: "business" },
  { name: "Priority support", tier: "business" },
];
