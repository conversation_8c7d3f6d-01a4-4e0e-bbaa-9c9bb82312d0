
import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  FormField, 
  FormControl, 
  FormDescription, 
  FormItem, 
  FormLabel 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";

interface InternationalShippingCardProps {
  form: any;
}

const InternationalShippingCard: React.FC<InternationalShippingCardProps> = ({ form }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>International Shipping</CardTitle>
        <CardDescription>Configure shipping options for international deliveries.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={form.control}
          name="enableInternationalShipping"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Enable International Shipping</FormLabel>
                <FormDescription>
                  Allow orders from international customers
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {form.watch("enableInternationalShipping") && (
          <FormField
            control={form.control}
            name="internationalShippingRate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>International Shipping Rate ($)</FormLabel>
                <FormControl>
                  <Input placeholder="15.00" {...field} />
                </FormControl>
                <FormDescription>
                  Flat rate for international orders.
                </FormDescription>
              </FormItem>
            )}
          />
        )}
      </CardContent>
    </Card>
  );
};

export default InternationalShippingCard;
