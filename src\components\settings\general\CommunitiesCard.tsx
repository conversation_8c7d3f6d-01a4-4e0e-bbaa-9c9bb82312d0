
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

interface CommunitiesCardProps {
  whatsappLink: string;
  setWhatsappLink: (value: string) => void;
  instagramLink: string;
  setInstagramLink: (value: string) => void;
  facebookLink: string;
  setFacebookLink: (value: string) => void;
}

const CommunitiesCard = ({
  whatsappLink,
  setWhatsappLink,
  instagramLink,
  setInstagramLink,
  facebookLink,
  setFacebookLink
}: CommunitiesCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Communities</CardTitle>
        <CardDescription>
          Showcase your online community on your stores
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="whatsapp-link">WhatsApp</Label>
          <Input 
            id="whatsapp-link"
            value={whatsappLink}
            onChange={(e) => setWhatsappLink(e.target.value)}
            placeholder="Group, community, or channel link"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="telegram-link">Telegram</Label>
          <Input 
            placeholder="Group or channel link"
            defaultValue="https://"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="instagram-link">Instagram</Label>
          <Input 
            id="instagram-link"
            value={instagramLink}
            onChange={(e) => setInstagramLink(e.target.value)}
            placeholder="Group, channel, or profile link"
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="facebook-link">Facebook</Label>
          <Input 
            id="facebook-link"
            value={facebookLink}
            onChange={(e) => setFacebookLink(e.target.value)}
            placeholder="Group or page link"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default CommunitiesCard;
