
import React from 'react';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import SettingsSidebar from '@/components/settings/SettingsSidebar';
import { useAuth, useStore } from '@/contexts';
import { Settings as SettingsIcon } from 'lucide-react';
import GeneralSettings from './settings/GeneralSettings';
import BillingSettings from './settings/BillingSettings';
import PaymentSettings from './settings/PaymentSettings';
import DeliverySettings from './settings/DeliverySettings';
import DomainsSettings from './settings/DomainsSettings';
import WhatsAppSettings from './settings/WhatsAppSettings';
import WorkflowSettings from './settings/WorkflowSettings';
import StaffSettings from './settings/StaffSettings';

const Settings = () => {
  const { user } = useAuth();
  const { currentStore } = useStore();
  const navigate = useNavigate();
  const location = useLocation();

  // Redirect if no user or store
  React.useEffect(() => {
    if (!user) {
      navigate('/signin');
    }
  }, [user, navigate]);

  // If accessing /settings directly without a nested route, redirect to /settings/general
  React.useEffect(() => {
    if (location.pathname === "/settings") {
      navigate('/settings/general');
    }
  }, [location.pathname, navigate]);

  return (
    <div className="min-h-screen bg-background">
      <div className="flex h-screen overflow-hidden">
        {/* Settings sidebar */}
        <SettingsSidebar />
        
        {/* Main content area */}
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            <header className="mb-8">
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <SettingsIcon className="h-6 w-6" />
                <span>
                  {currentStore?.name ? `${currentStore.name} Settings` : 'Store Settings'}
                </span>
              </h1>
            </header>
            
            {/* Routes for nested settings pages */}
            <Routes>
              <Route path="/" element={<GeneralSettings />} />
              <Route path="general" element={<GeneralSettings />} />
              <Route path="billing" element={<BillingSettings />} />
              <Route path="payments" element={<PaymentSettings />} />
              <Route path="delivery" element={<DeliverySettings />} />
              <Route path="domains" element={<DomainsSettings />} />
              <Route path="whatsapp" element={<WhatsAppSettings />} />
              <Route path="workflow/*" element={<WorkflowSettings />} />
              <Route path="staff" element={<StaffSettings />} />
              {/* Default to GeneralSettings if route doesn't match */}
              <Route path="*" element={<GeneralSettings />} />
            </Routes>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
