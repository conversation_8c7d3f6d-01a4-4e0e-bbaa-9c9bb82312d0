
import { motion } from "framer-motion";
import { Clock, Truck, ShieldCheck } from "lucide-react";

const StoreAbout = () => {
  return (
    <div className="p-4 border-b">
      <div className="text-sm font-medium mb-2">About Us</div>
      <p className="text-xs text-gray-600 mb-3">
        We deliver fresh groceries to your doorstep within 2 hours. Quality products at affordable prices across Dar es Salaam.
      </p>
      
      {/* Store features */}
      <div className="grid grid-cols-3 gap-2 mt-2">
        {[
          { icon: <Clock className="h-3 w-3" />, text: "2hr Delivery" },
          { icon: <Truck className="h-3 w-3" />, text: "Free Shipping" },
          { icon: <ShieldCheck className="h-3 w-3" />, text: "Quality Guarantee" }
        ].map((feature, index) => (
          <motion.div 
            key={index}
            initial={{ opacity: 0, y: 5 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="flex flex-col items-center justify-center bg-gray-50 rounded-md p-1.5"
          >
            <div className="text-green-600 mb-0.5">{feature.icon}</div>
            <div className="text-[0.6rem] text-gray-600">{feature.text}</div>
          </motion.div>
        ))}
      </div>
    </div>
  );
};

export default StoreAbout;
