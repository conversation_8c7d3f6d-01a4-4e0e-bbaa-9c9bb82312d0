
import React, { useContext } from "react";
import { <PERSON> } from "react-router-dom";
import { AuthContext } from '../../contexts/auth/AuthContext';

const DesktopNav: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);

  return (
    <nav className="hidden md:flex items-center gap-6">
      <Link
        to="/#features"
        className="text-muted-foreground hover:text-foreground transition-colors"
      >
        Features
      </Link>
      <Link
        to="/pricing"
        className="text-muted-foreground hover:text-foreground transition-colors"
      >
        Pricing
      </Link>
      <Link
        to="/#faq"
        className="text-muted-foreground hover:text-foreground transition-colors"
      >
        FAQ
      </Link>

      {/* Removed the Dashboard Preview link */}
    </nav>
  );
};

export default DesktopNav;
