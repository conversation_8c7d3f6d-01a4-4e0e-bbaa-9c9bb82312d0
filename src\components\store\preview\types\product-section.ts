
import { Product } from '@/types/unified-product';

export interface ProductSectionBaseProps {
  cornerRadius: number;
  fontFamily: string;
  darkMode: boolean;
  buttonClasses: string;
  primaryColor: string;
  imageClasses: string;
  displayCurrency?: boolean;
  enableNewBadge?: boolean;
  enableShareButtons?: boolean;
}

export interface ProductItemProps extends ProductSectionBaseProps {
  product: Product;
  showRatings: boolean;
  showQuickAdd: boolean;
  layoutType: string;
}

export interface ProductsSectionProps extends ProductSectionBaseProps {
  title: string;
  products: Product[];
  showRatings: boolean;
  showQuickAdd: boolean;
  layoutType: string;
}
