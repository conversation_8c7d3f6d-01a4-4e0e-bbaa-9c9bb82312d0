
import React from 'react';
import { Button } from '@/components/ui/button';
import { MessageCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface WhatsAppContactProps {
  store?: {
    whatsapp_number?: string;
    name?: string;
  } | null;
  productName?: string;
  className?: string;
}

const WhatsAppContact: React.FC<WhatsAppContactProps> = ({ store, productName, className }) => {
  // Return early if no store or whatsapp number
  if (!store || !store.whatsapp_number) {
    console.log("WhatsAppContact: Missing store or whatsapp number");
    return null;
  }

  // Clean up the WhatsApp number - remove all non-digits
  const whatsappNumber = store.whatsapp_number.replace(/\D/g, '');
  
  // If after cleaning there's no valid number, return null
  if (!whatsappNumber) {
    console.log("WhatsAppContact: Invalid WhatsApp number format");
    return null;
  }
  
  const storeName = store.name || 'Store';
  
  const handleWhatsAppClick = () => {
    // Include product name in message if available
    const message = productName 
      ? encodeURIComponent(`Hello ${storeName}, I'm interested in your product: ${productName}!`)
      : encodeURIComponent(`Hello ${storeName}, I'm interested in your products!`);
      
    console.log("Opening WhatsApp with message for:", whatsappNumber);
    window.open(`https://wa.me/${whatsappNumber}?text=${message}`, '_blank');
  };

  return (
    <div className={cn("fixed bottom-10 left-6 z-50", className)}>
      <Button
        size="lg"
        className={cn(
          "rounded-full w-14 h-14 shadow-lg flex items-center justify-center",
          "bg-green-500 hover:bg-green-600 transition-all"
        )}
        onClick={handleWhatsAppClick}
        title={`Contact ${storeName} via WhatsApp`}
      >
        <MessageCircle className="h-6 w-6 text-white" />
      </Button>
    </div>
  );
};

export default WhatsAppContact;
