
import React from 'react';
import { ShoppingCart, Bookmark, Share2 } from 'lucide-react';

interface QuickActionsProps {
  showQuickAdd: boolean;
  enableShareButtons: boolean;
}

const QuickActions: React.FC<QuickActionsProps> = ({
  showQuickAdd,
  enableShareButtons,
}) => {
  return (
    <>
      {(showQuickAdd || enableShareButtons) && (
        <div className="absolute bottom-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
          {showQuickAdd && (
            <button className="w-8 h-8 flex items-center justify-center rounded-full bg-white text-gray-800 shadow-md hover:scale-110 transition-transform">
              <ShoppingCart className="w-4 h-4" />
            </button>
          )}
          
          {enableShareButtons && (
            <>
              <button className="w-8 h-8 flex items-center justify-center rounded-full bg-white text-gray-800 shadow-md hover:scale-110 transition-transform">
                <Bookmark className="w-4 h-4" />
              </button>
              
              <button className="w-8 h-8 flex items-center justify-center rounded-full bg-white text-gray-800 shadow-md hover:scale-110 transition-transform">
                <Share2 className="w-4 h-4" />
              </button>
            </>
          )}
        </div>
      )}
    </>
  );
};

export default QuickActions;
