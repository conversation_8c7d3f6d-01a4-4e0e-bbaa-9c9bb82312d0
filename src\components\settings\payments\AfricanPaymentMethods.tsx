
import React from 'react';
import PaymentMethodSection, { PaymentMethod } from './PaymentMethodSection';
import { Smartphone, CreditCard, Briefcase } from 'lucide-react';

interface AfricanPaymentMethodsProps {
  onToggleMethod: (methodId: string, enabled: boolean) => void;
  isMethodEnabled: (methodId: string) => boolean;
}

const AfricanPaymentMethods: React.FC<AfricanPaymentMethodsProps> = ({
  onToggleMethod,
  isMethodEnabled
}) => {
  const paymentMethods: PaymentMethod[] = [
    {
      id: 'flutterwave',
      name: 'Flutterwave',
      description: 'Accept payments from across Africa including mobile money, cards, and bank transfers.',
      icon: <CreditCard className="h-5 w-5 text-orange-600" />,
      region: 'Africa',
      popular: true,
      fields: [
        {
          id: 'public-key',
          name: 'Public Key',
          placeholder: 'FLWPUBK_TEST-...',
          type: 'text',
          required: true
        },
        {
          id: 'secret-key',
          name: 'Secret Key',
          placeholder: 'FLWSECK_TEST-...',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://dashboard.flutterwave.com/signup'
    },
    {
      id: 'paystack',
      name: 'Paystack',
      description: 'Modern online and offline payments for Africa.',
      icon: <Briefcase className="h-5 w-5 text-green-600" />,
      region: 'Africa',
      fields: [
        {
          id: 'public-key',
          name: 'Public Key',
          placeholder: 'pk_test_...',
          type: 'text',
          required: true
        },
        {
          id: 'secret-key',
          name: 'Secret Key',
          placeholder: 'sk_test_...',
          type: 'password',
          required: true
        }
      ],
      setupUrl: 'https://dashboard.paystack.com/#/signup'
    },
    {
      id: 'dpo',
      name: 'DPO Group',
      description: 'Pan-African payment service provider operating across the continent.',
      icon: <CreditCard className="h-5 w-5 text-blue-600" />,
      region: 'Africa',
      fields: [
        {
          id: 'company-token',
          name: 'Company Token',
          placeholder: 'Enter your DPO company token',
          type: 'text',
          required: true
        },
        {
          id: 'service-type',
          name: 'Service Type',
          placeholder: 'Enter your DPO service type',
          type: 'text',
          required: true
        }
      ],
      setupUrl: 'https://www.directpay.online/become-a-merchant/'
    }
  ];

  return (
    <div className="space-y-5">
      {paymentMethods.map(method => (
        <PaymentMethodSection
          key={method.id}
          method={method}
          enabled={isMethodEnabled(method.id)}
          onToggle={onToggleMethod}
        />
      ))}
    </div>
  );
};

export default AfricanPaymentMethods;
