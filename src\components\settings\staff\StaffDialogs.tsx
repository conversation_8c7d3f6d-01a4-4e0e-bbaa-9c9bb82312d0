
import React from 'react';
import StaffDialog from './StaffDialog';
import DeleteStaffDialog from './DeleteStaffDialog';
import { StaffFormValues, StaffMember } from './types';

interface StaffDialogsProps {
  isAddStaffOpen: boolean;
  isEditStaffOpen: boolean;
  isDeleteStaffOpen: boolean;
  selectedStaff?: StaffMember;
  isLoading: boolean;
  onCloseDialogs: () => void;
  onAddStaff: (data: StaffFormValues) => Promise<boolean | void>;
  onEditStaff: (data: StaffFormValues) => Promise<boolean | void>;
  onDeleteStaff: () => Promise<boolean | void>;
}

const StaffDialogs: React.FC<StaffDialogsProps> = ({
  isAddStaffOpen,
  isEditStaffOpen,
  isDeleteStaffOpen,
  selectedStaff,
  isLoading,
  onCloseDialogs,
  onAddStaff,
  onEditStaff,
  onDeleteStaff
}) => {
  return (
    <>
      {/* Add/Edit Staff Dialog */}
      <StaffDialog 
        isOpen={isAddStaffOpen || isEditStaffOpen}
        onClose={onCloseDialogs}
        onSubmit={isEditStaffOpen ? onEditStaff : onAddStaff}
        staff={isEditStaffOpen ? selectedStaff : undefined}
        isLoading={isLoading}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteStaffDialog 
        isOpen={isDeleteStaffOpen}
        onClose={onCloseDialogs}
        onConfirm={onDeleteStaff}
        staff={selectedStaff}
        isLoading={isLoading}
      />
    </>
  );
};

export default StaffDialogs;
