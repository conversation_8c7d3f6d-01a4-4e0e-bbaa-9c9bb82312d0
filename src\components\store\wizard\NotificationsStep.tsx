
import React from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { StoreFormData } from '@/types/store';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';

const formSchema = z.object({
  notificationsEmail: z.string().email({ message: 'Please enter a valid email address.' }).optional(),
  orderNotifications: z.boolean().default(true),
  stockNotifications: z.boolean().default(true),
  marketingNotifications: z.boolean().default(false),
});

interface NotificationsStepProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

const NotificationsStep: React.FC<NotificationsStepProps> = ({ data, updateData }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(formSchema),
    defaultValues: {
      notificationsEmail: data.notificationsEmail || '',
      orderNotifications: data.notifications?.orderNotifications ?? true,
      stockNotifications: data.notifications?.stockNotifications ?? true,
      marketingNotifications: data.notifications?.marketingNotifications ?? false,
    },
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    updateData({
      notificationsEmail: values.notificationsEmail,
      notifications: {
        orderNotifications: values.orderNotifications,
        stockNotifications: values.stockNotifications,
        marketingNotifications: values.marketingNotifications,
      }
    });
  };

  // Auto-save as user changes options
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      onSubmit(value as z.infer<typeof formSchema>);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Notifications</h2>
      <p className="text-muted-foreground mb-6">
        Configure how you want to receive notifications about your store's activity.
      </p>

      <Form {...form}>
        <form onChange={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="notificationsEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notification Email</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="<EMAIL>" 
                    type="email"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Email address where you'll receive store notifications.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <div className="space-y-4">
            <FormField
              control={form.control}
              name="orderNotifications"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <div>
                    <FormLabel>Order Notifications</FormLabel>
                    <FormDescription>
                      Receive notifications when new orders are placed
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch 
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="stockNotifications"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <div>
                    <FormLabel>Inventory Alerts</FormLabel>
                    <FormDescription>
                      Get notified when products are low on stock
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch 
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="marketingNotifications"
              render={({ field }) => (
                <FormItem className="flex items-center justify-between">
                  <div>
                    <FormLabel>Marketing Updates</FormLabel>
                    <FormDescription>
                      Receive tips, updates and promotional content for your store
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch 
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </form>
      </Form>
    </div>
  );
};

export default NotificationsStep;
