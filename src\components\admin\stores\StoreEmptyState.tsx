
import React from 'react';
import { TableRow, TableCell } from '@/components/ui/table';

interface StoreEmptyStateProps {
  searchQuery: string;
  colSpan: number;
}

const StoreEmptyState: React.FC<StoreEmptyStateProps> = ({ searchQuery, colSpan }) => {
  return (
    <TableRow>
      <TableCell colSpan={colSpan} className="h-24 text-center">
        {searchQuery ? 'No stores match your search' : 'No stores found'}
      </TableCell>
    </TableRow>
  );
};

export default StoreEmptyState;
