
import React from 'react';
import { useFormContext } from 'react-hook-form';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertTriangle } from 'lucide-react';
import { ProductVariant } from '@/types/unified-product';
import VariantsTable from '../variants/VariantsTable';
import NewVariantForm from '../variants/NewVariantForm';
import { generateSku } from '@/utils/skuGenerator';

interface VariantsTabProps {
  currency: string;
  price: number;
  sku: string;
}

const VariantsTab: React.FC<VariantsTabProps> = ({ currency, price, sku }) => {
  const form = useFormContext();
  const hasVariants = form.watch('has_variants');
  const variants = form.watch('variants') || [];

  const handleUpdateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    const newVariants = [...variants];
    
    // If changing size or color, regenerate the SKU if it was auto-generated
    if ((field === 'size' || field === 'color') && !newVariants[index].sku?.includes('-custom')) {
      const updatedVariant = {
        ...newVariants[index],
        [field]: value
      };
      newVariants[index] = {
        ...updatedVariant,
        sku: generateSku(
          sku,
          field === 'size' ? value : updatedVariant.size, 
          field === 'color' ? value : updatedVariant.color
        )
      };
    } else {
      newVariants[index] = {
        ...newVariants[index],
        [field]: value
      };
    }
    
    form.setValue('variants', newVariants, { shouldValidate: true });
  };

  const handleRemoveVariant = (index: number) => {
    const newVariants = [...variants];
    newVariants.splice(index, 1);
    form.setValue('variants', newVariants, { shouldValidate: true });
  };

  const handleAddVariant = (newVariant: ProductVariant) => {
    const updatedVariant = {
      ...newVariant,
      sku: newVariant.sku || generateSku(sku, newVariant.size, newVariant.color)
    };
    
    form.setValue('variants', [...variants, updatedVariant], { shouldValidate: true });
  };

  return (
    <div className="space-y-6">
      <FormField
        control={form.control}
        name="has_variants"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Product Variants</FormLabel>
              <FormDescription>
                Enable variants for different sizes, colors, etc.
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />

      {hasVariants ? (
        <FormField
          control={form.control}
          name="variants"
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className="space-y-4">
                  <div className="text-sm text-muted-foreground mb-2">
                    Add variants for different sizes, colors, or other attributes of your product.
                  </div>
                  
                  {variants.length > 0 ? (
                    <VariantsTable 
                      variants={variants}
                      onUpdateVariant={handleUpdateVariant}
                      onRemoveVariant={handleRemoveVariant}
                    />
                  ) : (
                    <div className="text-center p-4 border border-dashed rounded-md text-muted-foreground">
                      No variants added yet
                    </div>
                  )}
                  
                  <NewVariantForm
                    initialPrice={price}
                    onAddVariant={handleAddVariant}
                    generateSku={(size?: string, color?: string) => generateSku(sku, size, color)}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      ) : (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Enable variants above if your product comes in different sizes, colors, or other variations.
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default VariantsTab;
