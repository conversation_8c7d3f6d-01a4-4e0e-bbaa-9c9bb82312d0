import React, { createContext, useContext, useState, useEffect } from 'react';
import { CustomerType } from '@/types/customer';
import { fetchStoreCustomers } from './customerApi';
import { useAuth, useStore } from '@/contexts';
import { toast } from 'sonner';

interface CustomerContextType {
  customers: CustomerType[];
  isLoading: boolean;
  error: Error | null;
  refreshCustomers: () => Promise<void>;
}

const CustomerContext = createContext<CustomerContextType | undefined>(undefined);

export const CustomerProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [customers, setCustomers] = useState<CustomerType[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  const { user, isAuthenticated } = useAuth();
  const { currentStore } = useStore();

  const loadCustomers = async () => {
    if (!isAuthenticated || !currentStore?.id) {
      setIsLoading(false);
      setCustomers([]);
      setError(null);
      return;
    }

    if (
      (currentStore.id || "").startsWith("mock-store") ||
      !/^[0-9a-fA-F-]{36}$/.test(currentStore.id)
    ) {
      setIsLoading(false);
      setCustomers([]);
      setError(null);
      if (import.meta.env.DEV) {
        console.info("Dev: Skipping customer fetch for mock store");
      }
      return;
    }

    setIsLoading(true);
    setError(null);
    try {
      console.log("Loading customers for store:", currentStore.id);
      const fetchedCustomers = await fetchStoreCustomers(currentStore.id);
      setCustomers(fetchedCustomers);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load customers'));
      if (
        isAuthenticated &&
        currentStore?.id &&
        /^[0-9a-fA-F-]{36}$/.test(currentStore.id)
      ) {
        toast.error('Failed to load customers. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    loadCustomers();
  }, [currentStore?.id, isAuthenticated]);

  const refreshCustomers = async () => {
    await loadCustomers();
  };

  return (
    <CustomerContext.Provider value={{ customers, isLoading, error, refreshCustomers }}>
      {children}
    </CustomerContext.Provider>
  );
};

export const useCustomer = () => {
  const context = useContext(CustomerContext);
  if (!context) {
    throw new Error('useCustomer must be used within a CustomerProvider');
  }
  return context;
};
