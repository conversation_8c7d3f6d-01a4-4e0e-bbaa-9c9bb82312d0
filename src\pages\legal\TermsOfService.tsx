
import React from 'react';
import { Container } from "@/components/ui/container";
import Footer from "@/components/Footer";
import { Helmet } from "react-helmet-async";
import { Separator } from "@/components/ui/separator";
import { Link } from "react-router-dom";

const TermsOfService = () => {
  return (
    <>
      <Helmet>
        <title>Terms of Service - M-Duka</title>
        <meta name="description" content="M-Duka terms of service and conditions of use." />
      </Helmet>
      <div className="min-h-screen bg-background">
        <main className="py-12 space-y-8">
          <Container>
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold tracking-tight mb-6">Terms of Service</h1>
              <p className="text-muted-foreground mb-8">Last updated: April 25, 2025</p>
              
              <div className="prose prose-gray max-w-none space-y-8">
                <section>
                  <h2 className="text-2xl font-semibold mb-4">1. Agreement to Terms</h2>
                  <p className="text-base leading-7">
                    By accessing and using M-Duka's services, you agree to be bound by these Terms of Service 
                    ("Terms"), our Privacy Policy, and Cookie Policy. If you do not agree to these Terms, 
                    you must not access or use our services.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">2. Account Registration and Security</h2>
                  <p className="text-base leading-7 mb-4">
                    To use certain features of our platform, you must register for an account. You agree to:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Provide accurate and complete information</li>
                    <li>Maintain the security of your account credentials</li>
                    <li>Promptly update any information that changes</li>
                    <li>Accept responsibility for all activities under your account</li>
                  </ul>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">3. Platform Usage and Restrictions</h2>
                  <p className="text-base leading-7 mb-4">
                    When using M-Duka's platform, you agree not to:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Violate any applicable laws or regulations</li>
                    <li>Infringe on intellectual property rights</li>
                    <li>Upload malicious code or content</li>
                    <li>Attempt to gain unauthorized access to our systems</li>
                    <li>Use the platform for any illegal activities</li>
                  </ul>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">4. Intellectual Property Rights</h2>
                  <p className="text-base leading-7">
                    The platform, including all content, features, and functionality, is owned by M-Duka 
                    and is protected by international copyright, trademark, and other intellectual property laws.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">5. Payments and Billing</h2>
                  <p className="text-base leading-7">
                    If you choose a paid subscription or service, you agree to pay all fees in accordance 
                    with the pricing and terms displayed to you.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">6. Termination</h2>
                  <p className="text-base leading-7">
                    We reserve the right to terminate or suspend access to our services immediately, 
                    without prior notice or liability, for any reason whatsoever.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">7. Changes to Terms</h2>
                  <p className="text-base leading-7">
                    We reserve the right to modify these terms at any time. We will notify you of any 
                    material changes by posting the new Terms on this page.
                  </p>
                </section>

                <section className="mt-12 p-6 bg-muted rounded-lg">
                  <h2 className="text-xl font-semibold mb-4">Contact Us</h2>
                  <p className="text-base leading-7">
                    If you have any questions about these Terms, please contact <NAME_EMAIL>
                  </p>
                </section>
              </div>
              
              <div className="mt-8 flex justify-center">
                <Link to="/" className="text-primary hover:underline">
                  Return to Home
                </Link>
              </div>
            </div>
          </Container>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default TermsOfService;
