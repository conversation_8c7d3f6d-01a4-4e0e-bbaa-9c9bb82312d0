
import { Container } from "@/components/ui/container";
import HeroText from "./hero/HeroText";
import PhoneMockups from "./hero/PhoneMockups";
import TrustedBy from "./hero/TrustedBy";
import HeroBackground from "./hero/HeroBackground";
import { motion } from "framer-motion";

const Hero = () => {
  return (
    <section className="relative min-h-screen pt-24 pb-20 overflow-hidden">
      <HeroBackground />
      
      <Container className="px-4 md:px-6 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 md:gap-16 items-center relative">
          {/* Left column - text content */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="relative z-20"
          >
            <HeroText />
          </motion.div>
          
          {/* Right column - mobile phone mockups */}
          <div className="relative z-10">
            <PhoneMockups />
          </div>
        </div>
        
        {/* Demo Video Teaser */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-20 mb-20 text-center"
        >
          <div className="inline-flex items-center rounded-full bg-gradient-to-r from-green-100 to-blue-50 px-4 py-1.5 text-sm font-medium text-green-700 mb-6 shadow-sm border border-green-200/50">
            <span className="flex h-2 w-2 rounded-full bg-green-500 mr-1.5 animate-pulse"></span>
            <span>See M-Duka in Action</span>
          </div>
          
          <div className="relative max-w-4xl mx-auto aspect-video rounded-2xl overflow-hidden shadow-2xl border-8 border-white">
            <div className="absolute inset-0 bg-gradient-to-r from-green-500 to-blue-500 flex items-center justify-center">
              {/* This would be a video in production */}
              <div className="text-white text-center p-8">
                <h3 className="text-2xl font-bold mb-2">Watch How M-Duka Works</h3>
                <p className="mb-6">See how easy it is to set up your WhatsApp mobile shop</p>
                <motion.button 
                  className="bg-white text-green-600 rounded-full h-16 w-16 flex items-center justify-center mx-auto shadow-lg"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polygon points="5 3 19 12 5 21 5 3"></polygon>
                  </svg>
                </motion.button>
              </div>
            </div>
            
            {/* Overlay gradient */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent pointer-events-none"></div>
            
            {/* Corner decorations */}
            <div className="absolute top-4 right-4 w-16 h-16 bg-white/20 rounded-full blur-xl"></div>
            <div className="absolute bottom-4 left-4 w-16 h-16 bg-white/20 rounded-full blur-xl"></div>
          </div>
        </motion.div>
        
        {/* Trusted By Section */}
        <TrustedBy />
      </Container>
    </section>
  );
};

export default Hero;
