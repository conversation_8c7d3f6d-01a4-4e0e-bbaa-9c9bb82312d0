
/**
 * This module provides a minimal implementation of prop-types compatibility.
 * It's used as an alias for the prop-types package to avoid dependency issues.
 */

// Basic type checking functions
const PropTypes = {
  // Primitive types
  array: createType<PERSON><PERSON><PERSON>('array'),
  bool: createType<PERSON><PERSON><PERSON>('boolean'),
  func: createType<PERSON><PERSON><PERSON>('function'),
  number: createType<PERSON><PERSON><PERSON>('number'),
  object: createType<PERSON><PERSON><PERSON>('object'),
  string: createType<PERSON>he<PERSON>('string'),
  symbol: createType<PERSON><PERSON><PERSON>('symbol'),
  
  // React specific types
  node: createType<PERSON><PERSON><PERSON>('node'),
  element: createType<PERSON>he<PERSON>('element'),
  elementType: createType<PERSON><PERSON><PERSON>('elementType'),
  
  // Instance types
  instanceOf: () => createTypeChecker('instanceOf'),
  
  // Union and intersection types
  oneOf: () => createTypeChecker('oneOf'),
  oneOfType: () => createTypeChecker('oneOfType'),
  
  // Array type with shape validation
  arrayOf: () => createTypeChecker('arrayOf'),
  
  // Object types with shape validation
  objectOf: () => createType<PERSON>hecker('objectOf'),
  shape: () => createTypeChecker('shape'),
  exact: () => createTypeChecker('exact'),
  
  // Special types
  any: createTypeChecker('any')
};

// Add chainable methods for isRequired
Object.keys(PropTypes).forEach(type => {
  const typeChecker = PropTypes[type];
  if (typeof typeChecker === 'function') {
    typeChecker.isRequired = createTypeChecker(`${type}.isRequired`);
  }
});

// Helper function to create type checkers that don't actually validate in production
function createTypeChecker(type: string) {
  // In production, these will be empty functions
  const typeChecker = function() {
    return null;
  };
  
  // Add a type property for debugging purposes
  typeChecker.type = type;
  
  // Add isRequired for this type checker
  typeChecker.isRequired = typeChecker;
  
  return typeChecker;
}

// Default export for CommonJS compatibility
const exportedPropTypes = PropTypes;
export default exportedPropTypes;

// Named exports for consistency
export const array = PropTypes.array;
export const bool = PropTypes.bool;
export const func = PropTypes.func;
export const number = PropTypes.number;
export const object = PropTypes.object;
export const string = PropTypes.string;
export const symbol = PropTypes.symbol;
export const node = PropTypes.node;
export const element = PropTypes.element;
export const elementType = PropTypes.elementType;
export const instanceOf = PropTypes.instanceOf;
export const oneOf = PropTypes.oneOf;
export const oneOfType = PropTypes.oneOfType;
export const arrayOf = PropTypes.arrayOf;
export const objectOf = PropTypes.objectOf;
export const shape = PropTypes.shape;
export const exact = PropTypes.exact;
export const any = PropTypes.any;
