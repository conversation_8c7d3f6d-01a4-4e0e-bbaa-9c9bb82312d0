import { supabase } from '@/integrations/supabase/client';
import { Store, StoreFormData } from '@/types/store';
import { toast } from 'sonner';
import { formatStoreFromDb, formatStoreForDb } from '../utils/formatters';
import { sanitizeObject, isValidUUID } from '@/utils/inputValidation';

export const createStoreInDb = async (storeData: StoreFormData, userId: string): Promise<Store> => {
  try {
    // Validate user ID
    if (!isValidUUID(userId)) {
      throw new Error('Invalid user ID format');
    }

    // Sanitize the storeData directly without changing its type
    const dbData = formatStoreForDb(storeData, userId);
    console.log('Creating store with data:', dbData);

    // Ensure the owner_id is explicitly set to the current user's ID for security
    const storePayload = {
      name: dbData.name?.substring(0, 255) || 'Untitled Store', // Limit name length
      description: dbData.description?.substring(0, 1000) || '', // Limit description length
      owner_id: userId, // Always use the authenticated user's ID
      store_url: dbData.store_url?.substring(0, 100) || 'default-store',
      store_type: dbData.store_type || 'retail',
      category: dbData.category || 'general',
      is_active: true,
      payment_methods: Array.isArray(dbData.payment_methods) ? dbData.payment_methods : [],
      notifications: typeof dbData.notifications === 'object' ? dbData.notifications : { email: true, sms: false },
      theme: { primaryColor: '#FFFFFF', secondaryColor: '#000000' },
      logo_url: null,
      domain: null,
      slug: null,
      whatsapp_number: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('stores')
      .insert(storePayload)
      .select('*')
      .single();

    if (error) {
      console.error('Error creating store:', error);
      throw error;
    }

    toast.success('Store created successfully');
    return formatStoreFromDb(data);
  } catch (error: any) {
    console.error('Error creating store:', error);
    toast.error(error.message || 'Failed to create store');
    throw error;
  }
};
