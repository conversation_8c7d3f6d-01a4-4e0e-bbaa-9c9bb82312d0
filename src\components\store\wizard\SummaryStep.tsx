
import React from 'react';
import { StoreFormData } from '@/types/store';
import { Card, CardContent } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Store, Palette, Globe, CreditCard, Mail, Tag, Home, Eye } from 'lucide-react';
import { StorePreview } from '@/components/store/preview/StorePreview';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface SummaryStepProps {
  data: StoreFormData;
}

const SummaryStep: React.FC<SummaryStepProps> = ({ data }) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Review Your Store</h2>
          <p className="text-muted-foreground">
            Check all the details below before creating your store.
          </p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Eye className="h-4 w-4" />
              Preview Store
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Store Preview</DialogTitle>
            </DialogHeader>
            <StorePreview data={data} />
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="space-y-6">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-start">
              <Store className="h-5 w-5 mt-0.5 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold">Basic Information</h3>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Store Name:</span>
                    <span className="col-span-2 font-medium">{data.name}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Description:</span>
                    <span className="col-span-2">{data.description}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Store URL:</span>
                    <span className="col-span-2">
                      <span className="text-muted-foreground">m-duka.app/</span>
                      <span className="font-medium">{data.storeUrl}</span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex items-start">
              <Tag className="h-5 w-5 mt-0.5 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold">Type & Category</h3>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Store Type:</span>
                    <span className="col-span-2 capitalize">{data.storeType || 'Physical'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Category:</span>
                    <span className="col-span-2">{data.category || 'General'}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex items-start">
              <Palette className="h-5 w-5 mt-0.5 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold">Theme</h3>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Theme:</span>
                    <span className="col-span-2 capitalize">{data.themeId || 'Default'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Dark Mode:</span>
                    <span className="col-span-2">{data.themeOptions?.darkMode ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Layout:</span>
                    <span className="col-span-2 capitalize">{data.themeOptions?.layoutType || 'Grid'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Color Scheme:</span>
                    <span className="col-span-2 capitalize">{data.themeOptions?.colorScheme || 'Green'}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex items-start">
              <CreditCard className="h-5 w-5 mt-0.5 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold">Payment Methods</h3>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Accepted Methods:</span>
                    <div className="col-span-2">
                      {data.paymentMethods && data.paymentMethods.length > 0 ? (
                        <ul className="list-disc pl-5">
                          {data.paymentMethods.map((method, index) => (
                            <li key={index} className="capitalize">{method.replace(/-/g, ' ')}</li>
                          ))}
                        </ul>
                      ) : (
                        <span>No payment methods selected</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="flex items-start">
              <Mail className="h-5 w-5 mt-0.5 mr-3 text-primary" />
              <div>
                <h3 className="font-semibold">Notifications</h3>
                <div className="grid grid-cols-1 gap-2 mt-2">
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="col-span-2">{data.notificationsEmail || 'Not set'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Order Notifications:</span>
                    <span className="col-span-2">{data.notifications?.orderNotifications ? 'Enabled' : 'Disabled'}</span>
                  </div>
                  <div className="grid grid-cols-3 gap-2">
                    <span className="text-muted-foreground">Stock Alerts:</span>
                    <span className="col-span-2">{data.notifications?.stockNotifications ? 'Enabled' : 'Disabled'}</span>
                  </div>
                </div>
              </div>
            </div>
            
            <Separator className="my-4" />
            
            <div className="bg-muted p-4 rounded-md">
              <div className="flex items-center">
                <Home className="h-5 w-5 mr-2 text-primary" />
                <h3 className="font-semibold">Your Store URL will be:</h3>
              </div>
              <div className="mt-2 text-center">
                <p className="text-lg font-semibold">
                  <span className="text-muted-foreground">https://m-duka.app/</span>
                  <span>{data.storeUrl}</span>
                </p>
                <p className="text-sm text-muted-foreground mt-1">
                  You'll be able to add a custom domain after creating your store.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SummaryStep;
