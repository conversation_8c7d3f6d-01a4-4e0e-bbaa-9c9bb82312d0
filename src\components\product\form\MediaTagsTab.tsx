import React, { useState } from 'react';
import { useFormContext } from 'react-hook-form';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X, Sparkles, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { generateAIContent } from '@/services/aiGenerationService';
import ProductImageUpload from '../ProductImageUpload';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';

const MediaTagsTab: React.FC = () => {
  const form = useFormContext();
  const [tagInput, setTagInput] = useState('');
  const [isGeneratingTags, setIsGeneratingTags] = useState(false);

  const handleAddTag = () => {
    if (!tagInput.trim()) return;
    
    const currentTags = form.getValues('tags') || [];
    const normalizedTag = tagInput.trim().toLowerCase();
    
    // Avoid adding duplicate tags
    if (!currentTags.includes(normalizedTag)) {
      // Check if adding would exceed max tags
      if (currentTags.length >= 10) {
        form.setError('tags', { 
          type: 'manual', 
          message: 'Maximum 10 tags allowed' 
        });
        return;
      }
      form.setValue('tags', [...currentTags, normalizedTag]);
      form.clearErrors('tags');
    }
    
    setTagInput('');
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags') || [];
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove));
    form.clearErrors('tags');
  };

  const handleTagInputKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      handleAddTag();
    }
  };

  const generateTagSuggestions = async () => {
    const productName = form.getValues('name');
    const productDescription = form.getValues('description');
    const productCategory = form.getValues('category');
    
    if (!productName && !productDescription) {
      toast.error('Please add a product name or description first to generate tags');
      return;
    }
    
    setIsGeneratingTags(true);
    
    try {
      // Create a context prompt using available product information
      const context = [
        `Product Name: ${productName || 'Not specified'}`,
        `Category: ${productCategory || 'Not specified'}`,
        `Description: ${productDescription || 'Not specified'}`
      ].join('\n');
      
      const response = await generateAIContent({
        prompt: "Generate 5-8 relevant tags for this product",
        context,
        type: 'general'
      });
      
      if (response.isError) {
        toast.error('Failed to generate tags');
        return;
      }
      
      // Parse the generated content into individual tags
      const suggestedTags = response.content
        .split(/[,\n]/) // Split by comma or newline
        .map(tag => tag.trim().toLowerCase())
        .filter(tag => tag && !tag.includes(':') && tag.length > 1) // Remove empty or invalid tags
        .slice(0, 8); // Take max 8 tags
      
      // Add tags that don't already exist
      const currentTags = form.getValues('tags') || [];
      const newTags = suggestedTags.filter(tag => !currentTags.includes(tag));
      
      // Check if we'd exceed the maximum
      if (currentTags.length + newTags.length > 10) {
        const availableSlots = 10 - currentTags.length;
        newTags.splice(availableSlots); // Truncate to available slots
      }
      
      if (newTags.length > 0) {
        form.setValue('tags', [...currentTags, ...newTags]);
        toast.success(`Added ${newTags.length} suggested tags`);
      } else {
        toast.info('No new tags to add');
      }
    } catch (error) {
      console.error('Error generating tags:', error);
      toast.error('Failed to generate tags');
    } finally {
      setIsGeneratingTags(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Product Images */}
      <FormField
        control={form.control}
        name="images"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Product Images (Optional)</FormLabel>
            <FormControl>
              <ProductImageUpload 
                images={field.value || []} 
                onChange={field.onChange}
              />
            </FormControl>
            <FormDescription>
              Upload up to 5 images (max 5MB each)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      {/* Tags */}
      <FormField
        control={form.control}
        name="tags"
        render={({ field }) => (
          <FormItem>
            <div className="flex justify-between items-center">
              <FormLabel>Tags (Optional)</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="h-7 gap-1"
                    onClick={(e) => {
                      e.preventDefault(); 
                      generateTagSuggestions();
                    }}
                    disabled={isGeneratingTags}
                  >
                    {isGeneratingTags ? (
                      <>
                        <Loader2 className="h-3.5 w-3.5 animate-spin" />
                        <span className="text-xs">Generating...</span>
                      </>
                    ) : (
                      <>
                        <Sparkles className="h-3.5 w-3.5" />
                        <span className="text-xs">Suggest Tags</span>
                      </>
                    )}
                  </Button>
                </PopoverTrigger>
              </Popover>
            </div>
            <div className="flex">
              <FormControl>
                <Input 
                  placeholder="Add tags (press Enter or comma to add)" 
                  value={tagInput}
                  onChange={(e) => setTagInput(e.target.value)}
                  onKeyDown={handleTagInputKeyPress}
                  onBlur={handleAddTag}
                />
              </FormControl>
              <Button 
                type="button" 
                onClick={handleAddTag}
                className="ml-2"
              >
                Add
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {(field.value || []).map((tag: string, index: number) => (
                <Badge key={index} variant="secondary" className="px-2 py-1">
                  {tag}
                  <button 
                    type="button" 
                    onClick={() => handleRemoveTag(tag)}
                    className="ml-1 text-muted-foreground hover:text-foreground"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              ))}
            </div>
            <FormDescription>
              Add up to 10 descriptive tags to help customers find your product
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};

export default MediaTagsTab;
