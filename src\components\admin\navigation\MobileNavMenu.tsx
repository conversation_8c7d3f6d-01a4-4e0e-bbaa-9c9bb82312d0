
import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON><PERSON>lert, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import MobileNavLink from './MobileNavLink';

interface MobileNavMenuProps {
  isActive: (path: string) => boolean;
}

const MobileNavMenu: React.FC<MobileNavMenuProps> = ({ isActive }) => {
  return (
    <Sheet>
      <SheetTrigger asChild className="md:hidden mr-2">
        <Button variant="ghost" size="icon">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[240px] sm:w-[300px]">
        <div className="px-2 py-6">
          <Link to="/admin" className="flex items-center gap-2 mb-8">
            <ShieldAlert className="h-6 w-6 text-amber-500" />
            <span className="font-bold text-xl">Admin Panel</span>
          </Link>
          <nav className="flex flex-col gap-4 text-sm font-medium">
            <MobileNavLink href="/admin" label="Dashboard" active={isActive('/admin')} />
            <MobileNavLink href="/admin/users" label="Users" active={isActive('/admin/users')} />
            <MobileNavLink href="/admin/stores" label="Stores" active={isActive('/admin/stores')} />
            <MobileNavLink href="/admin/orders" label="Orders" active={isActive('/admin/orders')} />
            <MobileNavLink href="/admin/settings" label="Settings" active={isActive('/admin/settings')} />
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default MobileNavMenu;
