
import { supabase } from '@/integrations/supabase/client';
import { Order, OrderStatus, Transaction } from '@/types/order';
import { toast } from 'sonner';

// Convert database order to frontend model
export const formatOrderFromDb = (dbOrder: any): Order => ({
  id: dbOrder.id,
  store_id: dbOrder.store_id,
  user_id: dbOrder.user_id,
  status: dbOrder.status as OrderStatus,
  total_amount: dbOrder.total_amount,
  currency: dbOrder.currency,
  payment_method: dbOrder.payment_method,
  items: dbOrder.items,
  shipping_address: dbOrder.shipping_address,
  billing_address: dbOrder.billing_address,
  created_at: new Date(dbOrder.created_at),
  updated_at: new Date(dbOrder.updated_at)
});

// Convert database transaction to frontend model
export const formatTransactionFromDb = (dbTransaction: any): Transaction => ({
  id: dbTransaction.id,
  orderId: dbTransaction.order_id,
  userId: dbTransaction.user_id,
  amount: dbTransaction.amount,
  currency: dbTransaction.currency,
  paymentMethod: dbTransaction.payment_method,
  status: dbTransaction.status,
  transactionId: dbTransaction.transaction_id || null,
  transactionReference: dbTransaction.transaction_reference || null,
  paymentData: dbTransaction.payment_data,
  createdAt: new Date(dbTransaction.created_at),
  updatedAt: new Date(dbTransaction.updated_at)
});

export const fetchOrdersByStore = async (storeId: string): Promise<Order[]> => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('store_id', storeId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(formatOrderFromDb);
  } catch (error) {
    console.error('Error fetching orders:', error);
    toast.error('Failed to fetch orders');
    return [];
  }
};

export const updateOrderStatusInDb = async (orderId: string, status: OrderStatus): Promise<void> => {
  try {
    const { error } = await supabase
      .from('orders')
      .update({ status })
      .eq('id', orderId);

    if (error) throw error;
  } catch (error) {
    console.error('Error updating order status:', error);
    toast.error('Failed to update order status');
    throw error;
  }
};

export const fetchOrderById = async (orderId: string): Promise<Order | null> => {
  try {
    const { data, error } = await supabase
      .from('orders')
      .select('*')
      .eq('id', orderId)
      .single();

    if (error) throw error;
    
    return formatOrderFromDb(data);
  } catch (error) {
    console.error('Error fetching order:', error);
    toast.error('Failed to fetch order details');
    return null;
  }
};

export const fetchOrderTransactions = async (orderId: string): Promise<Transaction[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('order_id', orderId);

    if (error) throw error;
    
    return data.map(formatTransactionFromDb);
  } catch (error) {
    console.error('Error fetching order transactions:', error);
    toast.error('Failed to fetch payment information');
    return [];
  }
};
