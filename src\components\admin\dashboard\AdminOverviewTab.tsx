
import React from 'react';
import { Activity, Users, ShoppingBag, CreditCard } from 'lucide-react';
import MetricCard from './MetricCard';
import RevenueChart from './charts/RevenueChart';
import UserGrowthChart from './charts/UserGrowthChart';
import OrdersByStoreChart from './charts/OrdersByStoreChart';
import { Skeleton } from '@/components/ui/skeleton';

interface AdminOverviewTabProps {
  metrics: {
    totalUsers: number;
    totalStores: number;
    totalOrders: number;
    totalRevenue: number;
  };
  revenueData: { name: string; total: number }[];
  userGrowthData: { name: string; users: number }[];
  ordersByStoreData: { name: string; orders: number }[];
  formatCurrency: (amount: number) => string;
  isLoading: boolean;
}

const AdminOverviewTab: React.FC<AdminOverviewTabProps> = ({
  metrics,
  revenueData,
  userGrowthData,
  ordersByStoreData,
  formatCurrency,
  isLoading
}) => {
  // Helper to calculate change percentage (for demo purposes)
  const getChangePercentage = (value: number): { change: string; trend: 'up' | 'down' | 'neutral' } => {
    if (value === 0) return { change: '0%', trend: 'neutral' };
    return { change: '+0%', trend: 'neutral' };
  };

  if (isLoading) {
    return (
      <div className="grid gap-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map((index) => (
            <div key={index} className="p-6 rounded-lg border bg-card">
              <Skeleton className="h-10 w-32 mb-2" />
              <Skeleton className="h-6 w-24 mb-4" />
              <Skeleton className="h-4 w-20" />
            </div>
          ))}
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="border rounded-lg p-6">
            <Skeleton className="h-8 w-40 mb-6" />
            <Skeleton className="h-[200px] w-full" />
          </div>
          <div className="border rounded-lg p-6">
            <Skeleton className="h-8 w-40 mb-6" />
            <Skeleton className="h-[200px] w-full" />
          </div>
        </div>
        <div className="border rounded-lg p-6">
          <Skeleton className="h-8 w-40 mb-6" />
          <Skeleton className="h-[200px] w-full" />
        </div>
      </div>
    );
  }

  // Calculate trend for the metrics (simplified for demo)
  const usersTrend = getChangePercentage(metrics.totalUsers);
  const storesTrend = getChangePercentage(metrics.totalStores);
  const ordersTrend = getChangePercentage(metrics.totalOrders);
  const revenueTrend = getChangePercentage(metrics.totalRevenue);

  // Check if data is empty (no activity yet)
  const hasNoActivity = metrics.totalUsers === 0 && 
                       metrics.totalStores === 0 && 
                       metrics.totalOrders === 0 && 
                       metrics.totalRevenue === 0;

  // Display empty state if no activity
  if (hasNoActivity) {
    return (
      <div className="text-center py-12 px-4 border rounded-lg">
        <h3 className="text-2xl font-medium mb-4">No Platform Activity Yet</h3>
        <p className="text-muted-foreground max-w-md mx-auto mb-6">
          Your platform is ready, but there's no activity to display yet. As users register, create stores, and place orders, you'll see real-time metrics here.
        </p>
        <div className="flex justify-center space-x-2 text-sm text-muted-foreground">
          <span className="flex items-center"><Users className="h-4 w-4 mr-1" /> Users: 0</span>
          <span className="flex items-center"><ShoppingBag className="h-4 w-4 mr-1" /> Stores: 0</span>
          <span className="flex items-center"><CreditCard className="h-4 w-4 mr-1" /> Orders: 0</span>
        </div>
      </div>
    );
  }

  return (
    <div className="grid gap-6">
      {/* KPI Cards Row */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6">
        <MetricCard 
          title="Total Users" 
          value={metrics.totalUsers.toString()} 
          description="Registered users"
          icon={<Users className="h-4 w-4 text-blue-500" />}
          change={usersTrend.change}
          trend={usersTrend.trend}
        />
        <MetricCard 
          title="Total Stores" 
          value={metrics.totalStores.toString()} 
          description="Active stores"
          icon={<ShoppingBag className="h-4 w-4 text-amber-500" />}
          change={storesTrend.change}
          trend={storesTrend.trend}
        />
        <MetricCard 
          title="Total Orders" 
          value={metrics.totalOrders.toString()} 
          description="Processed orders"
          icon={<CreditCard className="h-4 w-4 text-green-500" />}
          change={ordersTrend.change}
          trend={ordersTrend.trend}
        />
        <MetricCard 
          title="Total Revenue" 
          value={formatCurrency(metrics.totalRevenue)} 
          description="Platform earnings"
          icon={<Activity className="h-4 w-4 text-purple-500" />}
          change={revenueTrend.change}
          trend={revenueTrend.trend}
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <RevenueChart data={revenueData} formatCurrency={formatCurrency} />
        <UserGrowthChart data={userGrowthData} />
      </div>

      {/* Bottom Chart */}
      <OrdersByStoreChart data={ordersByStoreData} />
    </div>
  );
};

export default AdminOverviewTab;
