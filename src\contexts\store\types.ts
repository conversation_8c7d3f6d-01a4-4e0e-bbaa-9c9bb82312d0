
import { Store, StoreFormData } from '@/types/store';
import { Store as DatabaseStore } from '@/types/database';
import { ReactNode } from 'react';

export interface StoreContextType {
  currentStore: DatabaseStore | null;
  stores: DatabaseStore[];
  isLoading: boolean;
  loading: boolean;
  error: Error | null;
  createStore: (storeData: any) => Promise<DatabaseStore>;
  updateStore: (storeId: string, storeData: any) => Promise<DatabaseStore>;
  deleteStore: (storeId: string) => Promise<void>;
  fetchStore: (storeId: string) => Promise<void>;
  fetchStoreByUrl: (url: string) => Promise<void>;
  setCurrentStore: (store: DatabaseStore | null) => void;
  getCurrentUIStore: () => Store | null;
}

export interface StoreProviderProps {
  children: ReactNode;
}
