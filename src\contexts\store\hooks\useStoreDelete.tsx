
import { useState } from 'react';
import { Store } from '@/types/store';
import { deleteStoreFromDb } from '../api/deleteStore';

type UseStoreDeleteProps = {
  user: any;
  stores: Store[];
  setStores: React.Dispatch<React.SetStateAction<Store[]>>;
  currentStore: Store | null;
  setCurrentStore: React.Dispatch<React.SetStateAction<Store | null>>;
};

export const useStoreDelete = ({
  user,
  stores,
  setStores,
  currentStore,
  setCurrentStore
}: UseStoreDeleteProps) => {
  const [isDeleting, setIsDeleting] = useState<boolean>(false);

  const deleteStore = async (storeId: string): Promise<void> => {
    if (!user) throw new Error('You must be logged in to delete a store');
    
    setIsDeleting(true);
    try {
      await deleteStoreFromDb(storeId, user.id);

      // Update state
      const updatedStores = stores.filter(s => s.id !== storeId);
      setStores(updatedStores);
      
      // If the deleted store was the current store, update current store
      if (currentStore?.id === storeId) {
        if (updatedStores.length > 0) {
          setCurrentStore(updatedStores[0]);
          localStorage.setItem('m-duka-current-store', updatedStores[0].id || '');
        } else {
          setCurrentStore(null);
          localStorage.removeItem('m-duka-current-store');
        }
      }
    } finally {
      setIsDeleting(false);
    }
  };

  return { deleteStore, isDeleting };
};
