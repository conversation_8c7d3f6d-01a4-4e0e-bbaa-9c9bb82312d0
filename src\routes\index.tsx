import { createBrowserRouter } from "react-router-dom";
import NotFound from '@/pages/NotFound';
import { publicRoutes } from './public.routes';
import { storeOwnerRoutes } from './store-owner.routes';
import { adminRoutes } from './admin.routes';
import { storefrontRoutes } from './storefront.routes';
import { isStoreSubdomain, isMainDomain, logDomainInfo } from "@/utils/authRedirects";
import Index from "@/pages/Index"; // Import the Index component directly

// Get environment information
const hostname = typeof window !== 'undefined' ? window.location.hostname : '';
const pathname = typeof window !== 'undefined' ? window.location.pathname : '';
const isStorefront = typeof window !== 'undefined' ? isStoreSubdomain() : false;
const isMain = typeof window !== 'undefined' ? isMainDomain() : true; // Default to main for SSR
const isDevelopment = process.env.NODE_ENV === 'development'; 
const isPreview = hostname.includes('lovable.app') || 
                  hostname.includes('lovableproject.com') || 
                  hostname.includes('preview--');

// Enhanced debug information
console.log("Router configuration loading:", {
  hostname,
  pathname,
  isStorefront,
  isMain,
  isDevelopment,
  isPreview,
  environment: process.env.NODE_ENV,
  publicRoutes: publicRoutes.length,
  storeOwnerRoutes: storeOwnerRoutes.length,
  adminRoutes: adminRoutes.length,
  storefrontRoutes: storefrontRoutes.length,
});

// Record detailed domain info in console
if (typeof window !== 'undefined') {
  logDomainInfo();
}

// Create routes configuration - simplified to always show main site for root
let routerRoutes = [
  {
    path: "/",
    element: <Index />,
  },
  ...publicRoutes.filter(route => route.path !== "/"), // Add other public routes except home
  ...storeOwnerRoutes,
  ...adminRoutes,
];

// Find the storefront layout route
const storefrontLayoutRoute = storefrontRoutes.find(route => route.path === "/");

// Add the shop namespace route with the proper structure
if (storefrontLayoutRoute && storefrontLayoutRoute.children) {
  routerRoutes.push({
    path: "/shop",
    element: storefrontLayoutRoute.element,
    children: storefrontLayoutRoute.children,
  } as any);
}

// Find the account route and add it with the shop prefix
const accountRoute = storefrontRoutes.find(route => route.path === "/account");
if (accountRoute && accountRoute.children) {
  routerRoutes.push({
    path: "/shop/account",
    element: accountRoute.element,
    children: accountRoute.children,
  } as any);
}

// Add the catch-all route at the end
routerRoutes.push({
  path: "*",
  element: <NotFound />,
});

// Create the router with our routes configuration
const router = createBrowserRouter(routerRoutes);

console.log("Router configuration loaded. Routes applied:", isStorefront ? "STOREFRONT ROUTES" : "MAIN SITE ROUTES");
console.log("Final router routes:", router.routes.map(r => r.path));

export { router };
