
import React from 'react';
import { Helmet } from 'react-helmet-async';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Line<PERSON>hart, <PERSON><PERSON>hart, FileBarChart, AlertCircle } from 'lucide-react';

const Reports: React.FC = () => {
  return (
    <DashboardLayout>
      <Helmet>
        <title>Reports - m-duka</title>
      </Helmet>
      
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Reports & Analytics</h1>
          <Button disabled className="flex items-center gap-2">
            <FileBarChart className="h-4 w-4" />
            Export Reports
          </Button>
        </div>
        
        <Tabs defaultValue="overview">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="sales">Sales</TabsTrigger>
            <TabsTrigger value="inventory">Inventory</TabsTrigger>
            <TabsTrigger value="customers">Customers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Analytics dashboard is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Reports Overview</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    We're working on a comprehensive analytics dashboard to help you track your store's performance and make data-driven decisions.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl">
                    <ReportCard 
                      icon={<BarChart className="h-8 w-8" />}
                      title="Sales Analytics"
                      description="Track revenue, top products, and sales trends"
                    />
                    <ReportCard 
                      icon={<LineChart className="h-8 w-8" />}
                      title="Growth Metrics"
                      description="Monitor your store's performance over time"
                    />
                    <ReportCard 
                      icon={<PieChart className="h-8 w-8" />}
                      title="Customer Insights"
                      description="Understand your audience better"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="sales" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Sales reports feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Sales Reports</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    Detailed analytics about your sales performance will be available soon.
                  </p>
                  <Button variant="outline" disabled>
                    Check Back Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="inventory" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Inventory reports feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Inventory Reports</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    Track stock levels, product performance, and inventory turnover.
                  </p>
                  <Button variant="outline" disabled>
                    Check Back Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="customers" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Customer reports feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Customer Reports</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    Analyze customer behavior, demographics, and purchase patterns.
                  </p>
                  <Button variant="outline" disabled>
                    Check Back Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

interface ReportCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const ReportCard: React.FC<ReportCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-accent/50 rounded-lg p-4 flex flex-col items-center text-center">
      <div className="mb-2 text-primary">{icon}</div>
      <h4 className="font-medium mb-1">{title}</h4>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
};

export default Reports;
