
-- Create store logos storage bucket
INSERT INTO storage.buckets (id, name, public)
VALUES ('store-logos', 'store-logos', true);

-- Create RLS policy for store logo uploads
CREATE POLICY "Store owners can upload their logo" 
ON storage.objects FOR INSERT 
WITH CHECK (
  auth.uid() = (SELECT owner_id FROM stores WHERE id = split_part(REPLACE(object_id, 'store-logos/', ''), '/', 1))
);

CREATE POLICY "Logos are publicly accessible" 
ON storage.objects FOR SELECT 
USING (bucket_id = 'store-logos');
