import React from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, Trash2 } from 'lucide-react';
import { Product } from '@/types/unified-product';
import { formatCurrency } from '@/utils/formatters';

interface ProductCardProps {
  product: Product;
  onDelete: (id: string) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onDelete }) => {
  const navigate = useNavigate();

  return (
    <Card className="h-full flex flex-col hover:shadow-md transition-shadow">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-xl">{product.name}</CardTitle>
          <Badge variant={product.is_active ? "success" : "outline"} className="ml-2">
            {product.is_active ? "Active" : "Inactive"}
          </Badge>
        </div>
        <div className="text-sm text-muted-foreground">
          SKU: {product.sku || 'N/A'}
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="mb-2">
          <p className="font-bold text-lg">
            {formatCurrency(product.price, product.currency)}
          </p>
          <p className="text-sm text-muted-foreground">
            Stock: {product.stock_quantity || 0} units
          </p>
        </div>
        
        {product.description && (
          <p className="text-sm line-clamp-3 mb-2">{product.description}</p>
        )}
        
        {product.category && (
          <div className="mb-2">
            <Badge variant="outline" className="mr-1">
              {product.category}
            </Badge>
          </div>
        )}
        
        {product.tags && product.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {product.tags.map((tag, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="pt-2 border-t flex justify-between">
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => navigate(`/products/${product.id}/edit`)}
        >
          <Edit className="h-4 w-4 mr-1" />
          Edit
        </Button>
        <Button 
          variant="outline" 
          size="sm"
          className="text-destructive hover:text-destructive"
          onClick={() => onDelete(product.id || '')}
        >
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ProductCard;
