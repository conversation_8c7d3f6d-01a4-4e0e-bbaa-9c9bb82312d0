
import { UserRole } from '@/constants/roles';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import AdminDashboard from '@/pages/AdminDashboard';

export const adminRoutes = [
  {
    path: "/admin",
    element: <ProtectedRoute requiredRoles={[UserRole.Admin]}><AdminDashboard /></ProtectedRoute>,
  },
  {
    path: "/admin/users",
    element: <ProtectedRoute requiredRoles={[UserRole.Admin]}><AdminDashboard /></ProtectedRoute>,
  },
  {
    path: "/admin/stores",
    element: <ProtectedRoute requiredRoles={[UserRole.Admin]}><AdminDashboard /></ProtectedRoute>,
  },
  {
    path: "/admin/orders",
    element: <ProtectedRoute requiredRoles={[UserRole.Admin]}><AdminDashboard /></ProtectedRoute>,
  },
  {
    path: "/admin/settings",
    element: <ProtectedRoute requiredRoles={[UserRole.Admin]}><AdminDashboard /></ProtectedRoute>,
  },
];

