
import React from 'react';
import { toast } from 'sonner';
import { useStore, useAuth } from '@/contexts';
import { useStoreTabState } from '@/hooks/settings/useStoreTabState';
import WhatsAppSettingsCard from '@/components/settings/general/WhatsAppSettingsCard';
import { Heading } from '@/components/ui/heading';
import { useNavigate } from 'react-router-dom';
import { databaseToUIStore } from '@/utils/typeConverters';
import { Store } from '@/types/store';

const WhatsAppSettings = () => {
  const { user } = useAuth();
  const { currentStore, updateStore } = useStore();
  const [loading, setLoading] = React.useState(false);
  const navigate = useNavigate();
  
  // Convert database store to UI store format for compatibility
  // Use type assertion to fix the type error
  const uiStore = currentStore ? databaseToUIStore(currentStore) as Store : null;
  
  const storeState = useStoreTabState(uiStore);
  
  // Redirect if no user
  React.useEffect(() => {
    if (!user) {
      navigate('/signin');
    }
  }, [user, navigate]);
  
  const handleSubmit = async () => {
    setLoading(true);
    
    try {
      // Update only WhatsApp settings
      await updateStore(currentStore?.id || '', {
        whatsapp_settings: {
          enabled: true,
          number: storeState.whatsappNumber,
          message: storeState.customMessage || 'Thank you for your order!',
          businessNumber: storeState.whatsappNumber,
          enableOrderNotifications: storeState.enableOrderNotifications,
          enableCustomerUpdates: storeState.enableCustomerUpdates,
          customMessage: storeState.customMessage,
          autoReply: storeState.autoReply
        }
      });
      
      toast.success('WhatsApp settings updated successfully!');
    } catch (error) {
      toast.error('Failed to update WhatsApp settings');
      console.error('WhatsApp settings update error:', error);
    } finally {
      setLoading(false);
    }
  };
  
  return (
    <div className="max-w-5xl space-y-6">
      <Heading title="WhatsApp Business Settings" description="Configure WhatsApp for order management and customer communication" />
      
      <WhatsAppSettingsCard
        whatsappNumber={storeState.whatsappNumber}
        setWhatsappNumber={storeState.setWhatsappNumber}
        phoneNumber={storeState.phoneNumber}
        enableOrderNotifications={storeState.enableOrderNotifications}
        setEnableOrderNotifications={storeState.setEnableOrderNotifications}
        enableCustomerUpdates={storeState.enableCustomerUpdates}
        setEnableCustomerUpdates={storeState.setEnableCustomerUpdates}
        customMessage={storeState.customMessage}
        setCustomMessage={storeState.setCustomMessage}
        autoReply={storeState.autoReply}
        setAutoReply={storeState.setAutoReply}
        onSave={handleSubmit}
        isSaving={loading}
      />
    </div>
  );
};

export default WhatsAppSettings;
