
import React, { useState } from 'react';
import { ProductVariant } from '@/types/unified-product';
import VariantsTable from './variants/VariantsTable';
import NewVariantForm from './variants/NewVariantForm';
import { generateSku } from '@/utils/skuGenerator';

interface ProductVariantsProps {
  variants: ProductVariant[];
  onChange: (variants: ProductVariant[]) => void;
  currency: string;
  basePrice: number;
  baseSku?: string;
}

const ProductVariants: React.FC<ProductVariantsProps> = ({
  variants,
  onChange,
  currency,
  basePrice,
  baseSku
}) => {
  const addVariant = (newVariant: ProductVariant) => {
    const updatedVariant = {
      ...newVariant,
      sku: newVariant.sku || generateSku(baseSku, newVariant.size, newVariant.color)
    };
    
    onChange([...variants, updatedVariant]);
  };

  const removeVariant = (index: number) => {
    const newVariants = [...variants];
    newVariants.splice(index, 1);
    onChange(newVariants);
  };

  const updateVariant = (index: number, field: keyof ProductVariant, value: any) => {
    const newVariants = [...variants];
    
    // If changing size or color, we should regenerate the SKU if it was auto-generated
    if ((field === 'size' || field === 'color') && !newVariants[index].sku?.includes('-custom')) {
      const updatedVariant = {
        ...newVariants[index],
        [field]: value
      };
      newVariants[index] = {
        ...updatedVariant,
        sku: generateSku(
          baseSku,
          field === 'size' ? value : updatedVariant.size, 
          field === 'color' ? value : updatedVariant.color
        )
      };
    } else {
      newVariants[index] = {
        ...newVariants[index],
        [field]: value
      };
    }
    
    onChange(newVariants);
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground mb-2">
        Add variants for different sizes, colors, or other attributes of your product.
      </div>
      
      {variants.length > 0 ? (
        <VariantsTable 
          variants={variants}
          onUpdateVariant={updateVariant}
          onRemoveVariant={removeVariant}
        />
      ) : (
        <div className="text-center p-4 border border-dashed rounded-md text-muted-foreground">
          No variants added yet
        </div>
      )}
      
      <NewVariantForm
        initialPrice={basePrice}
        onAddVariant={addVariant}
        generateSku={(size?: string, color?: string) => generateSku(baseSku, size, color)}
      />
    </div>
  );
};

export default ProductVariants;
