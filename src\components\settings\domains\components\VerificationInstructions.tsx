
import React from 'react';
import { Button } from '@/components/ui/button';

interface VerificationInstructionsProps {
  verificationInstructions: string;
  verificationCode: string;
  onDismiss: () => void;
}

export const VerificationInstructions: React.FC<VerificationInstructionsProps> = ({
  verificationInstructions,
  verificationCode,
  onDismiss
}) => {
  return (
    <div className="bg-muted p-4 rounded-md my-4 border">
      <h3 className="font-semibold mb-2">Verify Your Domain</h3>
      <p className="text-sm mb-2">{verificationInstructions}</p>
      <div className="bg-background p-2 rounded border text-sm font-mono">
        {verificationCode}
      </div>
      <Button variant="outline" className="mt-3" onClick={onDismiss}>
        Dismiss
      </Button>
    </div>
  );
};
