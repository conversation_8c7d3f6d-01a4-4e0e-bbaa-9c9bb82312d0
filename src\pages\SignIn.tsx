import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/auth/AuthContext';
import AuthLayout from '@/components/auth/AuthLayout';
import EmailSignInForm, { EmailSignInFormValues } from '@/components/auth/EmailSignInForm';
import GoogleSignInButton from '@/components/auth/GoogleSignInButton';
import SignInFooterLinks from '@/components/auth/SignInFooterLinks';
import { Separator } from '@/components/ui/separator';

const SignIn: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, signInWithSocial } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);

  const from = location.state?.from?.pathname || '/dashboard';

  const handleEmailSignIn = async (data: EmailSignInFormValues) => {
    setIsSubmitting(true);
    try {
      const { user, error } = await login(data.email, data.password);
      
      if (error) {
        if (error.message.includes('Invalid login credentials')) {
          toast.error('Invalid email or password');
        } else if (error.message.includes('Email not confirmed')) {
          toast.error('Please check your email and click the verification link before signing in');
        } else {
          toast.error(error.message || 'Sign in failed');
        }
        return;
      }
      
      if (user) {
        toast.success('Signed in successfully!');
        navigate(from, { replace: true });
      }
    } catch (error) {
      console.error('Sign in error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    setIsGoogleLoading(true);
    try {
      const { success, url, error } = await signInWithSocial('google');
      
      if (error) {
        toast.error('Google sign in failed');
        return;
      }
      
      if (success && url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Google sign in error:', error);
      toast.error('Google sign in failed');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <AuthLayout 
      title="Welcome back" 
      subtitle="Sign in to your account to continue"
    >
      <div className="w-full max-w-md space-y-6">

        <div className="space-y-4">
          <GoogleSignInButton
            onClick={handleGoogleSignIn}
            isLoading={isGoogleLoading}
          />
          
          <div className="relative">
            <Separator />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="bg-background px-2 text-muted-foreground text-sm">
                Or continue with
              </span>
            </div>
          </div>

          <EmailSignInForm
            onSubmit={handleEmailSignIn}
            isSubmitting={isSubmitting}
            isLoading={isSubmitting}
          />
        </div>

        <SignInFooterLinks />
      </div>
    </AuthLayout>
  );
};

export default SignIn;