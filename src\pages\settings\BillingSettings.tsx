
import React from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import Billing from './Billing';
import Plans from './Plans';
import { useAuth } from '@/contexts';
import { useNavigate } from 'react-router-dom';

const BillingSettings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Redirect if no user
  React.useEffect(() => {
    if (!user) {
      navigate('/signin');
    }
  }, [user, navigate]);

  return (
    <div className="max-w-6xl">
      <h2 className="text-xl font-semibold mb-6">Billing and Plans</h2>
      
      <Tabs defaultValue="billing" className="w-full">
        <TabsList className="grid grid-cols-2 mb-6">
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="plans">Plans</TabsTrigger>
        </TabsList>
        
        <TabsContent value="billing">
          <Billing />
        </TabsContent>
        
        <TabsContent value="plans">
          <Plans />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default BillingSettings;
