
import React from 'react';
import { useStore } from '@/contexts/store/StoreContext';
import { useProduct } from '@/contexts/product/ProductContext';
import { useWishlist } from '@/contexts/wishlist/WishlistContext';
import { useCart } from '@/contexts/cart/CartContext';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Heart, ShoppingCart } from 'lucide-react';
import type { Store as FrontendStore, StoreFormData } from '@/types/store';
import type { Tables } from '@/integrations/supabase/types';
import { Product } from '@/contexts/product/types';

type DbStore = Tables<'stores'>;
import { useUIStore } from '@/hooks/useUIStore';

interface StorePreviewProps {
  data?: StoreFormData | FrontendStore | DbStore;
}

// Type guard to check if data is frontend Store type
const isFrontendStore = (data: StoreFormData | FrontendStore | DbStore): data is FrontendStore => {
  return 'owner_id' in data && 'created_at' in data && 'updated_at' in data;
};

// Type guard to check if data is database Store type
const isDbStore = (data: StoreFormData | FrontendStore | DbStore): data is DbStore => {
  return 'owner_id' in data && 'created_at' in data && 'updated_at' in data;
};

// Type guard to check if data is StoreFormData type
const isStoreFormData = (data: StoreFormData | FrontendStore | DbStore): data is StoreFormData => {
  return !isFrontendStore(data) && !isDbStore(data);
};

export const StorePreview: React.FC<StorePreviewProps> = ({ data }) => {
  const { currentStore } = useStore();
  const { products } = useProduct();
  const { addItem: addToWishlist, removeItem: removeFromWishlist, isInWishlist } = useWishlist();
  const { addToCart } = useCart();

  // Use provided data or current store data
  const storeData = data || currentStore;
  if (!storeData) return null;

  // Filter active products
  const activeProducts = products.filter(product => product.is_active);

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      {/* Store Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{storeData.name}</h1>
        <p className="text-muted-foreground">{storeData.description}</p>
      </div>

      {/* Store Theme Preview */}
      {(isFrontendStore(storeData) || isDbStore(storeData)) && (
        <div 
          className="mb-8 p-4 rounded-lg"
            style={{
              backgroundColor: '#ffffff',
              color: '#000000'
            }}
        >
          <h2 className="text-xl font-semibold mb-2">Theme Preview</h2>
          <p>Primary Color: #ffffff</p>
          <p>Secondary Color: #000000</p>
        </div>
      )}

      {/* Products Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {activeProducts.map(product => (
          <Card key={product.id} className="overflow-hidden">
            <CardHeader className="p-0">
              <div className="aspect-square relative">
                <img
                  src={product.images?.[0] || '/placeholder.png'}
                  alt={product.name}
                  className="object-cover w-full h-full"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                  onClick={() => {
                    if (isInWishlist(product.id || '')) {
                      removeFromWishlist(product.id || '');
                    } else {
                      addToWishlist(product);
                    }
                  }}
                >
                  <Heart
                    className={`h-5 w-5 ${
                      isInWishlist(product.id || '') ? 'fill-current text-red-500' : ''
                    }`}
                  />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <CardTitle className="text-lg mb-2">{product.name}</CardTitle>
              <p className="text-muted-foreground text-sm mb-4">
                {product.description}
              </p>
              <div className="flex items-center justify-between">
                <div>
                  <span className="text-lg font-semibold">
                    ${product.price.toFixed(2)}
                  </span>
                  {product.sale_price !== undefined && (
                    <span className="ml-2 text-sm text-muted-foreground line-through">
                      ${product.sale_price.toFixed(2)}
                    </span>
                  )}
                </div>
                <Button
                  size="sm"
                  onClick={() => addToCart(product, 1)}
                >
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  Add to Cart
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Store Settings Preview */}
      {(isFrontendStore(storeData) || isDbStore(storeData)) && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Store Settings</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
              </CardHeader>
              <CardContent>
                  <ul className="list-disc list-inside">
                    <li>Payment methods will be displayed here</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Notifications</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="list-disc list-inside">
                  <li>Notifications settings will be displayed here</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
};
