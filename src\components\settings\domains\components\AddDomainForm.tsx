
import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

interface AddDomainFormProps {
  newDomain: string;
  setNewDomain: (domain: string) => void;
  isDomainValid: boolean;
  loading: boolean;
  handleAddDomain: () => Promise<void>;
}

export const AddDomainForm: React.FC<AddDomainFormProps> = ({
  newDomain,
  setNewDomain,
  isDomainValid,
  loading,
  handleAddDomain
}) => {
  return (
    <div className="flex items-end gap-2">
      <div className="flex-1">
        <Label htmlFor="new-domain" className="mb-2 block">Add a new domain</Label>
        <Input
          id="new-domain"
          placeholder="yourdomain.com"
          value={newDomain}
          onChange={(e) => setNewDomain(e.target.value)}
          className={!isDomainValid && newDomain ? "border-red-300" : ""}
        />
        {!isDomainValid && newDomain && (
          <p className="text-xs text-red-500 mt-1">
            Please enter a valid domain (e.g., yourdomain.com)
          </p>
        )}
      </div>
      <Button 
        onClick={handleAddDomain} 
        disabled={loading || !newDomain || !isDomainValid}
      >
        {loading ? 'Adding...' : 
          <>
            <PlusCircle className="h-4 w-4 mr-2" />
            Add Domain
          </>
        }
      </Button>
    </div>
  );
};
