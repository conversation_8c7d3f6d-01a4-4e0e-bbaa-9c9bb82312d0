import React, { useState } from "react";
import { Container } from "@/components/ui/container";
import { MapPin, Info, Search, Home, ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";
import { useUIStore } from "@/hooks/useUIStore";

interface StoreHeaderProps {
  name?: string;
  description?: string;
  address?: string;
  location?: string;
  about?: string;
  onSearch?: (searchTerm: string) => void;
}

const StoreHeader: React.FC<StoreHeaderProps> = ({ 
  name, 
  description, 
  address, 
  location,
  about,
  onSearch
}) => {
  const isMobile = useIsMobile();
  const { currentStore } = useUIStore();
  const [searchTerm, setSearchTerm] = useState("");
  
  // If props are not provided, use data from currentStore
  const storeName = name || currentStore?.name || "Store Name";
  const storeDescription = description || currentStore?.description || "";
  
  // Handle both camelCase and snake_case property names
  const storeAddress = address || currentStore?.address || "";
  const storeLocation = location || currentStore?.location || "";
  const storeAbout = about || currentStore?.about_text || "";

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchTerm);
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Trigger search on every keystroke with debouncing effect
    if (onSearch) {
      const timeoutId = setTimeout(() => {
        onSearch(value);
      }, 300);
      
      // Clear previous timeout
      return () => clearTimeout(timeoutId);
    }
  };
  
  return (
    <div className="bg-white shadow-sm">
      {/* Store Name / Logo */}
      <div className="py-3 border-b">
        <Container>
          <div className="flex justify-center">
            <h1 className="text-2xl font-bold text-center">{storeName}</h1>
          </div>
        </Container>
      </div>
      
      {/* Location */}
      {(storeLocation || storeAddress) && (
        <div className="py-2 border-b bg-gray-50">
          <Container>
            <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
              <MapPin className="h-3.5 w-3.5" />
              <span>{storeLocation}{storeLocation && storeAddress ? ", " : ""}{storeAddress}</span>
            </div>
          </Container>
        </div>
      )}
      
      {/* About Us */}
      {storeAbout && (
        <div className="py-3 border-b">
          <Container>
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-gray-500 flex-shrink-0" />
              <p className="text-sm text-gray-600 line-clamp-2">{storeAbout}</p>
            </div>
          </Container>
        </div>
      )}
      
      {/* Search */}
      <div className="py-3 border-b">
        <Container>
          <form onSubmit={handleSearchSubmit} className="relative">
            <Input 
              type="search" 
              placeholder="Search products..." 
              value={searchTerm}
              onChange={handleSearchChange}
              className="w-full pl-9 pr-4 py-2 rounded-lg"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            {searchTerm && (
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0"
                onClick={() => {
                  setSearchTerm("");
                  if (onSearch) onSearch("");
                }}
              >
                ✕
              </Button>
            )}
          </form>
        </Container>
      </div>
      
      {/* Only show navigation on mobile */}
      {isMobile && (
        <div className="py-3">
          <Container>
            <div className="flex justify-around">
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <Home className="h-5 w-5" />
                <span className="text-xs">Home</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <Search className="h-5 w-5" />
                <span className="text-xs">Search</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <ShoppingCart className="h-5 w-5" />
                <span className="text-xs">Cart</span>
              </Button>
            </div>
          </Container>
        </div>
      )}
    </div>
  );
};

export default StoreHeader;
