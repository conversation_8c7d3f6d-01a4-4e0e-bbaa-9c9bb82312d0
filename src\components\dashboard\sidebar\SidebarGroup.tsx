
import React from 'react';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { ChevronDown } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import SidebarItem from './SidebarItem';

interface SidebarGroupProps {
  title: string;
  icon: React.ReactNode;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isActive: boolean;
  children: React.ReactNode;
}

const SidebarGroup: React.FC<SidebarGroupProps> = ({
  title,
  icon,
  isOpen,
  onOpenChange,
  isActive,
  children
}) => {
  return (
    <Collapsible
      open={isOpen}
      onOpenChange={onOpenChange}
      className="w-full"
    >
      <CollapsibleTrigger asChild>
        <Button
          variant="ghost"
          className={cn(
            "w-full justify-between font-normal px-3 py-2 h-auto",
            isActive && "bg-accent"
          )}
        >
          <div className="flex items-center">
            {icon}
            <span>{title}</span>
          </div>
          <ChevronDown
            className={cn(
              "h-4 w-4 transition-transform",
              isOpen && "rotate-180"
            )}
          />
        </Button>
      </CollapsibleTrigger>
      <CollapsibleContent className="ml-5 border-l pl-6 pt-1">
        {children}
      </CollapsibleContent>
    </Collapsible>
  );
};

export default SidebarGroup;
