import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Download, X, Smartphone } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface BeforeInstallPromptEvent extends Event {
  readonly platforms: string[];
  readonly userChoice: Promise<{
    outcome: 'accepted' | 'dismissed';
    platform: string;
  }>;
  prompt(): Promise<void>;
}

export function PWAInstallPrompt() {
  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isInstalled, setIsInstalled] = useState(false);

  useEffect(() => {
    // Check if app is already installed
    const checkIfInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
      const isIOSInstalled = (window.navigator as any).standalone === true;
      setIsInstalled(isStandalone || isIOSInstalled);
    };

    checkIfInstalled();

    // Listen for the beforeinstallprompt event
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault();
      setDeferredPrompt(e as BeforeInstallPromptEvent);
      setIsVisible(true);
    };

    // Listen for the app installed event
    const handleAppInstalled = () => {
      setIsInstalled(true);
      setIsVisible(false);
      setDeferredPrompt(null);
    };

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
    window.addEventListener('appinstalled', handleAppInstalled);

    // Clean up event listeners
    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);
      window.removeEventListener('appinstalled', handleAppInstalled);
    };
  }, []);

  const handleInstallClick = async () => {
    if (!deferredPrompt) return;

    // Show the installation prompt
    deferredPrompt.prompt();

    // Wait for the user to respond to the prompt
    const { outcome } = await deferredPrompt.userChoice;

    if (outcome === 'accepted') {
      console.log('User accepted the install prompt');
    } else {
      console.log('User dismissed the install prompt');
    }

    // Clear the saved prompt since it can only be used once
    setDeferredPrompt(null);
    setIsVisible(false);
  };

  const handleDismiss = () => {
    setIsVisible(false);
    // Store dismissal in localStorage to avoid showing too frequently
    localStorage.setItem('pwa-install-dismissed', Date.now().toString());
  };

  // Don't show if already installed or not supported
  if (isInstalled || !isVisible || !deferredPrompt) {
    return null;
  }

  // Check if user has recently dismissed the prompt
  const lastDismissed = localStorage.getItem('pwa-install-dismissed');
  if (lastDismissed) {
    const daysSinceDismissal = (Date.now() - parseInt(lastDismissed)) / (1000 * 60 * 60 * 24);
    if (daysSinceDismissal < 7) {
      return null; // Don't show for 7 days after dismissal
    }
  }

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-sm shadow-lg border-2 border-primary/20 bg-background/95 backdrop-blur-md">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <Smartphone className="w-5 h-5 text-primary" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-foreground">
              Install M-duka App
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              Get quick access and work offline. Install our app for the best experience.
            </p>
            <div className="flex items-center space-x-2 mt-3">
              <Button
                size="sm"
                onClick={handleInstallClick}
                className="flex items-center space-x-1 touch-target"
              >
                <Download className="w-3 h-3" />
                <span>Install</span>
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleDismiss}
                className="touch-target"
              >
                <X className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// iOS Safari install instructions component
export function IOSInstallInstructions() {
  const [isIOS, setIsIOS] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const checkIfIOS = () => {
      const userAgent = window.navigator.userAgent.toLowerCase();
      const isIOSDevice = /ipad|iphone|ipod/.test(userAgent);
      const isStandalone = (window.navigator as any).standalone === true;
      
      setIsIOS(isIOSDevice && !isStandalone);
      
      // Only show on iOS Safari, not in standalone mode
      if (isIOSDevice && !isStandalone) {
        const lastDismissed = localStorage.getItem('ios-install-dismissed');
        if (!lastDismissed || (Date.now() - parseInt(lastDismissed)) > (1000 * 60 * 60 * 24 * 7)) {
          setIsVisible(true);
        }
      }
    };

    checkIfIOS();
  }, []);

  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem('ios-install-dismissed', Date.now().toString());
  };

  if (!isIOS || !isVisible) {
    return null;
  }

  return (
    <Card className="fixed bottom-4 left-4 right-4 z-50 mx-auto max-w-sm shadow-lg border-2 border-primary/20 bg-background/95 backdrop-blur-md">
      <CardContent className="p-4">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <Smartphone className="w-5 h-5 text-primary" />
            </div>
          </div>
          <div className="flex-1 min-w-0">
            <h3 className="text-sm font-semibold text-foreground">
              Install M-duka App
            </h3>
            <p className="text-xs text-muted-foreground mt-1">
              Tap the share button <span className="font-semibold">↗</span> and select "Add to Home Screen"
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="mt-3 touch-target"
            >
              Got it
            </Button>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="touch-target flex-shrink-0"
          >
            <X className="w-3 h-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 