import { Tables, TablesInsert, TablesUpdate, Database as SupabaseDatabase } from '@/integrations/supabase/types';

// Export Database type
export type Database = SupabaseDatabase;

// Store types with all required database properties
export type Store = Tables<'stores'> & {
  // UI-friendly properties
  storeUrl?: string;
  paymentMethods?: string[];
  notificationsEmail?: string;
  themeOptions?: any;
  whatsappSettings?: any;
};
export type InsertStore = TablesInsert<'stores'>;
export type UpdateStore = TablesUpdate<'stores'>;

// Profile types  
export type Profile = Tables<'profiles'> & {
  phone?: string;
  bio?: string;
};

// Product types
export type DatabaseProduct = Tables<'products'>;
export type InsertProduct = TablesInsert<'products'>;
export type UpdateProduct = TablesUpdate<'products'>;

// Order types
export type Order = Tables<'orders'>;
export type InsertOrder = TablesInsert<'orders'>;
export type UpdateOrder = TablesUpdate<'orders'>;

// Customer types
export type Customer = Tables<'customers'>;
export type InsertCustomer = TablesInsert<'customers'>;
export type UpdateCustomer = TablesUpdate<'customers'>;

// Wishlist types
export type Wishlist = Tables<'wishlists'>;
export type InsertWishlist = TablesInsert<'wishlists'>;

// Category types
export type Category = Tables<'categories'>;
export type InsertCategory = TablesInsert<'categories'>;
export type UpdateCategory = TablesUpdate<'categories'>;