
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useOrder } from '@/contexts';
import { Order } from '@/types/order';

const Orders: React.FC = () => {
  const navigate = useNavigate();
  const { orders: contextOrders, fetchOrders } = useOrder();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    const loadOrders = async () => {
      setIsLoading(true);
      try {
        // Update function call to match signature
        await fetchOrders();
        // Convert database orders to UI orders if needed
        setOrders(contextOrders as any);
      } catch (error) {
        console.error("Error loading orders:", error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadOrders();
  }, [fetchOrders, contextOrders]);

  // Placeholder data for demonstration
  const orderData = [
    {
      id: '12345',
      date: '2024-07-15',
      customer: '<PERSON>',
      total: 150.00,
      status: 'Processing',
    },
    {
      id: '67890',
      date: '2024-07-10',
      customer: '<PERSON>',
      total: 75.50,
      status: 'Shipped',
    },
    {
      id: '13579',
      date: '2024-07-05',
      customer: 'Alice Johnson',
      total: 200.00,
      status: 'Delivered',
    },
  ];

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-4">Orders</h1>
      {isLoading ? (
        <p>Loading orders...</p>
      ) : (
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr className="bg-gray-100">
                <th className="py-2 px-4 border-b">Order ID</th>
                <th className="py-2 px-4 border-b">Date</th>
                <th className="py-2 px-4 border-b">Customer</th>
                <th className="py-2 px-4 border-b">Total</th>
                <th className="py-2 px-4 border-b">Status</th>
              </tr>
            </thead>
            <tbody>
              {orderData.map((order) => (
                <tr key={order.id} className="hover:bg-gray-50">
                  <td className="py-2 px-4 border-b">{order.id}</td>
                  <td className="py-2 px-4 border-b">{order.date}</td>
                  <td className="py-2 px-4 border-b">{order.customer}</td>
                  <td className="py-2 px-4 border-b">${order.total}</td>
                  <td className="py-2 px-4 border-b">{order.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default Orders;
