
import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  FormField, 
  FormControl, 
  FormDescription, 
  FormItem, 
  FormLabel 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import DeliveryZonesSection from "./DeliveryZonesSection";

interface DomesticShippingCardProps {
  form: any;
  isAfricanCountry: boolean;
  selectedCountry: string;
  africanRegions: string[]; // Added prop for regions
}

const DomesticShippingCard: React.FC<DomesticShippingCardProps> = ({ 
  form,
  isAfricanCountry,
  selectedCountry,
  africanRegions // Receive regions as a prop
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Domestic Shipping</CardTitle>
        <CardDescription>Configure shipping options for local deliveries.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={form.control}
          name="flatRateShipping"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Flat Rate Shipping ($)</FormLabel>
              <FormControl>
                <Input placeholder="5.00" {...field} />
              </FormControl>
              <FormDescription>
                Standard shipping rate for all domestic orders.
              </FormDescription>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="enableFreeShipping"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Free Shipping</FormLabel>
                <FormDescription>
                  Enable free shipping threshold for domestic orders
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {form.watch("enableFreeShipping") && (
          <FormField
            control={form.control}
            name="freeShippingThreshold"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Free Shipping Threshold ($)</FormLabel>
                <FormControl>
                  <Input placeholder="50.00" {...field} />
                </FormControl>
                <FormDescription>
                  Orders above this amount qualify for free shipping.
                </FormDescription>
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="estimatedDeliveryDays"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Estimated Delivery Time (days)</FormLabel>
              <FormControl>
                <Input placeholder="3-5" {...field} />
              </FormControl>
              <FormDescription>
                Estimated delivery time to display to customers.
              </FormDescription>
            </FormItem>
          )}
        />

        {/* African Delivery Zones - Pass regions down */}
        {isAfricanCountry && (
          <DeliveryZonesSection 
            form={form} 
            country={selectedCountry} 
            availableRegions={africanRegions} // Pass down the fetched regions
          />
        )}
      </CardContent>
    </Card>
  );
};

export default DomesticShippingCard;
