
import React from 'react';
import { Link } from 'react-router-dom';

interface MobileNavLinkProps {
  href: string;
  label: string;
  active: boolean;
}

const MobileNavLink: React.FC<MobileNavLinkProps> = ({ 
  href, 
  label, 
  active 
}) => {
  return (
    <Link 
      to={href}
      className={`transition-colors py-2 px-3 rounded-md ${
        active 
          ? 'bg-muted font-semibold' 
          : 'hover:bg-muted/50'
      }`}
    >
      {label}
    </Link>
  );
};

export default MobileNavLink;
