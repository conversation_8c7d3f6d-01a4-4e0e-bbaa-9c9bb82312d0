
import React from 'react';
import { 
  ResizablePanel, 
  ResizablePanelGroup, 
  ResizableHandle 
} from '@/components/ui/resizable';
import { Button } from '@/components/ui/button';
import { ArrowRight, Eye, ExternalLink } from 'lucide-react';
import { useStore } from '@/contexts';
import { StorePreview } from '@/components/store/preview/StorePreview';
import { ProductProvider } from '@/contexts/product/ProductContext';
import { useNavigate } from 'react-router-dom';

interface PreviewPanelProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const SettingsPreviewPanel: React.FC<PreviewPanelProps> = ({ open, onOpenChange }) => {
  const { currentStore } = useStore();
  const navigate = useNavigate();
  
  const handleVisitStore = () => {
    // Navigate to the actual storefront in a new tab
    window.open('/shop', '_blank');
  };

  return (
    <ResizablePanelGroup direction="horizontal" className="min-h-[600px] rounded-lg border">
      <ResizablePanel defaultSize={open ? 40 : 100}>
        <div className="p-6 h-full">
          {open ? (
            <Button 
              variant="outline" 
              onClick={() => onOpenChange(false)}
              className="mb-4 flex items-center gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              <span>Close Preview</span>
            </Button>
          ) : null}
          {!open && (
            <div className="space-y-4 mt-4">
              <Button 
                variant="outline" 
                onClick={() => onOpenChange(true)}
                className="flex items-center gap-2 w-full"
              >
                <Eye className="h-4 w-4" />
                <span>Preview Storefront</span>
              </Button>
              
              <Button 
                variant="secondary" 
                onClick={handleVisitStore}
                className="flex items-center gap-2 w-full"
              >
                <ExternalLink className="h-4 w-4" />
                <span>Visit Live Store</span>
              </Button>
            </div>
          )}
        </div>
      </ResizablePanel>
      
      {open && (
        <>
          <ResizableHandle withHandle />
          <ResizablePanel defaultSize={60}>
            <ProductProvider>
              <div className="overflow-auto h-full">
                <StorePreview data={currentStore} />
              </div>
            </ProductProvider>
          </ResizablePanel>
        </>
      )}
    </ResizablePanelGroup>
  );
};

export default SettingsPreviewPanel;
