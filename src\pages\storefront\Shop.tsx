
import React, { useEffect } from "react";
import { Container } from "@/components/ui/container";
import { useProduct } from "@/contexts";
import ProductCard from "@/components/storefront/ProductCard";
import { useStore } from "@/contexts/store/StoreContext";
import BottomNavigation from "@/components/storefront/navigation/BottomNavigation";
import { useIsMobile } from "@/hooks/use-mobile";
import { Heading } from "@/components/ui/heading";
import { ShoppingBag, ArrowRight } from "lucide-react";
import StoreInfo from "@/components/storefront/StoreInfo";
import MarketingBanner from "@/components/storefront/MarketingBanner";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { databaseToUIStore } from "@/utils/typeConverters";
import { Store } from "@/types/store";

const Shop: React.FC = () => {
  const { products, isLoading, fetchProducts } = useProduct();
  const { currentStore } = useStore();
  const isMobile = useIsMobile();
  
  const uiStore = currentStore ? databaseToUIStore(currentStore) as Store : null;
  
  useEffect(() => {
    if (products.length === 0 && !isLoading) {
      fetchProducts('mock-store-1');
    }
  }, [products.length, isLoading, fetchProducts]);
  
  const availableProducts = products.filter(p => p.is_active && (p.stock_quantity || 0) > 0);
  
  const featuredProducts = availableProducts.filter(p => p.tags?.includes('featured'));
  
  const newArrivals = [...availableProducts].sort((a, b) => {
    const dateA = a.created_at ? new Date(a.created_at).getTime() : 0;
    const dateB = b.created_at ? new Date(b.created_at).getTime() : 0;
    return dateB - dateA;
  }).slice(0, 4);

  // Make sure business_hours is an array or undefined
  const businessHours = uiStore?.business_hours || undefined;

  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      <MarketingBanner 
        text="Free delivery on all orders above $50!" 
        backgroundColor="bg-green-500" 
        textColor="text-white"
        isFullWidth={true}
      />
      
      <StoreInfo 
        name={uiStore?.name || "My Shop"} 
        description={uiStore?.description || "Welcome to our store"} 
        phone={uiStore?.phone || undefined}
        email={uiStore?.email || undefined}
        businessHours={businessHours}
        logo={uiStore?.logo_url || undefined}
        address={uiStore?.address || "123 Main Street, Nairobi"}
        location={uiStore?.location || "Nairobi, Kenya"}
      />
      
      <section className="py-6 bg-white mt-4">
        <Container>
          <div className="flex justify-between items-center mb-4">
            <Heading 
              title="Featured Products" 
              description="Check out our top products"
              icon={<ShoppingBag className="h-5 w-5 text-green-500" />}
            />
            <Link to="/shop/featured">
              <Button variant="link" className="text-green-600 font-medium">
                View All <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-6">
            {isLoading ? (
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-[180px] bg-gray-100 rounded-lg animate-pulse"></div>
              ))
            ) : featuredProducts.length > 0 ? (
              featuredProducts.slice(0, 4).map((product) => (
                <div key={product.id} className="animate-fade-in">
                  <ProductCard product={product} />
                </div>
              ))
            ) : (
              availableProducts.slice(0, 4).map((product) => (
                <div key={product.id} className="animate-fade-in">
                  <ProductCard product={product} />
                </div>
              ))
            )}
          </div>
        </Container>
      </section>
      
      <section className="py-6 bg-gray-50">
        <Container>
          <div className="flex justify-between items-center mb-4">
            <Heading 
              title="New Arrivals" 
              description="Our latest products"
              icon={<ShoppingBag className="h-5 w-5 text-green-500" />}
            />
            <Link to="/shop/new">
              <Button variant="link" className="text-green-600 font-medium">
                View All <ArrowRight className="ml-1 h-4 w-4" />
              </Button>
            </Link>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 md:gap-6">
            {isLoading ? (
              [...Array(4)].map((_, i) => (
                <div key={i} className="h-[180px] bg-gray-100 rounded-lg animate-pulse"></div>
              ))
            ) : newArrivals.length > 0 ? (
              newArrivals.map((product) => (
                <div key={product.id} className="animate-fade-in">
                  <ProductCard product={product} />
                </div>
              ))
            ) : (
              <div className="col-span-full text-center py-8 bg-white rounded-lg shadow-sm border border-gray-100">
                <p className="text-gray-500 text-sm mb-4">No new products available at the moment.</p>
              </div>
            )}
          </div>
        </Container>
      </section>
      
      {isMobile && <BottomNavigation />}
      
      <div className={isMobile ? "h-16" : "h-8"}></div>
    </div>
  );
};

export default Shop;
