
/**
 * This module provides a minimal implementation of react-is compatibility.
 * It's used as an alias for the react-is package to avoid installing it as a dependency.
 */

import React, { ReactElement, JSXElementConstructor } from 'react';

type AnyComponent = string | JSXElementConstructor<any>;

export function isElement(element: any): boolean {
  return React.isValidElement(element);
}

export function isValidElementType(type: any): boolean {
  return typeof type === 'string' || typeof type === 'function' || type === React.Fragment;
}

export function isForwardRef(element: any): boolean {
  if (!React.isValidElement(element)) {
    return false;
  }
  
  // Safe way to check for forward ref without accessing $$typeof directly
  const elementType = element.type as any;
  if (typeof elementType !== 'object') return false;
  if (!elementType) return false;
  
  return typeof elementType.render === 'function' && 
         elementType.$$typeof === Symbol.for('react.forward_ref');
}

export function isContextProvider(element: any): boolean {
  if (!React.isValidElement(element)) {
    return false;
  }
  
  const elementType = element.type as any;
  if (typeof elementType !== 'object') return false;
  if (!elementType) return false;
  
  return elementType.$$typeof === Symbol.for('react.provider');
}

export function isContextConsumer(element: any): boolean {
  if (!React.isValidElement(element)) {
    return false;
  }
  
  const elementType = element.type as any;
  if (typeof elementType !== 'object') return false;
  if (!elementType) return false;
  
  return elementType.$$typeof === Symbol.for('react.consumer');
}

/**
 * Checks if the provided element is a React Fragment
 */
export function isFragment(element: any): boolean {
  if (!React.isValidElement(element)) {
    return false;
  }
  
  return element.type === React.Fragment;
}

// Default export containing all the functions
const ReactIs = {
  isElement,
  isValidElementType,
  isForwardRef,
  isContextProvider,
  isContextConsumer,
  isFragment
};

export default ReactIs;
