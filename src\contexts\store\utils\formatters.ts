
import { Store, StoreFormData } from '@/types/store';
import { Json } from '@/integrations/supabase/types';

export const formatStoreFromDb = (storeData: any): Store => {
  return {
    id: storeData.id,
    name: storeData.name,
    description: storeData.description,
    logo_url: storeData.logo,
    store_url: storeData.store_url,
    owner_id: storeData.owner_id,
    created_at: storeData.created_at,
    updated_at: storeData.updated_at,
    category: storeData.category,
    theme_id: storeData.theme_id,
    store_type: storeData.store_type,
    payment_methods: storeData.payment_methods || [],
    notificationsEmail: storeData.notifications_email,
    notifications: storeData.notifications || {
      email: true,
      sms: false,
      orderNotifications: true,
      stockNotifications: true,
      marketingNotifications: false
    },
    theme_options: storeData.theme_options || {
      primaryColor: "#ffffff",
      secondaryColor: "#000000",
      darkMode: false,
      customHeader: false,
      customFooter: false
    },
    whatsapp_settings: storeData.whatsapp_settings || {
      enabled: true,
      number: '',
      message: '',
      businessNumber: '',
      enableOrderNotifications: true,
      enableCustomerUpdates: true,
      customMessage: '',
      autoReply: false
    },
    logo: storeData.logo,
    storeUrl: storeData.store_url,
    is_active: storeData.is_active !== false
  };
};

// Helper function to convert complex objects to JSON-compatible format
const safeJsonify = (obj: any): Json => {
  if (obj === null || obj === undefined) {
    return null;
  }
  
  // Handle arrays by recursively converting each item
  if (Array.isArray(obj)) {
    return obj.map(item => safeJsonify(item)) as Json[];
  }
  
  // Handle objects by recursively converting each property
  if (typeof obj === 'object' && obj !== null) {
    const result: Record<string, Json> = {};
    for (const key in obj) {
      if (Object.prototype.hasOwnProperty.call(obj, key)) {
        result[key] = safeJsonify(obj[key]);
      }
    }
    return result;
  }
  
  // Primitive values can be returned as is (string, number, boolean)
  return obj as Json;
};

interface DbStoreData {
  name: string;
  description: string;
  store_url?: string;
  owner_id: string;
  category?: string;
  theme_id?: string;
  store_type?: string;
  payment_methods?: string[];
  notifications_email?: string;
  logo?: string;
  notifications?: Json;
  theme_options?: Json;
  whatsapp_settings?: Json;
  workflow_settings?: Json;
}

export const formatStoreForDb = (storeData: StoreFormData, userId: string): DbStoreData => {
  return {
    name: storeData.name,
    description: storeData.description,
    store_url: storeData.storeUrl,
    owner_id: userId,
    category: storeData.category,
    theme_id: storeData.themeId,
    store_type: storeData.storeType,
    payment_methods: storeData.paymentMethods,
    notifications_email: storeData.notificationsEmail,
    logo: storeData.logo,
    notifications: safeJsonify(storeData.notifications),
    theme_options: safeJsonify(storeData.themeOptions),
    whatsapp_settings: safeJsonify(storeData.whatsappSettings),
    workflow_settings: safeJsonify(storeData.workflowSettings)
  };
};
