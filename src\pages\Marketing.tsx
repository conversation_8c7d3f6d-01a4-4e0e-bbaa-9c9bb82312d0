import React, { useState } from 'react';
import { Helmet } from 'react-helmet-async';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Megaphone, Mail, BellRing, Users, FileText, Clock, AlertCircle, Sparkles, Package } from 'lucide-react';
import MarketingGenerator from '@/components/marketing/MarketingGenerator';
import { useProduct } from '@/contexts';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';

const Marketing: React.FC = () => {
  const { products } = useProduct();
  const [activeTab, setActiveTab] = useState('ai-assistant');
  const [selectedProductId, setSelectedProductId] = useState<string | null>(null);

  // Get the selected product details for the AI generator
  const selectedProduct = selectedProductId 
    ? products.find(p => p.id === selectedProductId) 
    : null;

  return (
    <DashboardLayout>
      <Helmet>
        <title>Marketing - m-duka</title>
      </Helmet>
      
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Marketing</h1>
          <Button disabled={activeTab !== 'campaigns'} className="flex items-center gap-2">
            <Megaphone className="h-4 w-4" />
            Create Campaign
          </Button>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="ai-assistant" className="flex items-center gap-1">
              <Sparkles className="h-4 w-4" />
              AI Assistant
            </TabsTrigger>
            <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
            <TabsTrigger value="automations">Automations</TabsTrigger>
            <TabsTrigger value="subscribers">Subscribers</TabsTrigger>
          </TabsList>
          
          <TabsContent value="ai-assistant" className="mt-4">
            <div className="mb-4">
              <Card className="bg-muted/30 mb-6">
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg flex items-center gap-1.5">
                    <Package className="h-4 w-4" />
                    Select a Product
                  </CardTitle>
                  <CardDescription>
                    Choose a product to generate marketing content specifically for that item
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 gap-4 max-w-md">
                    <div className="space-y-1.5">
                      <Label htmlFor="product-selector">Product</Label>
                      <Select 
                        value={selectedProductId || ''}
                        onValueChange={setSelectedProductId}
                      >
                        <SelectTrigger id="product-selector">
                          <SelectValue placeholder="Select a product" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">Generate generic content</SelectItem>
                          {products.map(product => (
                            <SelectItem key={product.id} value={product.id}>
                              {product.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <p className="text-xs text-muted-foreground pt-1">
                        {selectedProduct ? 
                          `Category: ${selectedProduct.category || 'N/A'} | Price: ${selectedProduct.currency} ${selectedProduct.price}` : 
                          'Select a product to auto-fill product details'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
            <MarketingGenerator 
              defaultProductName={selectedProduct?.name || ''}
              defaultProductDescription={selectedProduct?.description || ''}
              defaultCategory={selectedProduct?.category || ''}
              defaultPrice={selectedProduct?.price}
            />
          </TabsContent>
          
          <TabsContent value="campaigns" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Marketing campaigns feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Marketing Campaigns Feature</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    We're working on a powerful marketing campaigns feature that will help you engage with your customers and drive sales.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl">
                    <FeatureCard 
                      icon={<Mail className="h-8 w-8" />}
                      title="Email Campaigns"
                      description="Create and schedule promotional emails"
                    />
                    <FeatureCard 
                      icon={<BellRing className="h-8 w-8" />}
                      title="Notifications"
                      description="Send push notifications to customers"
                    />
                    <FeatureCard 
                      icon={<Users className="h-8 w-8" />}
                      title="Audience Targeting"
                      description="Segment your audience for better results"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="automations" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Marketing automations feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Marketing Automations Feature</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    Soon you'll be able to set up automated marketing workflows to nurture leads and improve customer retention.
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 w-full max-w-3xl">
                    <FeatureCard 
                      icon={<FileText className="h-8 w-8" />}
                      title="Abandoned Cart"
                      description="Recover potential lost sales automatically"
                    />
                    <FeatureCard 
                      icon={<Clock className="h-8 w-8" />}
                      title="Follow-ups"
                      description="Schedule automatic follow-up messages"
                    />
                    <FeatureCard 
                      icon={<Users className="h-8 w-8" />}
                      title="Welcome Series"
                      description="Onboard new customers effectively"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="subscribers" className="mt-4">
            <Card>
              <CardHeader>
                <CardTitle>Coming Soon</CardTitle>
                <CardDescription>
                  Subscriber management feature is under development
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                <div className="flex flex-col items-center justify-center py-8">
                  <AlertCircle className="h-16 w-16 text-muted-foreground mb-4" />
                  <h3 className="text-xl font-medium mb-2">Subscriber Management</h3>
                  <p className="text-muted-foreground text-center max-w-md mb-6">
                    Manage your marketing subscribers, import contacts, and analyze your audience growth.
                  </p>
                  <Button variant="outline" disabled>
                    Check Back Soon
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({ icon, title, description }) => {
  return (
    <div className="bg-accent/50 rounded-lg p-4 flex flex-col items-center text-center">
      <div className="mb-2 text-primary">{icon}</div>
      <h4 className="font-medium mb-1">{title}</h4>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
};

export default Marketing;
