import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { 
  Settings, 
  BadgePercent, 
  Clock, 
  Globe, 
  ShoppingBag, 
  TagIcon, 
  Share2, 
  Zap,
  Upload
} from 'lucide-react';
import { toast } from 'sonner';

interface StoreGeneralContentProps {
  storeName: string;
  setStoreName: (value: string) => void;
  orderPrefix: string;
  setOrderPrefix: (value: string) => void;
  defaultTimeZone: string;
  setDefaultTimeZone: (value: string) => void;
  enableInventoryTracking: boolean;
  setEnableInventoryTracking: (value: boolean) => void;
  lowStockThreshold: string;
  setLowStockThreshold: (value: string) => void;
  enableReviews: boolean;
  setEnableReviews: (value: boolean) => void;
  enableWishlist: boolean;
  setEnableWishlist: (value: boolean) => void;
  defaultMetaTitle: string;
  setDefaultMetaTitle: (value: string) => void;
  defaultMetaDescription: string;
  setDefaultMetaDescription: (value: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
  logo?: string;
  setLogo: (file: File | null) => void;
}

const StoreGeneralContent: React.FC<StoreGeneralContentProps> = ({
  storeName,
  setStoreName,
  orderPrefix,
  setOrderPrefix,
  defaultTimeZone,
  setDefaultTimeZone,
  enableInventoryTracking,
  setEnableInventoryTracking,
  lowStockThreshold,
  setLowStockThreshold,
  enableReviews,
  setEnableReviews,
  enableWishlist,
  setEnableWishlist,
  defaultMetaTitle,
  setDefaultMetaTitle,
  defaultMetaDescription,
  setDefaultMetaDescription,
  loading,
  onSubmit,
  logo,
  setLogo
}) => {
  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml', 'image/webp'];
      const maxSize = 5 * 1024 * 1024;

      if (!allowedTypes.includes(file.type)) {
        toast.error('Invalid file type. Please upload JPEG, PNG, SVG, or WebP.');
        return;
      }

      if (file.size > maxSize) {
        toast.error('File is too large. Maximum size is 5MB.');
        return;
      }

      setLogo(file);
    }
  };

  const handleLogoRemove = () => {
    setLogo(null);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Store Settings
          </CardTitle>
          <CardDescription>
            Configure general settings for your store
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={onSubmit} className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="store-name">Store Name</Label>
                <Input
                  id="store-name"
                  value={storeName}
                  onChange={(e) => setStoreName(e.target.value)}
                  placeholder="Your Store Name"
                  className="mt-1"
                />
              </div>
              
              <div>
                <Label htmlFor="order-prefix">Order Number Prefix</Label>
                <Input
                  id="order-prefix"
                  value={orderPrefix}
                  onChange={(e) => setOrderPrefix(e.target.value)}
                  placeholder="e.g. ORD-"
                  className="mt-1"
                />
                <p className="text-xs text-muted-foreground mt-1">
                  This prefix will be added to all order numbers (e.g. ORD-12345)
                </p>
              </div>
              
              <div>
                <Label htmlFor="timezone">Default Time Zone</Label>
                <Input
                  id="timezone"
                  value={defaultTimeZone}
                  onChange={(e) => setDefaultTimeZone(e.target.value)}
                  placeholder="e.g. UTC+3"
                  className="mt-1"
                />
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <ShoppingBag className="h-5 w-5" />
                Inventory Settings
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="inventory-tracking" className="text-base">Enable Inventory Tracking</Label>
                    <p className="text-sm text-muted-foreground">
                      Track product inventory levels and get low stock alerts
                    </p>
                  </div>
                  <Switch
                    id="inventory-tracking"
                    checked={enableInventoryTracking}
                    onCheckedChange={setEnableInventoryTracking}
                  />
                </div>
                
                {enableInventoryTracking && (
                  <div>
                    <Label htmlFor="low-stock">Low Stock Threshold</Label>
                    <Input
                      id="low-stock"
                      type="number"
                      value={lowStockThreshold}
                      onChange={(e) => setLowStockThreshold(e.target.value)}
                      placeholder="5"
                      className="mt-1"
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      You'll receive alerts when inventory falls below this number
                    </p>
                  </div>
                )}
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <Zap className="h-5 w-5" />
                Store Features
              </h3>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enable-reviews" className="text-base">Enable Customer Reviews</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow customers to leave reviews on your products
                    </p>
                  </div>
                  <Switch
                    id="enable-reviews"
                    checked={enableReviews}
                    onCheckedChange={setEnableReviews}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="enable-wishlist" className="text-base">Enable Wishlist</Label>
                    <p className="text-sm text-muted-foreground">
                      Allow customers to save products to wishlist
                    </p>
                  </div>
                  <Switch
                    id="enable-wishlist"
                    checked={enableWishlist}
                    onCheckedChange={setEnableWishlist}
                  />
                </div>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <Globe className="h-5 w-5" />
                SEO Settings
              </h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="meta-title">Default Meta Title</Label>
                  <Input
                    id="meta-title"
                    value={defaultMetaTitle}
                    onChange={(e) => setDefaultMetaTitle(e.target.value)}
                    placeholder="Your Store Name - Tagline"
                    className="mt-1"
                  />
                </div>
                
                <div>
                  <Label htmlFor="meta-description">Default Meta Description</Label>
                  <Textarea
                    id="meta-description"
                    value={defaultMetaDescription}
                    onChange={(e) => setDefaultMetaDescription(e.target.value)}
                    placeholder="A brief description of your store for search engines"
                    className="mt-1"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    This will be used when no specific meta description is provided
                  </p>
                </div>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-4 flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Store Logo
              </h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="store-logo">Upload Logo</Label>
                  <div className="flex items-center gap-4 mt-2">
                    <Input
                      id="store-logo"
                      type="file"
                      accept="image/jpeg,image/png,image/svg+xml,image/webp"
                      onChange={handleLogoUpload}
                      className="w-full max-w-sm"
                    />
                    {logo && (
                      <Button 
                        type="button" 
                        variant="destructive" 
                        size="sm"
                        onClick={handleLogoRemove}
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Recommended: PNG or JPEG, max 5MB
                  </p>
                </div>
                
                {logo && (
                  <div className="mt-4">
                    <Label>Logo Preview</Label>
                    <div className="mt-2 border rounded-md p-4 flex justify-center items-center">
                      <img 
                        src={typeof logo === 'string' ? logo : URL.createObjectURL(logo)} 
                        alt="Store Logo" 
                        className="max-h-32 max-w-full object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
            
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving Changes...' : 'Save Changes'}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default StoreGeneralContent;
