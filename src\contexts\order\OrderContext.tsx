import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from '../auth/AuthContext';
import { useStore } from '../store/StoreContext';
import { useCart } from '../cart/CartContext';
import { orderOperations } from '@/lib/supabase';
import { Tables, TablesInsert, TablesUpdate } from '@/integrations/supabase/types';

type Order = Tables<'orders'>;
type InsertOrder = TablesInsert<'orders'>;
type UpdateOrder = TablesUpdate<'orders'>;
import { toast } from 'sonner';

interface OrderContextType {
  orders: Order[];
  isLoading: boolean;
  error: Error | null;
  createOrder: (data: InsertOrder) => Promise<void>;
  updateOrder: (id: string, data: UpdateOrder) => Promise<void>;
  fetchOrders: () => Promise<void>;
  fetchOrder: (id: string) => Promise<Order>;
  checkout: (shippingAddress: any) => Promise<void>;
  updateOrderStatus?: (id: string, status: string) => Promise<void>;
}

const OrderContext = createContext<OrderContextType | undefined>(undefined);

export const OrderProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const { currentStore } = useStore();
  const { items, clearCart } = useCart();
  const [orders, setOrders] = useState<Order[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createOrder = async (data: InsertOrder) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: order, error } = await orderOperations.create(data);
      if (error) throw error;
      setOrders(prev => [...prev, order]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create order';
      setError(new Error(errorMessage));
      toast.error(`Error creating order: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrder = async (id: string, data: UpdateOrder) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: order, error } = await orderOperations.update(id, data);
      if (error) throw error;
      setOrders(prev => prev.map(o => o.id === id ? order : o));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update order';
      setError(new Error(errorMessage));
      toast.error(`Error updating order: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateOrderStatus = async (id: string, status: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: order, error } = await orderOperations.update(id, { status });
      if (error) throw error;
      setOrders(prev => prev.map(o => o.id === id ? order : o));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update order status';
      setError(new Error(errorMessage));
      toast.error(`Error updating order status: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchOrders = async () => {
    if (!user?.id) return;

    try {
      setIsLoading(true);
      setError(null);
      const { data: orders, error } = await orderOperations.getByCustomer(user.id);
      if (error) throw error;
      setOrders(orders || []);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch orders';
      setError(new Error(errorMessage));
      toast.error(`Error fetching orders: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchOrder = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: order, error } = await orderOperations.getById(id);
      if (error) throw error;
      if (!order) throw new Error('Order not found');
      return order;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch order';
      setError(new Error(errorMessage));
      toast.error(`Error fetching order: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const checkout = async (shippingAddress: any) => {
    if (!user?.id || !currentStore?.id || items.length === 0) {
      throw new Error('Invalid checkout state');
    }

    try {
      setIsLoading(true);
      setError(null);

      // Calculate total amount
      const totalAmount = items.reduce(
        (total, item) => total + item.product.price * item.quantity,
        0
      );

      const orderData = {
        store_id: currentStore.id,
        customer_id: user.id,
        status: 'pending' as const,
        total_amount: totalAmount,
        shipping_address: shippingAddress,
        payment_method: 'cash' as const,
      };

      const { data: order, error } = await orderOperations.create(orderData);
      if (error) throw error;

      // Clear cart after successful order creation
      clearCart();

      // Add order to state
      setOrders(prev => [...prev, order]);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to process checkout';
      setError(new Error(errorMessage));
      toast.error(`Error processing checkout: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchOrders();
    }
  }, [user?.id]);

  return (
    <OrderContext.Provider
      value={{
        orders,
        isLoading,
        error,
        createOrder,
        updateOrder,
        updateOrderStatus,
        fetchOrders,
        fetchOrder,
        checkout,
      }}
    >
      {children}
    </OrderContext.Provider>
  );
};

export const useOrder = () => {
  const context = useContext(OrderContext);
  if (context === undefined) {
    throw new Error('useOrder must be used within an OrderProvider');
  }
  return context;
};
