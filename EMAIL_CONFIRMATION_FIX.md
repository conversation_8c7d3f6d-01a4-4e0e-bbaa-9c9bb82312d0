# Email Confirmation Fix Guide

## 🚨 Current Issue
Users are not receiving email confirmation links after signup, preventing account activation.

## 🛠️ Immediate Solutions

### Option 1: Disable Email Confirmation (Development Only)

**⚠️ WARNING: Only use this for development/testing. Re-enable for production.**

1. **Go to Supabase Dashboard:**
   - Visit: https://supabase.com/dashboard/project/nheycjpozywomwscplcz
   - Navigate to **Authentication > Settings**

2. **Disable Email Confirmation:**
   - Find "Enable email confirmations" setting
   - **Turn it OFF** temporarily
   - Save changes

3. **Test Signup:**
   - Try creating a new account
   - You should be able to login immediately without email confirmation

### Option 2: Configure Custom SMTP (Recommended for Production)

1. **Get SMTP Credentials:**
   - Use Gmail, SendGrid, Mailgun, or similar service
   - For Gmail: Enable 2FA and create an App Password

2. **Configure in Supabase:**
   - Go to **Authentication > Settings > SMTP Settings**
   - Enable "Enable custom SMTP"
   - Configure:
     ```
     SMTP Host: smtp.gmail.com (for Gmail)
     SMTP Port: 587
     SMTP User: <EMAIL>
     SMTP Pass: your-app-password
     Sender Email: <EMAIL>
     Sender Name: M-Duka
     ```

### Option 3: Use Supabase CLI to Manually Confirm Users

1. **Install Supabase CLI:**
   ```bash
   npm install -g supabase
   ```

2. **Login to Supabase:**
   ```bash
   supabase login
   ```

3. **Manually confirm user:**
   ```bash
   supabase auth users list
   supabase auth users update <user-id> --email-confirm
   ```

## 🔧 Code Fix: Auto-Confirm for Development

Create a development bypass for email confirmation:

### Update registerApi.ts

Add this development mode bypass:

```typescript
// In development, auto-confirm users
if (process.env.NODE_ENV === 'development') {
  // Wait a moment for user creation
  setTimeout(async () => {
    try {
      const { data: users } = await supabase.auth.admin.listUsers();
      const newUser = users.users.find(u => u.email === email);
      if (newUser && !newUser.email_confirmed_at) {
        await supabase.auth.admin.updateUserById(newUser.id, {
          email_confirm: true
        });
        console.log('Auto-confirmed user for development');
      }
    } catch (error) {
      console.log('Auto-confirm failed:', error);
    }
  }, 2000);
}
```

## 📧 Email Template Configuration

If using custom SMTP, configure email templates in Supabase:

1. **Go to Authentication > Email Templates**
2. **Customize "Confirm signup" template:**
   ```html
   <h2>Welcome to M-Duka!</h2>
   <p>Thanks for signing up! Please confirm your email address by clicking the link below:</p>
   <p><a href="{{ .ConfirmationURL }}">Confirm your account</a></p>
   <p>If you didn't create an account, you can safely ignore this email.</p>
   ```

## 🔍 Debugging Steps

1. **Check Supabase Logs:**
   - Go to Supabase Dashboard > Logs
   - Look for email-related errors

2. **Check Spam Folder:**
   - Emails might be going to spam

3. **Verify Email Settings:**
   - Ensure SMTP credentials are correct
   - Test with a simple email service first

4. **Check Rate Limits:**
   - Supabase has email rate limits
   - Wait between signup attempts

## 🚀 Production Recommendations

1. **Use Professional Email Service:**
   - SendGrid (recommended)
   - Mailgun
   - Amazon SES
   - Postmark

2. **Configure Proper DNS:**
   - Set up SPF, DKIM, DMARC records
   - Use a verified domain

3. **Monitor Email Delivery:**
   - Set up email delivery monitoring
   - Track bounce rates and spam reports

## 🆘 Emergency Workaround

If you need to test immediately:

1. **Disable email confirmation in Supabase**
2. **Create accounts without verification**
3. **Re-enable email confirmation later**
4. **Manually verify existing users if needed**

---

**Priority Actions:**
1. ✅ Disable email confirmation for immediate testing
2. 🔧 Set up custom SMTP for production
3. 📧 Configure email templates
4. 🔍 Test thoroughly before going live
