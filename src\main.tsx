// Import the JSX runtime fix to ensure it's available
import "./utils/jsx-runtime-fix";

import React, { StrictMode } from "react"; // Added explicit React import
import { createRoot } from "react-dom/client";
import App from "./App.tsx";

import { SupabaseProvider } from "./providers/SupabaseProvider";
import "./index.css";
import { debugApp } from "./utils/debug";
import { ErrorBoundary } from "react-error-boundary";

// Application start timestamp for performance measurement
const appStartTime = performance.now();
console.log("Application initialization started");

// Enhanced error reporting with more detailed type handling
window.addEventListener("error", (event) => {
  // Track the error source
  const errorSource = event.filename?.split('?')[0] || 'unknown source';
  
  console.log("Global error caught:", {
    message: event.message, 
    source: errorSource,
    error: event.error
  });
  
  // Check for JSX runtime errors and provide debug info
  if (event.message?.includes('jsx-runtime') || event.message?.includes('jsx')) {
    console.warn('JSX Runtime error detected. This might be caused by a module resolution issue.', {
      message: event.message,
      source: errorSource
    });
  }
  
  // Check for lodash related errors
  if (event.message?.includes('lodash') || event.filename?.includes('lodash')) {
    console.warn('Lodash import error detected. Check lodash-compat.ts file for proper exports.', {
      message: event.message,
      source: errorSource
    });
  }
  
  // Check for potential Framer Motion issues
  if (event.message?.includes('framer') || event.filename?.includes('framer-motion')) {
    console.warn('Framer Motion error detected. This might be caused by animation issues.', {
      message: event.message,
      source: errorSource
    });
  }
  
  // Check for React related errors
  if (event.message?.includes('react') || event.filename?.includes('react')) {
    console.warn('React error detected. This might be caused by invalid hooks or component issues.', {
      message: event.message,
      source: errorSource
    });
  }
  
  debugApp.logError(event.error);
  console.error("Global error caught:", event.error);
});

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('SW registered: ', registration);
      })
      .catch((registrationError) => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// Check if the root element exists
const rootElement = document.getElementById("root");

if (rootElement) {
  debugApp.logRenderStart();
  debugApp.logElementExists("root");
  console.log("Root element found, starting render");

  try {
    // Show loading indicator before mounting app - Secure static content
    const loadingHTML = document.createElement('div');
    loadingHTML.style.cssText = 'display: flex; justify-content: center; align-items: center; height: 100vh; flex-direction: column;';
    
    const spinner = document.createElement('div');
    spinner.style.cssText = 'border: 3px solid #f3f3f3; border-radius: 50%; border-top: 3px solid #3498db; width: 40px; height: 40px; animation: spin 1s linear infinite;';
    
    const text = document.createElement('p');
    text.style.cssText = 'margin-top: 10px; font-family: sans-serif; color: #666;';
    text.textContent = 'Loading application...';
    
    const style = document.createElement('style');
    style.textContent = '@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }';
    
    loadingHTML.appendChild(spinner);
    loadingHTML.appendChild(text);
    document.head.appendChild(style);
    rootElement.appendChild(loadingHTML);
    
    // Log React version for debugging
    console.log("React version:", React.version || "unknown");
    
    // Brief timeout to ensure loading indicator shows
    setTimeout(() => {
      // Clear any previous content to prevent potential conflicts
      rootElement.textContent = '';
      
      // Create root and render
      console.log("Creating React root and rendering App");
      const root = createRoot(rootElement);
      root.render(
      <StrictMode>
        <ErrorBoundary
          FallbackComponent={({ error, resetErrorBoundary }) => (
            <div style={{ padding: '20px', textAlign: 'center', fontFamily: 'sans-serif' }}>
              <h1 style={{ color: '#e74c3c' }}>Something went wrong</h1>
              <p>The application encountered an error:</p>
              <div style={{ 
                backgroundColor: '#f8f8f8', 
                padding: '15px', 
                borderRadius: '5px', 
                textAlign: 'left', 
                margin: '20px 0',
                overflow: 'auto',
                maxHeight: '200px'
              }}>
                <code>{error?.message || 'Unknown error'}</code>
              </div>
              <button 
                onClick={resetErrorBoundary}
                style={{
                  backgroundColor: '#3498db',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer',
                  marginRight: '10px'
                }}
              >
                Try Again
              </button>
              <button 
                onClick={() => window.location.reload()}
                style={{
                  backgroundColor: '#e74c3c',
                  color: 'white',
                  border: 'none',
                  padding: '10px 20px',
                  borderRadius: '5px',
                  cursor: 'pointer'
                }}
              >
                Reload Page
              </button>
            </div>
          )}
          onError={(error, errorInfo) => {
            console.error("=== ErrorBoundary caught error ===", error);
            console.error("=== Error details ===", errorInfo);
            console.error("=== Stack trace ===", error.stack);
          }}
        >
          <SupabaseProvider>
            <App />
          </SupabaseProvider>
        </ErrorBoundary>
      </StrictMode>
      );
      
      // Log performance metrics
      const renderTime = performance.now() - appStartTime;
      console.log(
        `%c ✅ Render complete (${renderTime.toFixed(0)}ms)`,
        "background: #222; color: #00ff00; font-size: 16px;"
      );
    }, 300);
  } catch (error) {
    debugApp.logError(error as Error);
    console.error("Error during render:", error);
    // Display a fallback UI when the main app fails to render - Secure version
    const errorContainer = document.createElement('div');
    errorContainer.style.cssText = 'padding: 20px; font-family: sans-serif; text-align: center;';
    
    const title = document.createElement('h1');
    title.style.color = '#e74c3c';
    title.textContent = 'Something went wrong';
    
    const message = document.createElement('p');
    message.textContent = 'The application encountered an error during startup. Please check the console for details.';
    
    const errorDetails = document.createElement('div');
    errorDetails.style.cssText = 'background-color: #f8f8f8; padding: 15px; border-radius: 5px; text-align: left; margin: 20px 0; overflow: auto; max-height: 200px;';
    const errorCode = document.createElement('code');
    errorCode.textContent = (error as Error)?.message || 'Unknown error';
    errorDetails.appendChild(errorCode);
    
    const reloadButton = document.createElement('button');
    reloadButton.style.cssText = 'background-color: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;';
    reloadButton.textContent = 'Reload Application';
    reloadButton.onclick = () => window.location.reload();
    
    errorContainer.appendChild(title);
    errorContainer.appendChild(message);
    errorContainer.appendChild(errorDetails);
    errorContainer.appendChild(reloadButton);
    rootElement.appendChild(errorContainer);
  }
} else {
  console.error(
    "%c ❌ Root element not found",
    "background: #222; color: #ff0000; font-size: 16px;"
  );
  const errorContainer = document.createElement('div');
  errorContainer.style.cssText = 'padding: 20px; font-family: sans-serif; text-align: center;';
  
  const title = document.createElement('h1');
  title.textContent = 'Root element missing';
  
  const message = document.createElement('p');
  message.textContent = 'The application could not find the root element to mount. Please check your HTML structure.';
  
  const reloadButton = document.createElement('button');
  reloadButton.style.cssText = 'background-color: #3498db; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;';
  reloadButton.textContent = 'Reload';
  reloadButton.onclick = () => window.location.reload();
  
  errorContainer.appendChild(title);
  errorContainer.appendChild(message);
  errorContainer.appendChild(reloadButton);
  document.body.appendChild(errorContainer);
}
