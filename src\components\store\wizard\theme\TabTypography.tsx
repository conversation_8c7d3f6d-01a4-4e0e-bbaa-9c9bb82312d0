
import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface TabTypographyProps {
  themeOptions: {
    fontFamily?: string;
  };
  handleUpdateThemeOptions: (updates: Partial<TabTypographyProps['themeOptions']>) => void;
  fontOptions: {
    id: string;
    name: string;
    sample: string;
  }[];
}

const TabTypography: React.FC<TabTypographyProps> = ({
  themeOptions,
  handleUpdateThemeOptions,
  fontOptions,
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Font Family</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {fontOptions.map((font) => (
            <Card 
              key={font.id}
              className={cn(
                "cursor-pointer hover:border-green-500 transition-all",
                themeOptions.fontFamily === font.id && "border-2 border-green-500"
              )}
              onClick={() => handleUpdateThemeOptions({ fontFamily: font.id })}
            >
              <CardHeader className="py-3">
                <CardTitle className="text-base">{font.name}</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <p className={`font-${font.id}`}>{font.sample}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TabTypography;
