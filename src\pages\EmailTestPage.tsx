import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import { devBypassEmailConfirmation } from '@/utils/manualEmailConfirm';

const EmailTestPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleTestLogin = async () => {
    if (!email || !password) {
      toast.error('Please enter both email and password');
      return;
    }

    setIsLoading(true);
    try {
      const result = await devBypassEmailConfirmation(email, password);
      
      if (result.success) {
        toast.success('Login successful! Email confirmation is working.');
      } else {
        toast.error(result.error || 'Login failed');
        
        if (result.solution) {
          toast.info(result.solution, { duration: 8000 });
        }
      }
    } catch (error) {
      toast.error('Test failed');
    } finally {
      setIsLoading(false);
    }
  };

  const openSupabaseDashboard = () => {
    window.open('https://supabase.com/dashboard/project/nheycjpozywomwscplcz/auth/settings', '_blank');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-2xl space-y-6">
        
        {/* Main Solution Card */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertCircle className="h-5 w-5" />
              Email Confirmation Issue
            </CardTitle>
            <CardDescription className="text-red-700">
              Users cannot login because email confirmation emails are not being sent.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="bg-white rounded-lg p-4 border border-red-200">
              <h3 className="font-semibold text-red-800 mb-2">🎯 IMMEDIATE SOLUTION:</h3>
              <ol className="list-decimal list-inside space-y-2 text-sm text-red-700">
                <li>Click the button below to open Supabase Dashboard</li>
                <li>Go to <strong>Authentication → Settings</strong></li>
                <li>Find <strong>"Enable email confirmations"</strong></li>
                <li><strong>Turn OFF</strong> the toggle switch</li>
                <li>Click <strong>"Save"</strong></li>
                <li>Try signing up again - it should work immediately</li>
              </ol>
            </div>
            
            <Button 
              onClick={openSupabaseDashboard}
              className="w-full bg-red-600 hover:bg-red-700"
              size="lg"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Open Supabase Dashboard
            </Button>
          </CardContent>
        </Card>

        {/* Test Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Test Email Confirmation Status
            </CardTitle>
            <CardDescription>
              Test if email confirmation is still required for your account
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <label htmlFor="test-email" className="text-sm font-medium">
                Email Address
              </label>
              <Input
                id="test-email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="test-password" className="text-sm font-medium">
                Password
              </label>
              <Input
                id="test-password"
                type="password"
                placeholder="Your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
              />
            </div>

            <Button 
              onClick={handleTestLogin}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? 'Testing...' : 'Test Login (Check Console)'}
            </Button>
          </CardContent>
        </Card>

        {/* Instructions Card */}
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800">📋 Step-by-Step Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-blue-700">
            <div>
              <strong>1. Disable Email Confirmation (Quickest Fix):</strong>
              <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                <li>Go to Supabase Dashboard (button above)</li>
                <li>Authentication → Settings</li>
                <li>Turn OFF "Enable email confirmations"</li>
                <li>Save changes</li>
              </ul>
            </div>
            
            <div>
              <strong>2. Alternative - Configure SMTP:</strong>
              <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                <li>In Supabase: Authentication → Settings → SMTP Settings</li>
                <li>Enable "Enable custom SMTP"</li>
                <li>Use Gmail, SendGrid, or Mailgun credentials</li>
              </ul>
            </div>
            
            <div>
              <strong>3. Test Again:</strong>
              <ul className="list-disc list-inside ml-4 mt-1 space-y-1">
                <li>Try creating a new account</li>
                <li>You should be able to login immediately</li>
                <li>Re-enable email confirmation when SMTP is configured</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        <div className="text-center">
          <Button 
            onClick={() => window.location.href = '/signup'}
            variant="outline"
          >
            ← Back to Signup
          </Button>
        </div>
      </div>
    </div>
  );
};

export default EmailTestPage;
