
import React from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Smartphone, ShoppingBag, CreditCard, ShieldCheck } from "lucide-react";
import { Container } from "@/components/ui/container";

const KeyBenefits = () => {
  const benefits = [
    {
      icon: <Smartphone className="h-10 w-10 text-green-600" />,
      title: "Quick & Easy Setup",
      description: "Launch your mobile shop in minutes without technical skills or coding knowledge."
    },
    {
      icon: <ShoppingBag className="h-10 w-10 text-green-600" />,
      title: "Seamless Order Management",
      description: "Receive, track, and fulfill orders effortlessly through your WhatsApp interface."
    },
    {
      icon: <CreditCard className="h-10 w-10 text-green-600" />,
      title: "Secure Payments",
      description: "Accept payments through M-Pesa, PayPal, and other popular payment methods."
    },
    {
      icon: <ShieldCheck className="h-10 w-10 text-green-600" />,
      title: "Custom Branding",
      description: "Personalize your store with your logo, domain, and store information."
    }
  ];
  
  return (
    <section id="benefits" className="py-20 bg-accent/30">
      <Container>
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">Everything You Need to Succeed</h2>
          <p className="text-muted-foreground text-lg max-w-2xl mx-auto">
            M-Duka gives you all the tools to turn your WhatsApp into a powerful sales channel
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {benefits.map((benefit, index) => {
            const [ref, inView] = useInView({
              triggerOnce: true,
              threshold: 0.1
            });
            
            return (
              <motion.div
                key={index}
                ref={ref}
                initial={{ opacity: 0, y: 20 }}
                animate={inView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="bg-white p-6 rounded-xl shadow-sm hover:shadow-md transition-shadow"
              >
                <div className="bg-green-50 w-16 h-16 rounded-full flex items-center justify-center mb-4 mx-auto">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold mb-2 text-center">{benefit.title}</h3>
                <p className="text-muted-foreground text-center">{benefit.description}</p>
              </motion.div>
            );
          })}
        </div>
      </Container>
    </section>
  );
};

export default KeyBenefits;
