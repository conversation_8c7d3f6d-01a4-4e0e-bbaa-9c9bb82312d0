
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Product } from '@/types/unified-product';
import { cn } from '@/lib/utils';

interface ProductBadgesProps {
  product: Product;
  isNew: boolean;
  primaryColor: string;
}

const ProductBadges: React.FC<ProductBadgesProps> = ({
  product,
  isNew,
  primaryColor,
}) => {
  return (
    <>
      {isNew && (
        <div className={cn(
          "absolute top-2 left-2 text-xs font-medium px-2 py-1 rounded-full z-10",
          primaryColor.includes('custom') ? 'bg-primary-custom text-white' : primaryColor + ' text-white'
        )}>
          NEW
        </div>
      )}
      
      {product.stock_quantity > 0 && product.stock_quantity <= 5 && (
        <Badge className="absolute top-2 right-2 bg-amber-500 text-white shadow-sm">
          Only {product.stock_quantity} left
        </Badge>
      )}
    </>
  );
};

export default ProductBadges;
