
import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Link } from "react-router-dom";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import AuthLayout from "@/components/auth/AuthLayout";
import { useAuth } from "@/contexts";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
});

type FormValues = z.infer<typeof formSchema>;

const ForgotPassword = () => {
  const { resetPassword } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    // Prevent submitting if already in progress
    if (isSubmitting) return;
    
    setFormError(null);
    setIsSubmitting(true);
    
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Sending reset instructions...");
      
      await resetPassword(data.email);
      
      if (toastId) toast.dismiss(toastId);
      setEmailSent(true);
    } catch (error: any) {
      console.error("Password reset error:", error);
      if (toastId) toast.dismiss(toastId);
      setFormError(error.message || "Failed to send reset instructions");
      toast.error(error.message || "Failed to send reset instructions");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <AuthLayout title="Reset Your Password" subtitle="Enter your email to receive password reset instructions">
      {emailSent ? (
        <div className="text-center">
          <Alert className="mb-6 bg-green-50">
            <AlertDescription>
              <p className="text-sm">
                Password reset instructions have been sent to your email. Please check your inbox and follow the instructions to reset your password.
              </p>
            </AlertDescription>
          </Alert>
          
          <Button asChild className="mt-4">
            <Link to="/signin">Return to Sign In</Link>
          </Button>
        </div>
      ) : (
        <>
          {formError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? "Sending..." : "Send Reset Instructions"}
              </Button>
            </form>
          </Form>

          <div className="mt-6 text-center">
            <p className="text-sm text-muted-foreground">
              Remember your password?{" "}
              <Link to="/signin" className="text-primary hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </>
      )}
    </AuthLayout>
  );
};

export default ForgotPassword;
