import { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Header from "@/components/Header";
import Hero from "@/components/Hero";
import Features from "@/components/Features";
import Pricing from "@/components/Pricing";
import Testimonials from "@/components/Testimonials";
import Footer from "@/components/Footer";
import StatsSection from "@/components/StatsSection";
import KeyBenefits from "@/components/home/<USER>";
import HowItWorks from "@/components/home/<USER>";
import { SEO } from "@/components/SEO";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { X, Send, Minimize2 } from "lucide-react";

// Collapsible AI Assistant Chat Widget
const FloatingAIAssistant = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [message, setMessage] = useState("");
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi! I'm your M-Duka AI assistant. I can help you create your WhatsApp store, write product descriptions, and answer questions about getting started. What kind of store are you creating?",
      sender: "ai",
      timestamp: new Date()
    }
  ]);

  const quickActions = [
    "Help me create a store",
    "Generate product descriptions", 
    "WhatsApp store setup",
    "Pricing information"
  ];

  const handleSendMessage = () => {
    if (!message.trim()) return;

    const newMessage = {
      id: messages.length + 1,
      text: message,
      sender: "user",
      timestamp: new Date()
    };

    setMessages(prev => [...prev, newMessage]);
    setMessage("");

    // Enhanced AI responses based on message content
    setTimeout(() => {
      let aiResponse = "";
      const msgLower = message.toLowerCase();
      
      if (msgLower.includes("store") || msgLower.includes("create") || msgLower.includes("setup")) {
        aiResponse = "Great! I can help you set up your WhatsApp store. M-Duka allows you to create a professional online store connected to your WhatsApp number. You can:\n\n✅ Create a custom store page\n✅ Add products with photos\n✅ Accept payments via M-Pesa\n✅ Manage orders through WhatsApp\n\nWhat type of products will you be selling?";
      } else if (msgLower.includes("product") || msgLower.includes("description")) {
        aiResponse = "I can help you create compelling product descriptions! Just tell me:\n\n• Product name\n• Key features\n• Target audience\n• Price range\n\nI'll generate a professional description that converts visitors to customers.";
      } else if (msgLower.includes("pricing") || msgLower.includes("cost") || msgLower.includes("price")) {
        aiResponse = "M-Duka offers flexible pricing:\n\n🆓 **Free Plan**: Up to 10 products, basic features\n💎 **Premium**: More products, custom domain\n🚀 **Business**: Unlimited products, advanced analytics\n\nWhich plan interests you most?";
      } else if (msgLower.includes("whatsapp")) {
        aiResponse = "Perfect! M-Duka integrates seamlessly with WhatsApp Business. Your customers can:\n\n📱 Browse your products\n💬 Chat with you directly\n🛒 Place orders via WhatsApp\n💳 Pay securely\n\nYour WhatsApp becomes a complete mobile shop!";
      } else {
        aiResponse = "Thanks for your message! I'm here to help you succeed with M-Duka. I can assist with store creation, product management, WhatsApp integration, and growing your online business. What would you like to know more about?";
      }

      const response = {
        id: messages.length + 2,
        text: aiResponse,
        sender: "ai",
        timestamp: new Date()
      };
      setMessages(prev => [...prev, response]);
    }, 1000);
  };

  const handleQuickAction = (action: string) => {
    setMessage(action);
    setTimeout(() => handleSendMessage(), 100);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{ duration: 0.3 }}
            className="mb-4 w-80 h-[500px] bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden"
          >
            {/* WhatsApp-style Chat Header */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-4 flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-white/20 flex items-center justify-center border-2 border-white/30">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.891 3.515"/>
                  </svg>
                </div>
                <div>
                  <h3 className="font-semibold text-base">M-Duka AI Assistant</h3>
                  <div className="flex items-center gap-1">
                    <div className="w-2 h-2 bg-green-300 rounded-full animate-pulse"></div>
                    <span className="text-xs opacity-90">Online now</span>
                  </div>
                </div>
              </div>
              <button
                onClick={() => setIsExpanded(false)}
                className="text-white/80 hover:text-white transition-colors p-1 hover:bg-white/10 rounded-full"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Quick Actions */}
            {messages.length === 1 && (
              <div className="p-4 bg-green-50 dark:bg-gray-800 border-b">
                <p className="text-sm text-green-700 dark:text-green-400 mb-3 font-medium">🚀 Quick actions to get started:</p>
                <div className="flex flex-wrap gap-2">
                  {quickActions.map((action, index) => (
                    <button
                      key={index}
                      onClick={() => handleQuickAction(action)}
                      className="text-sm bg-green-500 hover:bg-green-600 text-white px-3 py-2 rounded-full transition-all duration-200 transform hover:scale-105 shadow-sm"
                    >
                      {action}
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50 dark:bg-gray-900">
              {messages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[85%] p-3 rounded-2xl text-sm whitespace-pre-line shadow-sm ${
                      msg.sender === 'user'
                        ? 'bg-green-500 text-white rounded-br-md'
                        : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-bl-md border'
                    }`}
                  >
                    {msg.text}
                  </div>
                </div>
              ))}
            </div>

            {/* WhatsApp-style Chat Input */}
            <div className="p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
              <div className="flex gap-3 items-end">
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type a message..."
                  className="flex-1 text-sm rounded-full border-gray-300 focus:border-green-500 focus:ring-green-500"
                />
                <Button
                  onClick={handleSendMessage}
                  size="sm"
                  className="bg-green-500 hover:bg-green-600 text-white rounded-full p-3 h-auto"
                  disabled={!message.trim()}
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                💬 Powered by M-Duka AI • Let me help you create content
              </p>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Prominent Floating WhatsApp Button */}
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className="bg-green-500 hover:bg-green-600 text-white rounded-full p-5 shadow-2xl hover:shadow-3xl transition-all duration-300 group relative"
        whileHover={{ scale: 1.15 }}
        whileTap={{ scale: 0.9 }}
        animate={!isExpanded ? {
          y: [0, -8, 0],
          scale: [1, 1.05, 1]
        } : {}}
        transition={{
          duration: 3,
          repeat: Infinity,
          repeatType: "reverse",
        }}
        style={{ 
          boxShadow: '0 20px 40px rgba(34, 197, 94, 0.4), 0 0 0 1px rgba(34, 197, 94, 0.1)'
        }}
      >
        <AnimatePresence mode="wait">
          {isExpanded ? (
            <motion.div
              key="minimize"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              <Minimize2 className="h-8 w-8" />
            </motion.div>
          ) : (
            <motion.div
              key="whatsapp"
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              exit={{ scale: 0, rotate: 180 }}
              transition={{ duration: 0.3 }}
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="36" 
                height="36" 
                viewBox="0 0 24 24" 
                fill="currentColor" 
                className="h-9 w-9"
              >
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.891 3.515"/>
              </svg>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Enhanced Pulse effect (only when collapsed) */}
        {!isExpanded && (
          <>
            <div className="absolute inset-0 rounded-full bg-green-400 animate-ping opacity-75"></div>
            <div className="absolute inset-0 rounded-full bg-green-300 animate-pulse opacity-50"></div>
          </>
        )}

        {/* Prominent notification badge */}
        {!isExpanded && (
          <motion.div 
            className="absolute -top-2 -right-2 bg-red-500 text-white text-sm rounded-full h-7 w-7 flex items-center justify-center font-bold border-2 border-white"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            💬
          </motion.div>
        )}
      </motion.button>
    </div>
  );
};

const Index = () => {
  useEffect(() => {
    console.log("Index component mounted");
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (target.tagName === 'A' && target.getAttribute('href')?.startsWith('#')) {
        e.preventDefault();
        const id = target.getAttribute('href')?.substring(1);
        const element = document.getElementById(id as string);
        if (element) {
          window.scrollTo({
            top: element.offsetTop - 80, // Offset for fixed header
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleAnchorClick);
    return () => document.removeEventListener('click', handleAnchorClick);
  }, []);

  return (
    <>
      <SEO 
        title="M-Duka | Turn Your WhatsApp Number into a Mobile Shop"
        description="M-Duka empowers you to transform your WhatsApp into a professional mobile shop, enabling you to showcase products, receive orders, and deliver with ease."
        ogImage="/og-image.png"
      />
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
        className="min-h-screen bg-background text-foreground"
      >
        <Header />
        <main>
          <Hero />
          <KeyBenefits />
          <HowItWorks />
          <StatsSection />
          <Features />
          <Pricing />
          <Testimonials />
        </main>
        <Footer />
      </motion.div>
      
      {/* Floating AI Assistant Chat Widget */}
      <FloatingAIAssistant />
    </>
  );
};

export default Index;
