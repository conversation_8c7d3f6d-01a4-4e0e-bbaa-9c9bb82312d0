
import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import Logo from "./navigation/Logo";
import DesktopNav from "./navigation/DesktopNav";
import AuthButtons from "./navigation/AuthButtons";
import MobileMenuButton from "./navigation/MobileMenuButton";
import MobileNav from "./navigation/MobileNav";

const Header: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const { pathname } = useLocation();

  // Close mobile menu when changing routes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  // Handle mobile menu toggle
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <header className="sticky top-0 z-40 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <Logo />

        {/* Desktop Navigation */}
        <DesktopNav />

        {/* Auth Buttons */}
        <AuthButtons />

        {/* Mobile Menu Button */}
        <MobileMenuButton isOpen={isOpen} onClick={toggleMenu} />

        {/* Mobile Menu */}
        <MobileNav isOpen={isOpen} isMobile={isMobile} />
      </div>
    </header>
  );
};

export default Header;
