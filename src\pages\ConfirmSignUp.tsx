
import React, { useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { toast } from "sonner";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import AuthLayout from "@/components/auth/AuthLayout";
import { Loader2, CheckCircle2, AlertCircle } from "lucide-react";
import { useAuth } from "@/contexts";

const ConfirmSignUp = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { confirmSignUp } = useAuth();
  const [isVerifying, setIsVerifying] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Extract token from the URL hash
  const getTokenFromHash = (): string | null => {
    const hash = location.hash;
    if (!hash) return null;
    
    const tokenMatch = hash.match(/access_token=([^&]*)/);
    return tokenMatch ? tokenMatch[1] : null;
  };

  useEffect(() => {
    const verifySignUp = async () => {
      const token = getTokenFromHash();
      
      if (!token) {
        setIsVerifying(false);
        setError("Invalid or missing verification token. Please check your email and click the verification link again.");
        return;
      }
      
      try {
        const { success, error } = await confirmSignUp(token);
        
        setIsVerifying(false);
        
        if (success) {
          setIsVerified(true);
          toast.success("Email verification successful! You can now sign in.");
        } else {
          setError(error?.message || "Failed to verify your email. The link may have expired.");
        }
      } catch (err: any) {
        setIsVerifying(false);
        setError(err.message || "An unexpected error occurred during verification.");
      }
    };
    
    verifySignUp();
  }, [confirmSignUp]);

  const handleNavigateToSignIn = () => {
    navigate("/signin");
  };

  const renderContent = () => {
    if (isVerifying) {
      return (
        <div className="flex flex-col items-center justify-center">
          <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
          <h2 className="text-xl font-semibold mb-2">Verifying your email</h2>
          <p className="text-muted-foreground text-center">
            Please wait while we verify your email address...
          </p>
        </div>
      );
    }
    
    if (isVerified) {
      return (
        <div className="flex flex-col items-center justify-center">
          <CheckCircle2 className="h-12 w-12 text-green-500 mb-4" />
          <h2 className="text-xl font-semibold mb-2">Email verified!</h2>
          <p className="text-muted-foreground text-center mb-6">
            Your email has been successfully verified. You can now sign in to your account.
          </p>
          <Button onClick={handleNavigateToSignIn} className="w-full">
            Continue to Sign In
          </Button>
        </div>
      );
    }
    
    return (
      <div className="flex flex-col items-center justify-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold mb-2">Verification Failed</h2>
        
        <Alert variant="destructive" className="mb-6">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
        
        <p className="text-muted-foreground text-center mb-6">
          If you're having trouble verifying your email, you can request a new verification link from the sign-in page.
        </p>
        
        <Button onClick={handleNavigateToSignIn} className="w-full">
          Return to Sign In
        </Button>
      </div>
    );
  };

  return (
    <AuthLayout title="Email Verification" subtitle="Verifying your email address">
      {renderContent()}
    </AuthLayout>
  );
};

export default ConfirmSignUp;
