
import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Eye, Save, CheckCircle, AlertCircle } from 'lucide-react';
import { useStore } from '@/contexts';
import { toast } from 'sonner';
import SettingsPreviewPanel from './preview/SettingsPreviewPanel';

interface SettingsLayoutProps {
  defaultTab: string;
  tabs: { value: string; label: string; content: React.ReactNode }[];
  title: string;
  description?: string;
  action?: () => Promise<void>;
  actionLabel?: string;
  actionIcon?: React.ReactNode;
  loading?: boolean;
}

const SettingsLayout: React.FC<SettingsLayoutProps> = ({
  defaultTab,
  tabs,
  title,
  description,
  action,
  actionLabel = 'Save Changes',
  actionIcon = <Save className="h-4 w-4 mr-2" />,
  loading = false,
}) => {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'success' | 'error'>('idle');
  const { currentStore } = useStore();
  
  const handleAction = async () => {
    if (!action) return;
    
    try {
      setSaveStatus('saving');
      await action();
      setSaveStatus('success');
      toast.success('Changes saved successfully');
      
      // Reset status after a delay
      setTimeout(() => {
        setSaveStatus('idle');
      }, 3000);
    } catch (error) {
      console.error('Error saving changes:', error);
      setSaveStatus('error');
      toast.error('Failed to save changes');
      
      // Reset status after a delay
      setTimeout(() => {
        setSaveStatus('idle');
      }, 3000);
    }
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold">{title}</h2>
          {description && <p className="text-muted-foreground mt-1">{description}</p>}
        </div>
        
        <div className="flex flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setPreviewOpen(!previewOpen)}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            {previewOpen ? 'Close Preview' : 'Preview Store'}
          </Button>
          
          {action && (
            <Button
              onClick={handleAction}
              disabled={loading || saveStatus === 'saving'}
              size="sm"
              className="flex items-center gap-2"
            >
              {saveStatus === 'saving' ? (
                <>
                  <span className="animate-spin h-4 w-4 border-2 border-r-0 border-b-0 rounded-full mr-2" />
                  Saving...
                </>
              ) : saveStatus === 'success' ? (
                <>
                  <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                  Saved!
                </>
              ) : saveStatus === 'error' ? (
                <>
                  <AlertCircle className="h-4 w-4 mr-2 text-red-500" />
                  Retry
                </>
              ) : (
                <>
                  {actionIcon}
                  {actionLabel}
                </>
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Preview Panel */}
      <SettingsPreviewPanel open={previewOpen} onOpenChange={setPreviewOpen} />
      
      {/* Settings Content */}
      <Tabs defaultValue={defaultTab} className="w-full">
        <TabsList className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 mb-6">
          {tabs.map((tab) => (
            <TabsTrigger key={tab.value} value={tab.value}>
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>
        
        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value}>
            {tab.content}
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default SettingsLayout;
