
import React from 'react';
import { 
  FormField, 
  FormControl, 
  FormDescription, 
  FormItem, 
  FormLabel 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import SettingsCard from '../common/SettingsCard'; // Import the new component

interface LocalPickupCardProps {
  form: any;
}

const LocalPickupCard: React.FC<LocalPickupCardProps> = ({ form }) => {
  return (
    // Use the new SettingsCard component
    <SettingsCard 
      title="Local Pickup"
      description="Allow customers to pick up orders in person."
    >
      <div className="space-y-6"> {/* Keep the inner spacing */}
        <FormField
          control={form.control}
          name="enableLocalPickup"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Enable Local Pickup</FormLabel>
                <FormDescription>
                  Allow customers to pick up orders at your location
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {form.watch("enableLocalPickup") && (
          <FormField
            control={form.control}
            name="localPickupAddress"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Pickup Address</FormLabel>
                <FormControl>
                  <Input placeholder="123 Store St, City, State, ZIP" {...field} />
                </FormControl>
                <FormDescription>
                  Address where customers can pick up their orders.
                </FormDescription>
              </FormItem>
            )}
          />
        )}
      </div>
    </SettingsCard>
  );
};

export default LocalPickupCard;
