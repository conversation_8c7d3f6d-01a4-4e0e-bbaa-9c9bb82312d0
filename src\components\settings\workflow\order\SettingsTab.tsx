
import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Zap, ExternalLink, MessageCircle, Save, Settings } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

const SettingsTab: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Order Notification Settings</CardTitle>
        <CardDescription>
          Configure how order status changes trigger notifications
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <div className="flex items-start gap-3">
            <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-800">Pubbly Connect Integration</p>
              <p className="text-sm text-blue-700">
                Your order notification system is powered by Pubbly Connect automation service.
              </p>
              <Button variant="link" className="h-auto p-0 text-blue-700 mt-1">
                <ExternalLink className="h-3 w-3 mr-1" />
                View in Pubbly Connect dashboard
              </Button>
            </div>
          </div>
        </div>
        
        <div className="space-y-4">
          <h3 className="text-base font-medium">Communication Channels</h3>
          
          <div className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <Label className="text-base flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                WhatsApp Notifications
              </Label>
              <p className="text-sm text-muted-foreground">
                Send order updates via WhatsApp
              </p>
            </div>
            <Switch defaultChecked />
          </div>
          
          <div className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <Label className="text-base flex items-center gap-2">
                <MessageCircle className="h-4 w-4" />
                Email Notifications
              </Label>
              <p className="text-sm text-muted-foreground">
                Send order updates via email
              </p>
            </div>
            <Switch defaultChecked />
          </div>
        </div>
        
        <Separator />
        
        <div className="space-y-4">
          <h3 className="text-base font-medium">Order Status Triggers</h3>
          
          <div className="grid gap-4">
            {['Order Placed', 'Payment Received', 'Processing', 'Ready for Pickup', 'Shipped', 'Delivered', 'Cancelled'].map((status) => (
              <div key={status} className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div>
                  <p className="font-medium">{status}</p>
                </div>
                <div className="flex items-center gap-2">
                  <Badge>WhatsApp</Badge>
                  {status !== 'Cancelled' && status !== 'Ready for Pickup' && (
                    <Badge variant="outline">Email</Badge>
                  )}
                  <Button variant="ghost" size="sm">
                    <Settings className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button className="ml-auto">
          <Save className="h-4 w-4 mr-2" />
          Save Settings
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SettingsTab;
