
import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useStore, useProduct } from '@/contexts';
import { Button } from '@/components/ui/button';
import { Plus, Trash2, Edit, AlertCircle } from 'lucide-react';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';

interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  productCount: number;
}

const CategoryManagement: React.FC = () => {
  const navigate = useNavigate();
  const { currentStore, isLoading: storeIsLoading } = useStore();
  const { products, fetchProducts, isLoading: productIsLoading } = useProduct();
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [newCategory, setNewCategory] = useState({ name: '', description: '' });
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [categoryToDelete, setCategoryToDelete] = useState<Category | null>(null);

  useEffect(() => {
    if (currentStore?.id) {
      fetchProducts(currentStore.id);
    }
  }, [currentStore?.id, fetchProducts]);

  useEffect(() => {
    if (products.length > 0) {
      // Extract categories from products and count products per category
      const categoryMap = products.reduce((acc: Record<string, any>, product) => {
        if (product.category) {
          if (!acc[product.category]) {
            acc[product.category] = {
              name: product.category,
              slug: product.category.toLowerCase().replace(/\s+/g, '-'),
              productCount: 0,
            };
          }
          acc[product.category].productCount += 1;
        }
        return acc;
      }, {});

      const categoriesArray = Object.values(categoryMap).map((cat: any, index) => ({
        id: `cat-${index + 1}`,
        ...cat,
      }));

      setCategories(categoriesArray);
    }
  }, [products]);

  const handleCreateCategory = () => {
    if (!newCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    const slug = newCategory.name.toLowerCase().replace(/\s+/g, '-');
    const exists = categories.some(cat => cat.slug === slug);
    
    if (exists) {
      toast.error('A category with this name already exists');
      return;
    }

    const newCategoryObj = {
      id: `cat-${categories.length + 1}`,
      name: newCategory.name,
      slug,
      description: newCategory.description,
      productCount: 0
    };

    setCategories([...categories, newCategoryObj]);
    setNewCategory({ name: '', description: '' });
    setIsDialogOpen(false);
    toast.success('Category created successfully');
  };

  const handleUpdateCategory = () => {
    if (!editingCategory || !editingCategory.name.trim()) {
      toast.error('Category name is required');
      return;
    }

    const updatedCategories = categories.map(cat => 
      cat.id === editingCategory.id ? editingCategory : cat
    );

    setCategories(updatedCategories);
    setEditingCategory(null);
    setIsDialogOpen(false);
    toast.success('Category updated successfully');
  };

  const handleDeleteCategory = () => {
    if (!categoryToDelete) return;

    const updatedCategories = categories.filter(cat => cat.id !== categoryToDelete.id);
    setCategories(updatedCategories);
    setCategoryToDelete(null);
    setIsDeleteDialogOpen(false);
    toast.success('Category deleted successfully');
  };

  const openEditDialog = (category: Category) => {
    setEditingCategory(category);
    setIsDialogOpen(true);
  };

  const openDeleteDialog = (category: Category) => {
    setCategoryToDelete(category);
    setIsDeleteDialogOpen(true);
  };

  return (
    <DashboardLayout>
      <Helmet>
        <title>Category Management - m-duka</title>
      </Helmet>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Category Management</h1>
          <Button 
            onClick={() => {
              setEditingCategory(null);
              setNewCategory({ name: '', description: '' });
              setIsDialogOpen(true);
            }} 
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Add Category
          </Button>
        </div>

        {storeIsLoading || productIsLoading ? (
          <Card>
            <CardContent className="p-8">
              <div className="flex justify-center">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            </CardContent>
          </Card>
        ) : categories.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <div className="mb-4 flex justify-center">
                <AlertCircle className="h-12 w-12 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-medium mb-2">No Categories</h3>
              <p className="text-muted-foreground mb-4">
                You haven't created any categories yet. Categories help you organize your products.
              </p>
              <Button onClick={() => setIsDialogOpen(true)}>Create Your First Category</Button>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>All Categories</CardTitle>
              <CardDescription>
                Manage your product categories and their descriptions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Slug</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="text-right">Products</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {categories.map((category) => (
                    <TableRow key={category.id}>
                      <TableCell className="font-medium">{category.name}</TableCell>
                      <TableCell>{category.slug}</TableCell>
                      <TableCell className="max-w-md truncate">
                        {category.description || '-'}
                      </TableCell>
                      <TableCell className="text-right">
                        <Badge variant="outline">{category.productCount}</Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => openEditDialog(category)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            className="text-destructive hover:text-destructive"
                            onClick={() => openDeleteDialog(category)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Create/Edit Category Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? 'Edit Category' : 'Create New Category'}
            </DialogTitle>
            <DialogDescription>
              {editingCategory 
                ? 'Update category details below.' 
                : 'Add a new category to organize your products.'}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="name">Category Name</Label>
              <Input
                id="name"
                value={editingCategory ? editingCategory.name : newCategory.name}
                onChange={(e) => {
                  if (editingCategory) {
                    setEditingCategory({ ...editingCategory, name: e.target.value });
                  } else {
                    setNewCategory({ ...newCategory, name: e.target.value });
                  }
                }}
                placeholder="e.g., Electronics, Clothing, Home Decor"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Input
                id="description"
                value={editingCategory ? (editingCategory.description || '') : newCategory.description}
                onChange={(e) => {
                  if (editingCategory) {
                    setEditingCategory({ ...editingCategory, description: e.target.value });
                  } else {
                    setNewCategory({ ...newCategory, description: e.target.value });
                  }
                }}
                placeholder="Brief description of this category"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={editingCategory ? handleUpdateCategory : handleCreateCategory}>
              {editingCategory ? 'Update Category' : 'Create Category'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the category "{categoryToDelete?.name}"?
              {categoryToDelete && categoryToDelete.productCount > 0 && (
                <p className="mt-2 text-destructive">
                  This category contains {categoryToDelete.productCount} products.
                  Deleting it will not remove the products, but they will no longer be categorized.
                </p>
              )}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteCategory}>
              Delete Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default CategoryManagement;
