
import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import { useStore, useProduct } from '@/contexts';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProductGrid from '@/components/product/ProductGrid';
import EmptyProductState from '@/components/product/EmptyProductState';

const Products: React.FC = () => {
  const navigate = useNavigate();
  const { currentStore, stores, isLoading: storeIsLoading } = useStore();
  const { products, fetchProducts, deleteProduct, isLoading: productIsLoading } = useProduct();
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  useEffect(() => {
    if (currentStore?.id) {
      fetchProducts(currentStore.id);
    }
  }, [currentStore?.id, fetchProducts]);

  const handleDelete = async (productId: string) => {
    try {
      setIsDeleting(productId);
      await deleteProduct(productId);
    } catch (error) {
      console.error('Failed to delete product:', error);
    } finally {
      setIsDeleting(null);
    }
  };

  // Handle no stores case
  const showNoStoresMessage = !storeIsLoading && (!stores || stores.length === 0);

  return (
    <DashboardLayout>
      <Helmet>
        <title>Products - m-duka</title>
      </Helmet>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-3xl font-bold">Products</h1>
          {currentStore && (
            <Button onClick={() => navigate('/create-product')} className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Product
            </Button>
          )}
        </div>

        {showNoStoresMessage ? (
          <div className="text-center py-16 border rounded-lg">
            <h3 className="text-xl font-medium mb-4">You need to create a store first</h3>
            <p className="text-muted-foreground mb-6 max-w-md mx-auto">
              Before you can add products, you need to create a store to add them to.
            </p>
            <Button onClick={() => navigate('/create-store')}>
              Create Store
            </Button>
          </div>
        ) : storeIsLoading || (productIsLoading && products.length === 0) ? (
          <ProductGrid 
            products={[]} 
            isLoading={true} 
            onDelete={handleDelete} 
            isDeleting={isDeleting} 
          />
        ) : products.length === 0 ? (
          <EmptyProductState hasStore={!!currentStore} />
        ) : (
          <ProductGrid 
            products={products} 
            isLoading={false} 
            onDelete={handleDelete} 
            isDeleting={isDeleting} 
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Products;
