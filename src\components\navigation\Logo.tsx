
import React from "react";
import { Link } from "react-router-dom";

const Logo: React.FC = () => {
  return (
    <Link
      to="/"
      className="text-2xl font-bold tracking-tight flex items-center"
    >
      <div className="relative">
        <div className="bg-green-500 rounded-xl text-white mr-1.5 px-3 py-2 inline-block shadow-md">
          M
          {/* SMS-style tail instead of speech bubble */}
          <div className="absolute -bottom-[6px] left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rotate-45 rounded-sm"></div>
        </div>
      </div>
      <span className="text-gray-800">-</span>
      <span>Duka</span>
    </Link>
  );
};

export default Logo;
