
import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import StaffForm from './StaffForm';
import { StaffFormValues, StaffMember } from './types';
import { ScrollArea } from '@/components/ui/scroll-area';

interface StaffDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: StaffFormValues) => Promise<boolean | void>;
  staff?: StaffMember;
  isLoading?: boolean;
}

const StaffDialog: React.FC<StaffDialogProps> = ({
  isOpen,
  onClose,
  onSubmit,
  staff,
  isLoading = false,
}) => {
  const handleSubmit = async (data: StaffFormValues) => {
    const success = await onSubmit(data);
    if (success) {
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] w-full max-w-[95%]">
        <DialogHeader>
          <DialogTitle>{staff ? 'Edit Staff Member' : 'Add Staff Member'}</DialogTitle>
          <DialogDescription>
            {staff 
              ? 'Update details and permissions for this staff member.' 
              : 'Add a new staff member to your store by entering their email and selecting their role.'}
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh] overflow-y-auto pr-2">
          <StaffForm 
            onSubmit={handleSubmit} 
            initialValues={staff}
            isLoading={isLoading}
          />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default StaffDialog;
