
import React from "react";
import { <PERSON> } from "react-router-dom";
import { User } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts";

interface UserMenuButtonProps {
  variant?: "icon" | "full";
  onClick?: () => void;
}

const UserMenuButton: React.FC<UserMenuButtonProps> = ({ 
  variant = "icon",
  onClick 
}) => {
  const { isAuthenticated } = useAuth();

  if (variant === "full") {
    return isAuthenticated ? (
      <Link to="/shop/account" onClick={onClick}>
        <Button variant="outline">
          <User className="h-4 w-4 mr-2" />
          Account
        </Button>
      </Link>
    ) : (
      <Link to="/signin" onClick={onClick}>
        <Button>Sign In</Button>
      </Link>
    );
  }

  return isAuthenticated ? (
    <Link to="/shop/account">
      <Button variant="ghost" size="icon">
        <User className="h-5 w-5" />
      </Button>
    </Link>
  ) : (
    <Link to="/signin">
      <Button variant="outline" size="sm">
        Sign In
      </Button>
    </Link>
  );
};

export default UserMenuButton;
