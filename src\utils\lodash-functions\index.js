
/**
 * This module provides native JavaScript implementations of common lodash functions
 * to avoid the need for the lodash library.
 */

import isNil from './isNil.js';
import isFunction from './isFunction.js';
import isObject from './isObject.js';
import isString from './isString.js';
import isEqual from './isEqual.js';
import sortBy from './sortBy.js';
import throttle from './throttle.js';
import last from './last.js';
import get from './get.js';
import upperFirst from './upperFirst.js';
import uniqBy from './uniqBy.js';
import max from './max.js';
import mapValues from './mapValues.js';
import find from './find.js';
import range from './range.js';
import maxBy from './maxBy.js';
import minBy from './minBy.js';
import first from './first.js';
import some from './some.js';
import isNaN from './isNaN.js';
import omit from './omit.js';
import min from './min.js';
import sumBy from './sumBy.js';
import isNumber from './isNumber.js';
import flatMap from './flatMap.js';
import isPlainObject from './isPlainObject.js';
import isBoolean from './isBoolean.js';
import every from './every.js';
import memoize from './memoize.js';

const lodashFunctions = {
  isNil,
  isFunction,
  isObject,
  isString,
  isEqual,
  sortBy,
  throttle,
  last,
  get,
  upperFirst,
  uniqBy,
  max,
  mapValues,
  find,
  range,
  maxBy,
  minBy,
  first,
  some,
  isNaN,
  omit,
  min,
  sumBy,
  isNumber,
  flatMap,
  isPlainObject,
  isBoolean,
  every,
  memoize
};

// Make each function directly importable
export {
  isNil,
  isFunction,
  isObject,
  isString,
  isEqual,
  sortBy,
  throttle,
  last,
  get,
  upperFirst,
  uniqBy,
  max,
  mapValues,
  find,
  range,
  maxBy,
  minBy,
  first,
  some,
  isNaN,
  omit,
  min,
  sumBy,
  isNumber,
  flatMap,
  isPlainObject,
  isBoolean,
  every,
  memoize
};

// Support default import
export default lodashFunctions;

// Support CJS commonjs require
if (typeof module === 'object' && module.exports) {
  module.exports = Object.assign(lodashFunctions, { default: lodashFunctions });
  
  // Add each function to module.exports to support direct imports
  Object.keys(lodashFunctions).forEach(key => {
    module.exports[key] = lodashFunctions[key];
  });
}
