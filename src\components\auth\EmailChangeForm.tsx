
import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { useAuth } from "@/contexts";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  password: z.string().min(1, { message: "Please enter your current password" }),
});

type FormValues = z.infer<typeof formSchema>;

interface EmailChangeFormProps {
  onSuccess?: () => void;
}

const EmailChangeForm: React.FC<EmailChangeFormProps> = ({ onSuccess }) => {
  const { updateEmail, reauthenticate } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailChangeInitiated, setEmailChangeInitiated] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (isSubmitting) return;
    
    setFormError(null);
    setIsSubmitting(true);
    
    let toastId: string | number | undefined;
    
    try {
      // First reauthenticate the user
      toastId = toast.loading("Verifying your identity...");
      
      const { success: authSuccess, error: authError } = await reauthenticate(data.password);
      
      if (!authSuccess) {
        if (toastId) toast.dismiss(toastId);
        throw authError || new Error("Authentication failed");
      }
      
      toast.loading("Initiating email change...", { id: toastId });
      
      // Then update the email
      const { success, error } = await updateEmail(data.email);
      
      if (toastId) toast.dismiss(toastId);
      
      if (!success) {
        throw error;
      }
      
      setEmailChangeInitiated(true);
      toast.success("Email change initiated! Please check your new email for verification.");
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Email change error:", error);
      if (toastId) toast.dismiss(toastId);
      setFormError(error.message || "Failed to change email");
      toast.error(error.message || "Failed to change email");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {emailChangeInitiated ? (
        <Alert className="mb-6 bg-green-50">
          <AlertDescription>
            <p className="text-sm">
              We've sent a verification link to your new email. 
              Please click the link to complete the email change.
            </p>
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {formError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>New Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Current Password</FormLabel>
                    <FormControl>
                      <Input 
                        type="password" 
                        placeholder="********" 
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? "Changing Email..." : "Change Email"}
              </Button>
            </form>
          </Form>
        </>
      )}
    </>
  );
};

export default EmailChangeForm;
