
import React, { useState, useEffect } from 'react';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import CustomerTable from '@/components/customers/CustomerTable';
import CustomerToolbar from '@/components/customers/CustomerToolbar';
import CustomerEmptyState from '@/components/customers/CustomerEmptyState';
import CustomerFilters from '@/components/customers/CustomerFilters';
import { CustomerType } from '@/types/customer';
import { useCustomer } from '@/contexts/customer/CustomerContext';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

const Customers = () => {
  const { customers, isLoading, refreshCustomers, error } = useCustomer();
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerType[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [statusFilter, setStatusFilter] = useState<string>('all');

  // Apply search and filters to the customers
  useEffect(() => {
    let result = [...customers];
    
    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      result = result.filter(
        customer => 
          customer.name?.toLowerCase().includes(query) || 
          customer.email?.toLowerCase().includes(query)
      );
    }
    
    // Apply status filter
    if (statusFilter !== 'all') {
      result = result.filter(customer => customer.status === statusFilter);
    }
    
    setFilteredCustomers(result);
    setCurrentPage(1); // Reset to first page on filter changes
  }, [customers, searchQuery, statusFilter]);

  // Calculate pagination info
  const totalItems = filteredCustomers.length;
  const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
  const paginatedCustomers = filteredCustomers.slice(startIndex, endIndex);

  const handleClearFilters = () => {
    setSearchQuery('');
    setStatusFilter('all');
  };

  return (
    <DashboardLayout>
      <div className="container mx-auto px-4 py-8">
        <CustomerToolbar 
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          totalCustomers={totalItems}
        />
      
        <div className="mt-4">
          <CustomerFilters 
            statusFilter={statusFilter}
            onStatusFilterChange={setStatusFilter}
            onClearFilters={handleClearFilters}
          />
        </div>

        {error && (
          <Alert variant="destructive" className="my-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error.message || 'Failed to load customers. Please try again.'}
            </AlertDescription>
          </Alert>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : totalItems > 0 ? (
          <CustomerTable 
            customers={paginatedCustomers} 
          />
        ) : (
          <CustomerEmptyState 
            refreshCustomers={refreshCustomers} 
          />
        )}
      </div>
    </DashboardLayout>
  );
};

export default Customers;
