
import React from 'react';
import { Button } from '@/components/ui/button';
import { RefreshCw, Users } from 'lucide-react';

export interface CustomerEmptyStateProps {
  refreshCustomers: () => Promise<void>;
}

const CustomerEmptyState = ({ refreshCustomers }: CustomerEmptyStateProps) => {
  return (
    <div className="flex flex-col items-center justify-center h-64 border border-dashed rounded-lg p-8 mt-6">
      <div className="h-20 w-20 rounded-full bg-primary/10 flex items-center justify-center mb-4">
        <Users className="h-10 w-10 text-primary" />
      </div>
      <h3 className="text-xl font-semibold mb-2">No customers found</h3>
      <p className="text-muted-foreground text-center max-w-md mb-4">
        You don't have any customers yet. Customers will appear here after they make their first purchase.
      </p>
      <Button 
        variant="outline"
        onClick={refreshCustomers}
        className="flex items-center"
      >
        <RefreshCw className="h-4 w-4 mr-2" />
        Refresh Data
      </Button>
    </div>
  );
};

export default CustomerEmptyState;
