
import React from 'react';
import { Badge } from '@/components/ui/badge';

interface StoreStatusBadgeProps {
  status: string;
}

const StoreStatusBadge: React.FC<StoreStatusBadgeProps> = ({ status }) => {
  switch(status) {
    case 'active':
      return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Active</Badge>;
    case 'pending-review':
      return <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">Pending Review</Badge>;
    case 'suspended':
      return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Suspended</Badge>;
    default:
      return <Badge variant="outline">{status}</Badge>;
  }
};

export default StoreStatusBadge;
