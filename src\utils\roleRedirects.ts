import { UserRole } from '@/constants/roles';

/**
 * Get the appropriate dashboard URL based on user role
 */
export const getDashboardUrlByRole = (role: UserRole): string => {
  switch (role) {
    case UserRole.Admin:
      return '/admin';
    case UserRole.StoreOwner:
      return '/dashboard';
    case UserRole.Customer:
      return '/account';
    default:
      return '/dashboard';
  }
};

/**
 * Get a user-friendly role description
 */
export const getRoleDescription = (role: UserRole): string => {
  switch (role) {
    case UserRole.Admin:
      return 'Administrator';
    case UserRole.StoreOwner:
      return 'Store Owner';
    case UserRole.Customer:
      return 'Customer';
    default:
      return 'User';
  }
};

/**
 * Check if a user has permission to access a specific route
 */
export const hasRoutePermission = (userRole: UserRole, requiredRoles: UserRole[]): boolean => {
  return requiredRoles.includes(userRole);
};

/**
 * Get the sign-in redirect URL with role context
 */
export const getSignInRedirectUrl = (intendedRole?: UserRole): string => {
  const baseUrl = window.location.origin;
  
  // If we have an intended role, redirect to that role's dashboard
  if (intendedRole) {
    return `${baseUrl}${getDashboardUrlByRole(intendedRole)}`;
  }
  
  // Default to general dashboard
  return `${baseUrl}/dashboard`;
}; 