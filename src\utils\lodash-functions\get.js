
/**
 * Gets the value at path of object.
 *
 * @param {Object} object - The object to query
 * @param {string|Array} path - The path of the property to get
 * @param {*} defaultValue - The value returned for undefined resolved values
 * @returns {*} Returns the resolved value
 */
function get(object, path, defaultValue) {
  if (object === null || object === undefined) {
    return defaultValue;
  }
  
  const keys = Array.isArray(path) ? path : path.split('.');
  let result = object;
  
  for (let i = 0; i < keys.length; i++) {
    if (result === null || result === undefined) {
      return defaultValue;
    }
    result = result[keys[i]];
  }
  
  return result === undefined ? defaultValue : result;
}

// Support both ESM and CJS
export default get;
