import React from 'react';
import { 
  FormField, 
  FormControl, 
  FormItem, 
  FormLabel, 
  FormDescription 
} from "@/components/ui/form";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";

interface CountrySelectorProps {
  form: any;
  onCountryChange: (country: string) => void;
}

const CountrySelector: React.FC<CountrySelectorProps> = ({ form, onCountryChange }) => {
  return (
    <FormField
      control={form.control}
      name="country"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Country</FormLabel>
          <Select
            onValueChange={(value) => {
              field.onChange(value);
              onCountryChange(value);
            }}
            defaultValue={field.value}
          >
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a country" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {/* East African Countries */}
              <SelectItem value="Kenya">Kenya</SelectItem>
              <SelectItem value="Tanzania">Tanzania</SelectItem>
              <SelectItem value="Uganda">Uganda</SelectItem>
              
              {/* Other African Countries */}
              <SelectItem value="Nigeria">Nigeria</SelectItem>
              <SelectItem value="Ghana">Ghana</SelectItem>
              <SelectItem value="South Africa">South Africa</SelectItem>
              <SelectItem value="Egypt">Egypt</SelectItem>
              <SelectItem value="Morocco">Morocco</SelectItem>
              <SelectItem value="Ethiopia">Ethiopia</SelectItem>
              <SelectItem value="Rwanda">Rwanda</SelectItem>
              <SelectItem value="Senegal">Senegal</SelectItem>
              
              {/* Non-African Countries */}
              <SelectItem value="United States">United States</SelectItem>
              <SelectItem value="Canada">Canada</SelectItem>
              <SelectItem value="United Kingdom">United Kingdom</SelectItem>
              <SelectItem value="Other">Other</SelectItem>
            </SelectContent>
          </Select>
          <FormDescription>
            Select your primary country of operation to configure delivery settings.
          </FormDescription>
        </FormItem>
      )}
    />
  );
};

export default CountrySelector;
