
import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { StaffMember } from './types';

interface DeleteStaffDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  staff?: StaffMember;
  isLoading?: boolean;
}

const DeleteStaffDialog: React.FC<DeleteStaffDialogProps> = ({
  isOpen,
  onClose,
  onConfirm,
  staff,
  isLoading = false,
}) => {
  if (!staff) return null;

  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove Staff Member</AlertDialogTitle>
          <AlertDialogDescription>
            Are you sure you want to remove {staff.email} from your store staff?
            They will no longer have access to your store.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
          <AlertDialogAction 
            onClick={onConfirm}
            disabled={isLoading}
            className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
          >
            {isLoading ? 'Removing...' : 'Remove Staff'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteStaffDialog;
