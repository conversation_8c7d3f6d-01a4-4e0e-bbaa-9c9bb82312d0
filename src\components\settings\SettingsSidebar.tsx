
import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeft, Store, Settings, CreditCard, ShoppingCart, Truck, MessageCircle, Workflow, Globe, Users, Search, Building2, FileText, User2, File, Link2, Gift } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

// Define the valid badge variant types
type BadgeVariant = "default" | "destructive" | "outline" | "secondary" | "success";

interface MenuItem {
  name: string;
  path: string;
  icon: React.ReactNode;
  isOverlay?: boolean;
  badge?: {
    text: string;
    variant: BadgeVariant;
  };
}

interface MenuCategory {
  title: string;
  type: 'category';
  items: MenuItem[];
}

const SettingsSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const currentPath = location.pathname;
  
  const menuItems: MenuCategory[] = [
    { 
      title: 'Store',
      type: 'category',
      items: [
        { name: 'General', path: '/settings/general', icon: <Settings className="h-4 w-4 mr-2" /> },
        { name: 'Payments', path: '/settings/payments', icon: <CreditCard className="h-4 w-4 mr-2" /> },
        { name: 'Checkout', path: '/settings/checkout', icon: <ShoppingCart className="h-4 w-4 mr-2" /> },
        { name: 'Delivery', path: '/settings/delivery', icon: <Truck className="h-4 w-4 mr-2" /> },
        { 
          name: 'WhatsApp Business', 
          path: '/settings/whatsapp', 
          icon: <MessageCircle className="h-4 w-4 mr-2" />,
          badge: { text: 'BUSINESS', variant: 'outline' }
        },
        { 
          name: 'Workflow', 
          path: '/settings/workflow', 
          icon: <Workflow className="h-4 w-4 mr-2" />,
          isOverlay: true,
          badge: { text: 'PREMIUM', variant: 'outline' }
        },
        { 
          name: 'Domains', 
          path: '/settings/domains', 
          icon: <Globe className="h-4 w-4 mr-2" />,
          badge: { text: 'PREMIUM', variant: 'outline' }
        },
        { 
          name: 'Membership', 
          path: '/settings/membership', 
          icon: <Users className="h-4 w-4 mr-2" />,
          badge: { text: 'BUSINESS', variant: 'outline' }
        },
        { 
          name: 'SEO and trackings', 
          path: '/settings/seo', 
          icon: <Search className="h-4 w-4 mr-2" />,
          badge: { text: 'PREMIUM', variant: 'outline' }
        },
      ]
    },
    {
      title: 'Organization',
      type: 'category',
      items: [
        { name: 'Details', path: '/settings/details', icon: <Building2 className="h-4 w-4 mr-2" /> },
        { name: 'Billing', path: '/settings/billing', icon: <FileText className="h-4 w-4 mr-2" /> },
        { 
          name: 'Staff', 
          path: '/settings/staff', 
          icon: <User2 className="h-4 w-4 mr-2" />,
          badge: { text: 'BUSINESS', variant: 'outline' }
        },
        { name: 'Files', path: '/settings/files', icon: <File className="h-4 w-4 mr-2" /> },
        { 
          name: 'Integrations', 
          path: '/settings/integrations', 
          icon: <Link2 className="h-4 w-4 mr-2" />,
          badge: { text: 'BUSINESS', variant: 'outline' }
        },
        { name: 'Affiliate Program', path: '/settings/affiliate', icon: <Gift className="h-4 w-4 mr-2" /> },
      ]
    }
  ];

  // Handle click for overlay items
  const handleItemClick = (item: MenuItem, event: React.MouseEvent) => {
    if (item.isOverlay) {
      event.preventDefault();
      navigate(item.path);
    }
  };

  return (
    <div className="w-64 border-r h-full bg-background flex flex-col">
      <div className="p-4 border-b">
        <div className="flex items-center gap-2 mb-6">
          <Settings className="h-5 w-5" />
          <h2 className="font-medium text-lg">Settings</h2>
        </div>
        <Button variant="outline" size="sm" className="w-full justify-start mb-2" onClick={() => navigate('/dashboard')}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Return to Dashboard
        </Button>
        <Button variant="outline" size="sm" className="w-full justify-start" asChild>
          <Link to="/products">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Return to products
          </Link>
        </Button>
      </div>
      
      <div className="flex-1 overflow-auto py-2">
        {menuItems.map((category, categoryIndex) => (
          <div key={categoryIndex} className="mb-6">
            {category.title && (
              <h3 className="px-4 text-sm font-medium text-muted-foreground mb-2">
                {category.title}
              </h3>
            )}
            <ul className="space-y-1 px-2">
              {category.items.map((item, itemIndex) => (
                <li key={itemIndex}>
                  <Link
                    to={item.path}
                    className={cn(
                      "flex items-center justify-between px-2 py-1.5 rounded-md text-sm",
                      (currentPath === item.path || currentPath.startsWith(`${item.path}/`))
                        ? "bg-secondary font-medium" 
                        : "hover:bg-secondary/50"
                    )}
                    onClick={(e) => handleItemClick(item, e)}
                  >
                    <div className="flex items-center">
                      {item.icon}
                      <span>{item.name}</span>
                    </div>
                    {item.badge && (
                      <Badge variant={item.badge.variant} className="ml-2 text-xs font-normal">
                        {item.badge.text}
                      </Badge>
                    )}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SettingsSidebar;
