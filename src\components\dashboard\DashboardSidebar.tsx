
import React from 'react';
import { useLocation } from 'react-router-dom';
import { ScrollArea } from '@/components/ui/scroll-area';
import SidebarHeader from './sidebar/SidebarHeader';
import NavigationItems from './sidebar/navigation/NavigationItems';

const DashboardSidebar: React.FC = () => {
  const location = useLocation();
  
  // Check if we're on the dashboard page or not
  const isDashboardPage = location.pathname === '/dashboard';

  return (
    <div className="hidden md:flex flex-col h-screen w-64 border-r bg-background">
      <SidebarHeader isDashboardPage={isDashboardPage} />
      
      <ScrollArea className="flex-1">
        <div className="px-4 pb-6">
          <NavigationItems />
        </div>
      </ScrollArea>
    </div>
  );
};

export default DashboardSidebar;
