
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface TaxSettingsCardProps {
  taxRate: string;
  setTaxRate: (value: string) => void;
  taxMethod: string;
  setTaxMethod: (value: string) => void;
  taxId: string;
  setTaxId: (value: string) => void;
}

const TaxSettingsCard = ({
  taxRate,
  setTaxRate,
  taxMethod,
  setTaxMethod,
  taxId,
  setTaxId
}: TaxSettingsCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Tax Settings</CardTitle>
        <CardDescription>
          Configure tax information for your store
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="tax-rate">Tax rate (%)</Label>
          <Input 
            id="tax-rate"
            value={taxRate}
            onChange={(e) => setTaxRate(e.target.value)}
            placeholder="Enter your tax rate (e.g. 18.00)"
          />
        </div>
        
        <div className="space-y-2">
          <Label>Tax method</Label>
          <RadioGroup value={taxMethod} onValueChange={setTaxMethod} className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="inclusive" id="inclusive" />
              <Label htmlFor="inclusive">Inclusive</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="exclusive" id="exclusive" />
              <Label htmlFor="exclusive">Exclusive</Label>
            </div>
          </RadioGroup>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="tax-id">Tax ID</Label>
          <Input 
            id="tax-id"
            value={taxId}
            onChange={(e) => setTaxId(e.target.value)}
            placeholder="Enter your tax identification number"
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default TaxSettingsCard;
