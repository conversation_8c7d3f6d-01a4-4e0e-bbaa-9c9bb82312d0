import React, { createContext, useContext, useState, useEffect, useMemo } from 'react';
import { useStore } from '../store/StoreContext';
import { productOperations } from '@/lib/supabase';
import { Product, toUIProduct, ProductContextType, ProductProviderProps, ProductFilters } from './types';
import { toast } from 'sonner';

const ProductContext = createContext<ProductContextType | undefined>(undefined);

export const ProductProvider: React.FC<ProductProviderProps> = ({ children }) => {
  const { currentStore } = useStore();
  const [products, setProducts] = useState<Product[]>([]);
  const [currentProduct, setCurrentProduct] = useState<Product | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filters, setFilters] = useState<ProductFilters>({});

  // Compute filtered products based on search term and filters
  const filteredProducts = useMemo(() => {
    let result = [...products];

    // Apply search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      result = result.filter(product => 
        product.name?.toLowerCase().includes(searchLower) ||
        product.description?.toLowerCase().includes(searchLower) ||
        product.category?.toLowerCase().includes(searchLower) ||
        product.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Apply filters
    if (filters.category) {
      result = result.filter(product => product.category === filters.category);
    }

    if (filters.minPrice !== undefined) {
      result = result.filter(product => (product.price || 0) >= filters.minPrice!);
    }

    if (filters.maxPrice !== undefined) {
      result = result.filter(product => (product.price || 0) <= filters.maxPrice!);
    }

    if (filters.isActive !== undefined) {
      result = result.filter(product => product.is_active === filters.isActive);
    }

    if (filters.inStock === true) {
      result = result.filter(product => (product.stock_quantity || 0) > 0);
    }

    if (filters.tags && filters.tags.length > 0) {
      result = result.filter(product => 
        filters.tags!.some(tag => product.tags?.includes(tag))
      );
    }

    return result;
  }, [products, searchTerm, filters]);

  // Get unique categories for filtering
  const categories = useMemo(() => {
    const categorySet = new Set<string>();
    products.forEach(product => {
      if (product.category) {
        categorySet.add(product.category);
      }
    });
    return Array.from(categorySet).sort();
  }, [products]);

  const searchProducts = (newSearchTerm: string) => {
    setSearchTerm(newSearchTerm);
  };

  const updateFilters = (newFilters: ProductFilters) => {
    setFilters(prevFilters => ({ ...prevFilters, ...newFilters }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchTerm("");
  };

  const createProduct = async (productData: any, storeId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Ensure required fields are provided
      const productPayload = {
        store_id: storeId,
        name: productData.name || 'Untitled Product',
        category: productData.category || 'General',
        price: productData.price || 0,
        description: productData.description || '',
        is_active: productData.is_active !== undefined ? productData.is_active : true,
        stock_quantity: productData.stock_quantity || 0,
        ...productData
      };
      
      const { data: product, error } = await productOperations.create(productPayload);
      if (error) throw error;
      const uiProduct = toUIProduct(product);
      setProducts(prev => [...prev, uiProduct]);
      return uiProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create product';
      setError(new Error(errorMessage));
      toast.error(`Error creating product: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const updateProduct = async (productId: string, productData: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: product, error } = await productOperations.update(productId, productData);
      if (error) throw error;
      const uiProduct = toUIProduct(product);
      setProducts(prev => prev.map(p => p.id === productId ? uiProduct : p));
      return uiProduct;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update product';
      setError(new Error(errorMessage));
      toast.error(`Error updating product: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteProduct = async (productId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { error } = await productOperations.delete(productId);
      if (error) throw error;
      setProducts(prev => prev.filter(p => p.id !== productId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete product';
      setError(new Error(errorMessage));
      toast.error(`Error deleting product: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const fetchProducts = async (storeId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: products, error } = await productOperations.getByStore(storeId);
      if (error) throw error;
      const uiProducts = (products || []).map(toUIProduct);
      setProducts(uiProducts);
      return uiProducts;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch products';
      setError(new Error(errorMessage));
      toast.error(`Error fetching products: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  const getProduct = async (productId: string) => {
    try {
      setIsLoading(true);
      setError(null);
      const { data: product, error } = await productOperations.getById(productId);
      if (error) throw error;
      if (!product) throw new Error('Product not found');
      return toUIProduct(product);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch product';
      setError(new Error(errorMessage));
      toast.error(`Error fetching product: ${errorMessage}`);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch products when store changes
  useEffect(() => {
    if (currentStore?.id) {
      fetchProducts(currentStore.id);
    }
  }, [currentStore?.id]);

  return (
    <ProductContext.Provider
      value={{
        products,
        filteredProducts,
        currentProduct,
        isLoading,
        error: error,
        searchTerm,
        filters,
        categories,
        createProduct,
        updateProduct,
        deleteProduct,
        fetchProducts,
        getProduct,
        setCurrentProduct: (product: Product | null) => setCurrentProduct(product),
        searchProducts,
        setFilters: updateFilters,
        clearFilters
      }}
    >
      {children}
    </ProductContext.Provider>
  );
};

export const useProduct = () => {
  const context = useContext(ProductContext);
  if (context === undefined) {
    throw new Error('useProduct must be used within a ProductProvider');
  }
  return context;
};
