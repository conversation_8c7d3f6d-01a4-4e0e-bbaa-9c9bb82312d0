
import React from "react";
import { Container } from "@/components/ui/container";
import { MapPin, Info, Search, Home, ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useIsMobile } from "@/hooks/use-mobile";

interface StoreHeaderProps {
  name: string;
  description: string;
  address: string;
  location: string;
  about: string;
}

const StoreHeader: React.FC<StoreHeaderProps> = ({ 
  name, 
  description, 
  address, 
  location,
  about
}) => {
  const isMobile = useIsMobile();
  
  return (
    <div className="bg-white shadow-sm">
      {/* Store Name / Logo */}
      <div className="py-3 border-b">
        <Container>
          <div className="flex justify-center">
            <h1 className="text-2xl font-bold text-center">{name}</h1>
          </div>
        </Container>
      </div>
      
      {/* Location */}
      <div className="py-2 border-b bg-gray-50">
        <Container>
          <div className="flex items-center justify-center gap-1 text-sm text-gray-600">
            <MapPin className="h-3.5 w-3.5" />
            <span>{location}, {address}</span>
          </div>
        </Container>
      </div>
      
      {/* About Us */}
      <div className="py-3 border-b">
        <Container>
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4 text-gray-500 flex-shrink-0" />
            <p className="text-sm text-gray-600 line-clamp-2">{about}</p>
          </div>
        </Container>
      </div>
      
      {/* Search */}
      <div className="py-3 border-b">
        <Container>
          <div className="relative">
            <Input 
              type="search" 
              placeholder="Search products..." 
              className="w-full pl-9 pr-4 py-2 rounded-lg"
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
        </Container>
      </div>
      
      {/* Only show navigation on mobile */}
      {isMobile && (
        <div className="py-3">
          <Container>
            <div className="flex justify-around">
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <Home className="h-5 w-5" />
                <span className="text-xs">Home</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <Search className="h-5 w-5" />
                <span className="text-xs">Search</span>
              </Button>
              <Button variant="ghost" size="sm" className="flex flex-col items-center gap-1">
                <ShoppingCart className="h-5 w-5" />
                <span className="text-xs">Cart</span>
              </Button>
            </div>
          </Container>
        </div>
      )}
    </div>
  );
};

export default StoreHeader;
