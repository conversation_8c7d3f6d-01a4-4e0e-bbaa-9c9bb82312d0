
import React from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm, FormProvider } from 'react-hook-form';
import { Form } from '@/components/ui/form';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Product } from '@/types/unified-product';
import { productSchema, ProductFormValues } from './form/productSchema';
import BasicInfoTab from './form/BasicInfoTab';
import VariantsTab from './form/VariantsTab';
import MediaTagsTab from './form/MediaTagsTab';
import ProductStatusField from './form/ProductStatusField';
import ProductFormActions from './form/ProductFormActions';

interface ProductFormProps {
  initialData?: Partial<Product>;
  onSubmit: (data: ProductFormValues) => void;
  isSubmitting: boolean;
}

const ProductForm: React.FC<ProductFormProps> = ({
  initialData,
  onSubmit,
  isSubmitting,
}) => {
  const form = useForm<ProductFormValues>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      price: initialData?.price || 0,
      currency: initialData?.currency || 'KES',
      sku: initialData?.sku || '',
      stock_quantity: initialData?.stock_quantity || 0,
      category: initialData?.category || '',
      is_active: initialData?.is_active !== undefined ? initialData.is_active : true,
      tags: initialData?.tags || [],
      images: initialData?.images || [],
      has_variants: initialData?.has_variants || false,
      variants: initialData?.variants || []
    },
    mode: 'onChange',
  });

  const hasVariants = form.watch('has_variants');
  const price = form.watch('price');
  const sku = form.watch('sku');
  const currency = form.watch('currency');

  return (
    <FormProvider {...form}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <Tabs defaultValue="basic" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="basic">Basic Info</TabsTrigger>
              <TabsTrigger value="variants">Variants</TabsTrigger>
              <TabsTrigger value="media">Media & Tags</TabsTrigger>
            </TabsList>
            
            {/* Basic Info Tab */}
            <TabsContent value="basic" className="pt-4">
              <BasicInfoTab hasVariants={hasVariants} />
            </TabsContent>

            {/* Variants Tab */}
            <TabsContent value="variants" className="pt-4">
              <VariantsTab 
                currency={currency} 
                price={price} 
                sku={sku} 
              />
            </TabsContent>
            
            {/* Media & Tags Tab */}
            <TabsContent value="media" className="pt-4">
              <MediaTagsTab />
            </TabsContent>
          </Tabs>

          {/* Product Status - Always visible */}
          <ProductStatusField />

          {/* Form Actions */}
          <ProductFormActions 
            isSubmitting={isSubmitting} 
            isEditing={!!initialData?.id} 
          />
        </form>
      </Form>
    </FormProvider>
  );
};

export default ProductForm;
