
import { supabase } from '@/integrations/supabase/client';
import { getRedirectUrl } from '@/utils/authRedirects';
import { toast } from 'sonner';

/**
 * Send a magic link to the user's email
 */
export const sendMagicLink = async (email: string): Promise<{ success: boolean; error?: any }> => {
  try {
    const redirectTo = getRedirectUrl();
    console.log("Sending magic link to:", email, "with redirect:", redirectTo);
    
    const { data, error } = await supabase.auth.signInWithOtp({
      email,
      options: {
        emailRedirectTo: redirectTo,
      },
    });
    
    if (error) {
      console.error("Magic link error:", error.message);
      return { success: false, error };
    }
    
    console.log("Magic link sent successfully:", data);
    return { success: true };
  } catch (error: any) {
    console.error('Magic link error:', error);
    return { success: false, error };
  }
};

/**
 * Verify a one-time password (OTP) sent via magic link
 */
export const verifyOtp = async (token: string): Promise<{ success: boolean; error?: any }> => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email',
    });
    
    if (error) {
      console.error("OTP verification error:", error.message);
      return { success: false, error };
    }
    
    console.log("OTP verified successfully:", data);
    return { success: true };
  } catch (error: any) {
    console.error('OTP verification error:', error);
    return { success: false, error };
  }
};
