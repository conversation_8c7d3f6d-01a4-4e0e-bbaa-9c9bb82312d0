
import React from 'react';
import { Search, User, Heart, ShoppingCart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface StoreHeaderProps {
  storeName: string;
  darkMode: boolean;
  secondaryColor: string;
  fontFamily: string;
}

const StoreHeader: React.FC<StoreHeaderProps> = ({ 
  storeName, 
  darkMode, 
  secondaryColor,
  fontFamily 
}) => {
  return (
    <header className={cn(
      "p-4 mb-6 flex justify-between items-center",
      darkMode ? "bg-gray-800" : secondaryColor
    )}>
      <div className={cn("font-bold text-xl", `font-${fontFamily}`)}>
        {storeName || 'Store Name'}
      </div>
      <div className="flex items-center gap-4">
        <Search className="h-5 w-5" />
        <User className="h-5 w-5" />
        <Heart className="h-5 w-5" />
        <ShoppingCart className="h-5 w-5" />
      </div>
    </header>
  );
};

export default StoreHeader;
