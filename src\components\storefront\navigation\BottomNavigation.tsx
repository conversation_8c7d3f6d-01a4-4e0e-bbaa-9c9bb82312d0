import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, Package, Search, Menu } from "lucide-react";
import { isStoreSubdomain } from "@/utils/authRedirects";

const BottomNavigation: React.FC = () => {
  const { pathname } = useLocation();
  const isSubdomain = isStoreSubdomain();
  const basePath = isSubdomain ? "" : "/shop";
  const hostname = window.location.hostname;
  
  // Function to check if a path is active, accounting for base path
  const isActive = (path: string) => {
    const fullPath = `${basePath}${path}`;
    return pathname === fullPath || 
           // Also check without basePath for subdomain case
           (isSubdomain && pathname === path) ||
           // Special case for root path in shop subdomain
           (path === '/' && (pathname === '/' || pathname === basePath));
  };
  
  console.log("BottomNavigation rendering", { 
    pathname, 
    isSubdomain, 
    basePath,
    hostname,
    isHomeActive: isActive('/')
  });

  const navigationItems = [
    {
      path: '/',
      icon: Home,
      label: 'Home'
    },
    {
      path: '/products',
      icon: Package,
      label: 'Products'
    },
    {
      path: '/search',
      icon: Search,
      label: 'Search'
    },
    {
      path: '/categories',
      icon: Menu,
      label: 'Categories'
    }
  ];

  return (
    <nav className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-md border-t border-border z-50 safe-bottom">
      <div className="mobile-container">
        <div className="flex justify-around items-center py-2 min-h-[60px]">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActiveItem = isActive(item.path);
            const linkPath = item.path === '/' ? (basePath || "/") : `${basePath}${item.path}`;
            
            return (
              <Link 
                key={item.path}
                to={linkPath}
                className={`
                  flex flex-col items-center justify-center 
                  touch-target touch-feedback focus-enhanced
                  transition-all duration-200 ease-in-out
                  px-2 py-1 rounded-lg
                  ${isActiveItem 
                    ? 'text-primary bg-primary/10' 
                    : 'text-muted-foreground hover:text-foreground hover:bg-muted/50'
                  }
                `}
              >
                <Icon className={`h-5 w-5 transition-transform duration-200 ${isActiveItem ? 'scale-110' : ''}`} />
                <span className="text-xs mt-1 font-medium tracking-tight">
                  {item.label}
                </span>
              </Link>
            );
          })}
        </div>
      </div>
      
      {/* Visual indicator for active tab */}
      <div className="absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-primary/0 via-primary to-primary/0" />
    </nav>
  );
};

export default BottomNavigation;
