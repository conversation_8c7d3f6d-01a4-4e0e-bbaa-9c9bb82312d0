
import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2 } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useAuth } from "@/contexts";
import { UserRole } from "@/constants/roles";

const formSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address" }),
  role: z.enum(['admin', 'store_owner', 'customer']).default('store_owner'),
});

type FormValues = z.infer<typeof formSchema>;

interface InviteUserFormProps {
  onSuccess?: () => void;
}

const InviteUserForm: React.FC<InviteUserFormProps> = ({ onSuccess }) => {
  const { inviteUser } = useAuth();
  const [formError, setFormError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [inviteSent, setInviteSent] = useState(false);
  
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      role: "store_owner",
    },
  });

  const onSubmit = async (data: FormValues) => {
    if (isSubmitting) return;
    
    setFormError(null);
    setIsSubmitting(true);
    
    let toastId: string | number | undefined;
    
    try {
      toastId = toast.loading("Sending invitation...");
      
      const role = data.role === 'store_owner' 
        ? UserRole.StoreOwner 
        : data.role === 'admin' 
          ? UserRole.Admin 
          : UserRole.Customer;
          
      const { success, error } = await inviteUser(data.email, role);
      
      if (toastId) toast.dismiss(toastId);
      
      if (!success) {
        throw error;
      }
      
      setInviteSent(true);
      toast.success("Invitation sent! The user will receive an email to complete registration.");
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error: any) {
      console.error("Invitation error:", error);
      if (toastId) toast.dismiss(toastId);
      setFormError(error.message || "Failed to send invitation");
      toast.error(error.message || "Failed to send invitation");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      {inviteSent ? (
        <Alert className="mb-6 bg-green-50">
          <AlertDescription>
            <p className="text-sm">
              Invitation sent successfully! The user will receive an email with instructions to complete registration.
            </p>
          </AlertDescription>
        </Alert>
      ) : (
        <>
          {formError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 w-full">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email</FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="<EMAIL>" 
                        type="email"
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="role"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Role</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                      disabled={isSubmitting}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select role" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="store_owner">Store Owner</SelectItem>
                        <SelectItem value="customer">Customer</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isSubmitting}
              >
                {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSubmitting ? "Sending Invitation..." : "Send Invitation"}
              </Button>
            </form>
          </Form>
        </>
      )}
    </>
  );
};

export default InviteUserForm;
