
/**
 * Creates a flattened array of values by running each element in collection
 * through iteratee and flattening the mapped results.
 *
 * @param {Array} array - The array to iterate over
 * @param {Function} iteratee - The function invoked per iteration
 * @returns {Array} Returns the new flattened array
 */
function flatMap(array, iteratee) {
  if (!array || !array.length) return [];
  
  return array.flatMap(function(item, index, arr) {
    return iteratee(item, index, arr);
  });
}

// Support both ESM and CJS
export default flatMap;
