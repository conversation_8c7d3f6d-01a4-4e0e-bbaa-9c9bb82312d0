import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
const faqs = [{
  question: "How quickly can I set up my store?",
  answer: "With M-Duka, you can set up your store in minutes. Our intuitive interface guides you through the process of creating your store, adding products, and connecting payment methods. Most users can have their store live and ready to accept orders in less than a day."
}, {
  question: "What payment methods are supported?",
  answer: "M-Duka supports a wide range of payment methods including M-Pesa, Tigo Pesa, Airtel Money, PayPal, Stripe, and Flutterwave. This allows you to accept payments from customers both locally and internationally."
}, {
  question: "Can I use my own domain name?",
  answer: "Yes! While all stores get a free M-Duka subdomain (yourstore.m-duka.app), our Premium and Business plans allow you to connect your own custom domain (yourstore.com). We also provide automatic SSL certificates to ensure your store is secure."
}, {
  question: "Is there a limit to how many products I can add?",
  answer: "Our Free plan allows up to 10 products, the Premium plan supports up to 100 products, and the Business plan offers unlimited products. You can always upgrade your plan as your store grows."
}, {
  question: "How do the AI features work?",
  answer: "M-Duka offers several AI-powered features. Our AI can generate engaging product descriptions based on basic product details, provide automated customer support through chatbots, and analyze your sales data to provide insights and recommendations for growing your business."
}, {
  question: "Can I manage my store from a mobile device?",
  answer: "Absolutely! M-Duka is built with a mobile-first approach. Our admin dashboard is fully responsive and works on all devices. You can manage products, process orders, and view analytics on the go from your smartphone or tablet."
}, {
  question: "Is there a transaction fee?",
  answer: "M-Duka charges a small transaction fee of 2% on the Free plan, 1.5% on the Premium plan, and 1% on the Business plan. These fees are in addition to any fees charged by the payment processors."
}, {
  question: "Can I upgrade or downgrade my plan later?",
  answer: "Yes, you can upgrade or downgrade your plan at any time. When upgrading, you'll immediately get access to all the features of your new plan. When downgrading, the changes will take effect at the end of your current billing cycle."
}];
const FAQ = () => {
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  return <section id="faq" ref={sectionRef} className="py-20 sm:py-24 lg:py-32 bg-accent/3">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-16">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5
        }} className="inline-flex items-center rounded-full bg-white px-3 py-1 text-sm font-medium text-mduka-700 mb-4">
            <span className="flex h-2 w-2 rounded-full bg-mduka-500 mr-1.5"></span>
            <span>Questions Answered</span>
          </motion.div>
          
          <motion.h2 initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.1
        }} className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-balance">
            Frequently Asked Questions
          </motion.h2>
          
          <motion.p initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.2
        }} className="text-muted-foreground text-lg max-w-2xl mx-auto text-pretty">
            Find answers to common questions about M-Duka and how it can help your business grow online.
          </motion.p>
        </div>
        
        <div className="max-w-3xl mx-auto">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => {
            const [faqRef, faqInView] = useInView({
              triggerOnce: true,
              threshold: 0.1
            });
            return <motion.div key={index} ref={faqRef} initial={{
              opacity: 0,
              y: 20
            }} animate={faqInView ? {
              opacity: 1,
              y: 0
            } : {
              opacity: 0,
              y: 20
            }} transition={{
              duration: 0.4,
              delay: index * 0.05
            }}>
                  <AccordionItem value={`item-${index}`} className="border-b border-border py-2">
                    <AccordionTrigger className="text-left font-medium text-base md:text-lg hover:no-underline">
                      {faq.question}
                    </AccordionTrigger>
                    <AccordionContent className="text-muted-foreground text-pretty">
                      {faq.answer}
                    </AccordionContent>
                  </AccordionItem>
                </motion.div>;
          })}
          </Accordion>
          
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.6
        }} className="text-center mt-12">
            <p className="text-muted-foreground">
              Still have questions? <a href="#" className="text-mduka-600 hover:underline">Contact our support team</a>
            </p>
          </motion.div>
        </div>
      </div>
    </section>;
};
export default FAQ;