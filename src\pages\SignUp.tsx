import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { useAuth } from '@/contexts/auth/AuthContext';
import AuthLayout from '@/components/auth/AuthLayout';
import SignupForm from '@/components/auth/signup/SignupForm';
import { SignupFormValues } from '@/components/auth/signup/signupSchema';
import GoogleSignInButton from '@/components/auth/GoogleSignInButton';
import SignupFooter from '@/components/auth/signup/SignupFooter';
import { Separator } from '@/components/ui/separator';
import { UserRole } from '@/constants/roles';

const SignUp: React.FC = () => {
  const navigate = useNavigate();
  const { signup, signInWithSocial } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isGoogleLoading, setIsGoogleLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);

  const handleSignup = async (data: SignupFormValues) => {
    setIsSubmitting(true);
    setFormError(null);
    
    try {
      const { success, error } = await signup(
        data.name,
        data.email,
        data.password,
        data.role as UserRole
      );
      
      if (error) {
        if (error.message.includes('already registered')) {
          setFormError('An account with this email already exists');
        } else {
          setFormError(error.message || 'Sign up failed');
        }
        return;
      }
      
      if (success) {
        toast.success('Account created! Please check your email to verify your account.');
        navigate('/signin');
      }
    } catch (error) {
      console.error('Sign up error:', error);
      setFormError('An unexpected error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsGoogleLoading(true);
    try {
      const { success, url, error } = await signInWithSocial('google');
      
      if (error) {
        toast.error('Google sign up failed');
        return;
      }
      
      if (success && url) {
        window.location.href = url;
      }
    } catch (error) {
      console.error('Google sign up error:', error);
      toast.error('Google sign up failed');
    } finally {
      setIsGoogleLoading(false);
    }
  };

  return (
    <AuthLayout 
      title="Create your account" 
      subtitle="Get started with your free account"
    >
      <div className="w-full max-w-md space-y-6">

        <div className="space-y-4">
          <GoogleSignInButton
            onClick={handleGoogleSignUp}
            isLoading={isGoogleLoading}
          />
          
          <div className="relative">
            <Separator />
            <div className="absolute inset-0 flex items-center justify-center">
              <span className="bg-background px-2 text-muted-foreground text-sm">
                Or continue with
              </span>
            </div>
          </div>

          <SignupForm
            onSubmit={handleSignup}
            isSubmitting={isSubmitting}
            formError={formError}
            role={UserRole.StoreOwner}
          />
        </div>

        <SignupFooter />
      </div>
    </AuthLayout>
  );
};

export default SignUp;