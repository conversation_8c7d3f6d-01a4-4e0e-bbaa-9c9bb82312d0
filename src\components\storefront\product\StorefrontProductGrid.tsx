import React from 'react';
import { Product } from '@/types/unified-product';
import { LoadingFallback } from '@/components/ui/loading-fallback';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import StorefrontProductCard from './StorefrontProductCard';

interface StorefrontProductGridProps {
  products: Product[];
  isLoading: boolean;
  onWhatsAppOrder?: (product: Product) => void;
}

const StorefrontProductGrid: React.FC<StorefrontProductGridProps> = ({ 
  products, 
  isLoading, 
  onWhatsAppOrder 
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index} className="overflow-hidden">
            <CardHeader className="p-0">
              <Skeleton className="w-full h-48" />
            </CardHeader>
            <CardContent className="p-4">
              <Skeleton className="h-6 w-3/4 mb-2" />
              <Skeleton className="h-8 w-1/2 mb-3" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-2/3 mb-4" />
              <Skeleton className="h-10 w-full mb-2" />
              <Skeleton className="h-10 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-500 text-lg">No products available at the moment.</p>
        <p className="text-gray-400 text-sm mt-2">Please check back later!</p>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.map((product) => (
        <StorefrontProductCard
          key={product.id}
          product={product}
          onWhatsAppOrder={onWhatsAppOrder}
        />
      ))}
    </div>
  );
};

export default StorefrontProductGrid; 