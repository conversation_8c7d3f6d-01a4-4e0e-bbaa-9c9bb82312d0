
import React from "react";
import { CheckCircle } from "lucide-react";

interface CheckoutStepperProps {
  currentStep: number;
  steps: string[];
}

const CheckoutStepper: React.FC<CheckoutStepperProps> = ({ currentStep, steps }) => {
  return (
    <div className="mb-8">
      <div className="flex justify-between">
        {steps.map((step, index) => {
          const isActive = currentStep === index;
          const isCompleted = currentStep > index;
          
          return (
            <div 
              key={index} 
              className="flex flex-col items-center relative flex-1"
            >
              <div 
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 mb-2 z-10 
                  ${isActive ? 'bg-green-100 border-green-500 text-green-500' : 
                    isCompleted ? 'bg-green-500 border-green-500 text-white' : 
                    'bg-white border-gray-300 text-gray-500'}`}
              >
                {isCompleted ? (
                  <CheckCircle className="h-5 w-5" />
                ) : (
                  <span>{index + 1}</span>
                )}
              </div>
              
              <span className={`text-xs font-medium ${isActive || isCompleted ? 'text-green-500' : 'text-gray-500'}`}>
                {step}
              </span>
              
              {/* Connector line */}
              {index < steps.length - 1 && (
                <div 
                  className={`absolute top-4 w-full h-0.5 left-1/2 
                    ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`}
                />
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CheckoutStepper;
