
// Enhanced debugging utility for troubleshooting rendering issues
export const debugApp = {
  log: (message: string, ...args: any[]) => {
    console.log(
      `%c 🛠️ ${message}`,
      "background: #222; color: #7fd6c2; font-size: 14px;",
      ...args
    );
  },
  
  logRenderStart: () => {
    const startTime = performance.now();
    console.log(
      "%c 🚀 App render started",
      "background: #222; color: #bada55; font-size: 16px;"
    );
    console.log("Window location:", window.location.href);
    console.log("Environment:", import.meta.env.MODE);
    console.log("User Agent:", navigator.userAgent);
    
    // Return timing function to log render completion time
    return () => {
      const endTime = performance.now();
      console.log(`Render completed in ${(endTime - startTime).toFixed(2)}ms`);
    };
  },

  logRouterState: (routerState: any) => {
    console.log(
      "%c 🧭 Router State",
      "background: #222; color: #ffcc00; font-size: 16px;",
      routerState
    );
  },

  logError: (error: Error) => {
    console.error(
      "%c 🔴 App Error",
      "background: #222; color: #ff0000; font-size: 16px;",
      error
    );
    
    // Enhanced error reporting
    if (error.stack) {
      const stackLines = error.stack.split('\n');
      console.group('Error Details:');
      console.error('Message:', error.message);
      console.error('Name:', error.name);
      console.error('Stack (summarized):', stackLines.slice(0, 3).join('\n'));
      
      // Log additional information that might help with debugging
      if (typeof window !== 'undefined') {
        console.log('Current URL:', window.location.href);
        console.log('User Agent:', navigator.userAgent);
        console.log('Screen Size:', `${window.innerWidth}x${window.innerHeight}`);
        console.log('Storage Available:', 'localStorage' in window);
      }
      
      console.groupEnd();
    }
    
    // Return error for chaining
    return error;
  },

  logElementExists: (id: string) => {
    const element = document.getElementById(id);
    console.log(
      `%c Element #${id} exists: ${!!element}`,
      "background: #222; color: #00ff00; font-size: 14px;"
    );
    if (element) {
      console.log("Element properties:", {
        size: `${element.offsetWidth}x${element.offsetHeight}`,
        visible: element.offsetParent !== null,
        children: element.children.length,
        position: {
          top: element.offsetTop,
          left: element.offsetLeft
        },
        styles: window.getComputedStyle(element),
      });
    }
    return !!element;
  },
  
  logModuleLoad: (moduleName: string) => {
    console.log(
      `%c 📦 Module loaded: ${moduleName}`,
      "background: #222; color: #66ccff; font-size: 14px;"
    );
  },
  
  // Enhanced performance measurement tools
  perf: {
    activeTimers: {} as Record<string, number>,
    
    startTimer: (label: string) => {
      const start = performance.now();
      debugApp.perf.activeTimers[label] = start;
      console.time(label);
      return () => {
        console.timeEnd(label);
        delete debugApp.perf.activeTimers[label];
        return performance.now() - start;
      };
    },
    
    measure: async <T>(label: string, fn: () => Promise<T> | T): Promise<T> => {
      const start = performance.now();
      try {
        const result = await fn();
        const duration = performance.now() - start;
        console.log(`%c ⏱️ ${label}: ${duration.toFixed(2)}ms`, 
          "background: #222; color: #ff9900; font-size: 14px;");
        return result;
      } catch (error) {
        const duration = performance.now() - start;
        console.error(`%c ❌ ${label} failed after ${duration.toFixed(2)}ms`, 
          "background: #222; color: #ff3333; font-size: 14px;", error);
        throw error;
      }
    },
    
    // Check all timers and report if any are hanging
    checkHangingTimers: () => {
      const now = performance.now();
      const hanging = Object.entries(debugApp.perf.activeTimers)
        .filter(([_, startTime]) => now - startTime > 5000)
        .map(([label, startTime]) => ({
          label,
          duration: now - startTime
        }));
        
      if (hanging.length > 0) {
        console.warn('Hanging timers detected:', hanging);
      }
      
      return hanging;
    }
  },
  
  // Memory usage tracking
  memory: {
    snapshot: () => {
      if ('performance' in window && 'memory' in performance) {
        const memoryInfo = (performance as any).memory;
        return {
          totalJSHeapSize: memoryInfo.totalJSHeapSize,
          usedJSHeapSize: memoryInfo.usedJSHeapSize,
          jsHeapSizeLimit: memoryInfo.jsHeapSizeLimit,
          timestamp: new Date().toISOString()
        };
      }
      return null;
    }
  },
  
  // Resource loading tracking
  resources: {
    getLoadTimes: () => {
      if ('performance' in window) {
        const resources = performance.getEntriesByType('resource');
        return resources.map(res => {
          // Cast to PerformanceResourceTiming which has the required properties
          const resourceTiming = res as PerformanceResourceTiming;
          return {
            name: resourceTiming.name.split('/').pop(),
            duration: resourceTiming.duration.toFixed(2),
            size: resourceTiming.encodedBodySize || 0,
            type: resourceTiming.initiatorType || 'unknown'
          };
        })
        .sort((a, b) => Number(b.duration) - Number(a.duration));
      }
      return [];
    },
    
    logSlowResources: (threshold = 1000) => {
      const resources = debugApp.resources.getLoadTimes();
      const slowResources = resources.filter(res => Number(res.duration) > threshold);
      
      if (slowResources.length > 0) {
        console.warn('Slow loading resources detected:', slowResources);
      }
      
      return slowResources;
    }
  }
};
