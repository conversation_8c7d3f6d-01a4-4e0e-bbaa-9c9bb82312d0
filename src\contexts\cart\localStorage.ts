
import { CartItem } from './types';

const CART_STORAGE_KEY = 'cart';

/**
 * Loads cart data from localStorage
 */
export const loadCartFromLocalStorage = (): CartItem[] => {
  try {
    const savedCart = localStorage.getItem(CART_STORAGE_KEY);
    if (savedCart) {
      return JSON.parse(savedCart);
    }
  } catch (error) {
    console.error('Failed to parse saved cart:', error);
  }
  return [];
};

/**
 * Saves cart data to localStorage
 */
export const saveCartToLocalStorage = (items: CartItem[]): void => {
  localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(items));
};
