
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { BarChart } from '@/components/ui/chart';
import { ShoppingBag } from 'lucide-react';

interface OrdersByStoreChartProps {
  data: { name: string; orders: number }[];
}

const OrdersByStoreChart: React.FC<OrdersByStoreChartProps> = ({ data }) => {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="text-base flex items-center">
          <ShoppingBag className="h-4 w-4 mr-2 text-amber-500" />
          Orders by Top Stores
        </CardTitle>
        <CardDescription>Number of orders per store</CardDescription>
      </CardHeader>
      <CardContent>
        <BarChart 
          data={data}
          index="name"
          categories={["orders"]}
          colors={["amber"]}
          valueFormatter={(value) => `${value} orders`}
          className="h-[300px]"
        />
      </CardContent>
    </Card>
  );
};

export default OrdersByStoreChart;
