
import React from "react";
import { formatCurrency } from "@/utils/formatters";
import { Product } from "@/types/unified-product";
import { Badge } from "@/components/ui/badge";

interface ProductCardInfoProps {
  product: Product;
}

const ProductCardInfo: React.FC<ProductCardInfoProps> = ({ product }) => {
  return (
    <div className="flex flex-col h-full space-y-2">
      <div>
        {product.category && (
          <p className="text-xs text-green-600 font-medium tracking-wide uppercase mb-1">
            {product.category}
          </p>
        )}
        <h3 className="font-medium text-gray-900 mb-1 line-clamp-2 group-hover:text-green-600 transition-colors duration-300">
          {product.name}
        </h3>
        <div className="flex items-baseline gap-2 mt-auto">
          <p className="font-semibold text-gray-900">
            {formatCurrency(product.price || 0, product.currency || 'USD')}
          </p>
          {product.tags && product.tags.includes('sale') && (
            <Badge variant="outline" className="text-red-500 border-red-200 bg-red-50 text-xs">
              Sale
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductCardInfo;
