import React from "react";
import { Container } from "@/components/ui/container";
import { Twitter, Facebook, Instagram, Linkedin, Mail } from "lucide-react";
import { Link } from "react-router-dom";

// Footer with improved logo (green bubble "M" and dark "-Duka") matching the provided screenshot
const Footer = () => {
  const currentYear = new Date().getFullYear();
  return <footer className="py-12 border-t bg-[#f6fbf7]">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-5 gap-8">
          {/* Logo and Description */}
          <div className="md:col-span-2">
            <Link to="/" className="flex items-center mb-4 group" aria-label="M-Duka Home">
              {/* Logo left: green "M" bubble, dark "–Duka" text */}
              <div className="relative mr-2">
                <div className="bg-[#13C45C] rounded-xl text-white px-3 py-2 inline-block text-xl font-bold leading-none select-none shadow" style={{
                fontFamily: "Inter, Poppins, Arial, sans-serif",
                letterSpacing: '-1px'
              }}>
                  M
                  {/* Speech bubble tail */}
                  <div className="absolute -bottom-[6px] left-1/2 transform -translate-x-1/2 w-4 h-4 bg-[#13C45C] rotate-45 rounded-sm" />
                </div>
              </div>
              <span className="text-xl font-bold ml-1" style={{
              color: "#0F1828",
              letterSpacing: "-.5px",
              fontFamily: "Inter, Poppins, Arial, sans-serif"
            }}>
                -Duka
              </span>
            </Link>
            <p className="text-gray-500 mb-4 max-w-md">Empower your business with a full-featured e-commerce platform. Sell anywhere, accept payments easily, and grow your business.</p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-[#1EAEDB]">
                <Twitter className="h-5 w-5" />
                <span className="sr-only">Twitter</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-[#1EAEDB]">
                <Facebook className="h-5 w-5" />
                <span className="sr-only">Facebook</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-[#1EAEDB]">
                <Instagram className="h-5 w-5" />
                <span className="sr-only">Instagram</span>
              </a>
              <a href="#" className="text-gray-400 hover:text-[#1EAEDB]">
                <Linkedin className="h-5 w-5" />
                <span className="sr-only">LinkedIn</span>
              </a>
            </div>
          </div>

          {/* Product Section */}
          <div>
            <h3 className="font-semibold mb-4 text-black">Product</h3>
            <ul className="space-y-2">
              <li>
                <Link to="/pricing" className="text-gray-600 hover:text-[#1EAEDB]">Pricing</Link>
              </li>
              <li>
                <a href="#features" className="text-gray-600 hover:text-[#1EAEDB]">Features</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">How It Works</a>
              </li>
            </ul>
          </div>

          {/* Resources Section */}
          <div>
            <h3 className="font-semibold mb-4 text-black">Resources</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">Blog</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">Documentation</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">Help Center</a>
              </li>
            </ul>
          </div>

          {/* Company Section */}
          <div>
            <h3 className="font-semibold mb-4 text-black">Company</h3>
            <ul className="space-y-2">
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">About Us</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">Careers</a>
              </li>
              <li>
                <a href="#" className="text-gray-600 hover:text-[#1EAEDB]">Contact</a>
              </li>
              <li>
                <Link to="/terms" className="text-gray-600 hover:text-[#1EAEDB]">Terms of Service</Link>
              </li>
              <li>
                <Link to="/privacy" className="text-gray-600 hover:text-[#1EAEDB]">Privacy Policy</Link>
              </li>
              <li>
                <Link to="/cookies" className="text-gray-600 hover:text-[#1EAEDB]">Cookie Policy</Link>
              </li>
            </ul>
          </div>
        </div>

        {/* Separator line */}
        <div className="border-t border-gray-200 mt-12 pt-6 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-gray-400">
            &copy; {currentYear} Omitech Group Co. Ltd. All rights reserved.
          </p>
          <div className="flex space-x-4 mt-4 md:mt-0">
            <a href="mailto:<EMAIL>" className="flex items-center text-sm text-gray-400 hover:text-[#1EAEDB] px-[240px]">
              <Mail className="h-4 w-4 mr-2" />
              <EMAIL>
            </a>
          </div>
        </div>
      </Container>
    </footer>;
};
export default Footer;