#!/bin/bash

# This script sets the reCAPTCHA secret in Supabase and provides instructions for Vercel setup

echo "Setting reCAPTCHA secret key in Supabase..."
supabase secrets set RECAPTCHA_SECRET_KEY=6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe

echo "Done! reCAPTCHA secrets updated."

echo ""
echo "========= VERCEL DEPLOYMENT INSTRUCTIONS ========="
echo "To deploy properly to Vercel, you need to set the following environment variables:"
echo ""
echo "For the Vercel dashboard:"
echo "1. Go to https://vercel.com/your-username/m-duka-app/settings/environment-variables"
echo "2. Add the following environment variables:"
echo "   - VITE_PUBLIC_SUPABASE_URL: https://nheycjpozywomwscplcz.supabase.co"
echo "   - VITE_PUBLIC_SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oZXljanBvenl3b213c2NwbGN6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg4MTEyOTcsImV4cCI6MjA2NDM4NzI5N30.ptp3jHqbaAXGayez_zcAE_3eWsf5CRsYJkH3OwCBy3g"
echo ""
echo "Or use our vercel-env-setup.sh script to set them via CLI."
echo "=================================================="