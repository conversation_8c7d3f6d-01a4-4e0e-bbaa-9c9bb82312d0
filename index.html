<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
    <title>M-duka | Turn Your WhatsApp Business into a Digital Store</title>
    
    <!-- Primary Meta Tags -->
    <meta name="title" content="M-duka | Turn Your WhatsApp Business into a Digital Store" />
    <meta name="description" content="Transform your WhatsApp Business into a powerful digital storefront. Create your online store in minutes, manage orders, and grow your business effortlessly." />
    <meta name="keywords" content="whatsapp store, digital storefront, online business, ecommerce, m-commerce, mobile commerce, whatsapp business, africa ecommerce, kenya online store" />
    <meta name="author" content="M-duka" />
    <meta name="robots" content="index, follow" />
    <meta name="language" content="English" />
    <meta name="revisit-after" content="7 days" />
    
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://m-duka.app/" />
    <meta property="og:title" content="M-duka | WhatsApp Digital Store Platform" />
    <meta property="og:description" content="Transform your WhatsApp Business into a powerful digital storefront. Start selling online in minutes." />
    <meta property="og:image" content="/og-image.png" />
    <meta property="og:site_name" content="M-duka" />
    <meta property="og:locale" content="en_US" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://m-duka.app/" />
    <meta property="twitter:title" content="M-duka | WhatsApp Digital Store Platform" />
    <meta property="twitter:description" content="Transform your WhatsApp Business into a powerful digital storefront. Start selling online in minutes." />
    <meta property="twitter:image" content="/og-image.png" />
    <meta name="twitter:creator" content="@mduka_app" />
    
    <!-- PWA Meta Tags -->
    <meta name="application-name" content="M-duka" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="M-duka" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="msapplication-TileColor" content="#4CAF50" />
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="theme-color" content="#4CAF50" />
    
    <!-- Favicon and Icons -->
    <link rel="icon" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" type="image/png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="mask-icon" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" color="#4CAF50" />
    
    <!-- iOS Splash Screens -->
    <link rel="apple-touch-startup-image" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" media="(device-width: 320px) and (device-height: 568px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" media="(device-width: 375px) and (device-height: 667px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    <link rel="apple-touch-startup-image" href="/lovable-uploads/f6aefdd9-fbc4-43da-83ba-fc1cf8c9402c.png" media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2) and (orientation: portrait)" />
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@500;600;700&display=swap" rel="stylesheet" />
    <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
    
    <!-- Canonical URL -->
    <link rel="canonical" href="https://m-duka.app/" />
    
    <!-- Mobile and PWA specific -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="msapplication-config" content="/browserconfig.xml" />
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-touch-fullscreen" content="yes" />
    
    <!-- Safe area insets for modern devices with notches -->
    <style>
      :root {
        --safe-area-inset-top: env(safe-area-inset-top);
        --safe-area-inset-bottom: env(safe-area-inset-bottom);
        --safe-area-inset-left: env(safe-area-inset-left);
        --safe-area-inset-right: env(safe-area-inset-right);
      }
      
      body {
        padding-top: var(--safe-area-inset-top);
        padding-bottom: var(--safe-area-inset-bottom);
        padding-left: var(--safe-area-inset-left);
        padding-right: var(--safe-area-inset-right);
      }
    </style>
  </head>

  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
