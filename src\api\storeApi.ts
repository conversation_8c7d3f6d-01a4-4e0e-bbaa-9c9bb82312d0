
// Mock storeApi implementation
export const storeApi = {
  getStoresByOwner: async (userId: string) => {
    return { data: [], error: null };
  },
  getById: async (storeId: string) => {
    return { data: null, error: null };
  },
  getByUrl: async (url: string) => {
    return { data: null, error: null };
  },
  create: async (storeData: any) => {
    return { data: null, error: null };
  },
  update: async (storeId: string, storeData: any) => {
    return { data: null, error: null };
  },
  delete: async (storeId: string) => {
    return { data: null, error: null };
  }
};
