
import React from 'react';
import { useUIStore } from '@/hooks/useUIStore';
import LightweightProductItem from './LightweightProductItem';
import { Button } from '@/components/ui/button';
import { MessageSquare } from 'lucide-react';
import { Product } from '@/types/unified-product';
import { LightweightProductGridProps } from './LightweightProductGridProps';

const LightweightProductGrid: React.FC<LightweightProductGridProps> = ({
  products,
  title = "Our Products",
  showCategory = true,
  limit = 6,
  isLoading = false
}) => {
  const { currentStore } = useUIStore();
  
  // Filter products and limit the number displayed
  const filteredProducts = products.slice(0, limit);
  
  // Group products by category if requested
  const groupedByCategory = React.useMemo(() => {
    if (!showCategory) return null;
    
    const grouped: Record<string, Product[]> = {};
    
    filteredProducts.forEach(product => {
      const category = product.category || 'Uncategorized';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(product);
    });
    
    return grouped;
  }, [filteredProducts, showCategory]);
  
  // Get WhatsApp contact info from the store
  const hasWhatsApp = currentStore?.whatsappSettings?.businessNumber || 
                      currentStore?.whatsapp_settings?.businessNumber;
  
  // WhatsApp URL builder
  const getWhatsAppUrl = () => {
    const businessNumber = currentStore?.whatsappSettings?.businessNumber || 
                         currentStore?.whatsapp_settings?.businessNumber || '';
    const formattedNumber = businessNumber.startsWith('+') 
      ? businessNumber.substring(1) 
      : businessNumber;
    return `https://wa.me/${formattedNumber}`;
  };
  
  // Show loading state if required
  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">{title}</h2>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {[...Array(limit)].map((_, i) => (
            <div key={i} className="border rounded-md overflow-hidden h-64 animate-pulse bg-gray-100"></div>
          ))}
        </div>
      </div>
    );
  }
  
  // Render single category view (no grouping)
  if (!showCategory || !groupedByCategory) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold">{title}</h2>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          {filteredProducts.map(product => (
            <LightweightProductItem key={product.id} product={product} />
          ))}
        </div>
        
        {hasWhatsApp && (
          <div className="flex justify-center mt-8">
            <a 
              href={getWhatsAppUrl()} 
              target="_blank" 
              rel="noreferrer"
              className="inline-flex"
            >
              <Button className="rounded-full px-6 bg-green-600 hover:bg-green-700 gap-2">
                <MessageSquare className="h-5 w-5" />
                Chat with us on WhatsApp
              </Button>
            </a>
          </div>
        )}
      </div>
    );
  }
  
  // Render products grouped by category
  return (
    <div className="space-y-10">
      {Object.entries(groupedByCategory).map(([category, categoryProducts]) => (
        <div key={category} className="space-y-4">
          <h2 className="text-2xl font-semibold">{category}</h2>
          
          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
            {categoryProducts.map(product => (
              <LightweightProductItem key={product.id} product={product} />
            ))}
          </div>
        </div>
      ))}
      
      {hasWhatsApp && (
        <div className="flex justify-center mt-8">
          <a 
            href={getWhatsAppUrl()} 
            target="_blank" 
            rel="noreferrer"
            className="inline-flex"
          >
            <Button className="rounded-full px-6 bg-green-600 hover:bg-green-700 gap-2">
              <MessageSquare className="h-5 w-5" />
              Chat with us on WhatsApp
            </Button>
          </a>
        </div>
      )}
    </div>
  );
};

export default LightweightProductGrid;
