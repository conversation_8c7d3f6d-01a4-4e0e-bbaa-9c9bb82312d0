
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.7.1";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }
  
  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );
    
    const { url } = await req.json();
    
    if (!url) {
      return new Response(
        JSON.stringify({ error: 'URL parameter is required' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 400 }
      );
    }
    
    // Check if URL is valid format using our utility function
    const sanitizedUrl = sanitizeUrl(url);
    
    if (sanitizedUrl !== url) {
      return new Response(
        JSON.stringify({ 
          available: false, 
          sanitized: sanitizedUrl,
          error: 'URL contains invalid characters'
        }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    // Call the database function to check URL availability
    const { data, error } = await supabaseClient.rpc(
      'check_store_url_availability',
      { url_to_check: sanitizedUrl }
    );
    
    if (error) {
      console.error('Error checking URL availability:', error);
      return new Response(
        JSON.stringify({ error: 'Failed to check URL availability' }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
      );
    }
    
    // If URL is not available, suggest an alternative
    if (!data) {
      const { data: suggestedUrl, error: suggestError } = await supabaseClient.rpc(
        'generate_unique_store_url',
        { base_url: sanitizedUrl }
      );
      
      if (suggestError) {
        console.error('Error generating suggested URL:', suggestError);
        return new Response(
          JSON.stringify({ available: false, error: 'Failed to generate a suggestion' }),
          { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
        );
      }
      
      return new Response(
        JSON.stringify({ available: false, suggestion: suggestedUrl }),
        { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }
    
    return new Response(
      JSON.stringify({ available: true }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    );
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return new Response(
      JSON.stringify({ error: 'An unexpected error occurred' }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' }, status: 500 }
    );
  }
});

// Utility function to sanitize URLs (same logic as our frontend helper)
function sanitizeUrl(url: string): string {
  return url
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/[^a-z0-9-]/g, '') // Remove special characters except hyphens
    .replace(/-+/g, '-'); // Replace multiple hyphens with single hyphen
}
