
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ShieldCheck } from "lucide-react";

interface AdminLoginButtonProps {
  onClick: () => void;
  isLoading: boolean;
}

const AdminLoginButton: React.FC<AdminLoginButtonProps> = ({ 
  onClick, 
  isLoading 
}) => {
  return (
    <Button 
      variant="outline" 
      className="w-full flex items-center justify-center gap-2 border-amber-500 text-amber-700 hover:bg-amber-50"
      onClick={onClick}
      disabled={isLoading}
    >
      <ShieldCheck className="h-4 w-4" />
      {isLoading ? "Logging in..." : "Sign In as Administrator"}
    </Button>
  );
};

export default AdminLoginButton;
