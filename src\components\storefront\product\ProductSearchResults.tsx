
import React from 'react';
import { Search } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface ProductSearchResultsProps {
  searchQuery: string;
  clearFilters: () => void;
}

const ProductSearchResults: React.FC<ProductSearchResultsProps> = ({ 
  searchQuery, 
  clearFilters 
}) => {
  return (
    <div className="text-center py-16">
      <div className="mb-4">
        <Search className="w-12 h-12 mx-auto text-gray-300" />
      </div>
      <h3 className="text-xl font-medium mb-2">No products found</h3>
      <p className="text-muted-foreground mb-6">
        {searchQuery 
          ? `No products found matching "${searchQuery}"`
          : "No products matching your filters"
        }
      </p>
      <Button onClick={clearFilters}>
        Clear Filters
      </Button>
    </div>
  );
};

export default ProductSearchResults;
