
/**
 * Creates an array of elements, sorted in ascending order by the results of
 * running each element in a collection through iteratee.
 *
 * @param {Array} array - The array to sort
 * @param {Function|string} iteratee - The iteratee to sort by
 * @returns {Array} Returns the new sorted array
 */
function sortBy(array, iteratee) {
  if (!array) return [];
  
  const mapped = array.map((item, index) => {
    let value = typeof iteratee === 'function' ? iteratee(item) : item[iteratee];
    return { value, index, item };
  });
  
  mapped.sort((a, b) => {
    if (a.value < b.value) return -1;
    if (a.value > b.value) return 1;
    return 0;
  });
  
  return mapped.map(mapped => mapped.item);
}

// Support both ESM and CJS
export default sortBy;
