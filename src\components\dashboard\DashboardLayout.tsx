import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth, useStore } from '@/contexts';
import DashboardHeader from './DashboardHeader';
import DashboardSidebar from './DashboardSidebar';
import { Button } from '@/components/ui/button';
import { Menu, Loader2 } from 'lucide-react'; 
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { CustomerProvider } from '@/contexts/customer/CustomerContext';

let hasShownInitialDashboardLoading = false;

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const { user, isLoading: authLoading } = useAuth();
  const { currentStore, stores, isLoading: storeLoading } = useStore();
  const navigate = useNavigate();

  const [showInitialLoading, setShowInitialLoading] = React.useState(!hasShownInitialDashboardLoading);

  React.useEffect(() => {
    if (!authLoading && !storeLoading && showInitialLoading) {
      hasShownInitialDashboardLoading = true;
      setShowInitialLoading(false);
    }
  }, [authLoading, storeLoading]);

  React.useEffect(() => {
    console.log("DashboardLayout state:", {
      user,
      authLoading,
      storesCount: stores?.length || 0,
      currentStore: currentStore?.id || null,
      storeLoading,
    });

    if (!authLoading && !user) {
      navigate('/signin', { replace: true });
      return;
    }

    if (
      !storeLoading &&
      !authLoading &&
      user &&
      (
        (!currentStore && stores && stores.length === 0) ||
        (currentStore && (!/^[0-9a-fA-F-]{36}$/.test(currentStore.id)))
      )
    ) {
      navigate('/create-store', { replace: true });
      return;
    }
  }, [user, authLoading, stores, currentStore, storeLoading, navigate]);

  const hasNoRealStore = (!currentStore && stores && stores.length === 0)
    || (currentStore && (!/^[0-9a-fA-F-]{36}$/.test(currentStore.id)));

  if (showInitialLoading) {
    return (
      <div className="mobile-full-height flex items-center justify-center bg-background">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <span className="text-responsive-base font-medium">Loading...</span>
        </div>
      </div>
    );
  }

  if (hasNoRealStore) {
    return (
      <div className="mobile-full-height flex items-center justify-center bg-background px-4">
        <div className="text-center mobile-card max-w-md w-full">
          <h2 className="text-responsive-xl font-bold mb-4">No Store Found</h2>
          <p className="text-muted-foreground mb-6 text-responsive-sm">
            Welcome! To use m-duka, you need to create your first store.
            <br />
            Real store data will unlock the full dashboard experience.
          </p>
          <Button 
            onClick={() => navigate('/create-store')}
            className="touch-target w-full sm:w-auto"
          >
            Create Store
          </Button>
          <p className="text-xs text-muted-foreground mt-3">
            Mock/demo stores are not available in production. Please create a real store to continue.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="mobile-full-height bg-background">
      <div className="flex flex-col h-full">
        <DashboardHeader />
        <div className="flex flex-1 overflow-hidden">
          {/* Desktop sidebar */}
          <div className="desktop-only">
            <DashboardSidebar />
          </div>
          
          {/* Mobile sidebar sheet */}
          <CustomerProvider>
            <Sheet>
              <SheetTrigger asChild>
                <Button 
                  variant="outline" 
                  size="icon" 
                  className="mobile-only fixed top-4 left-4 z-40 touch-target bg-background/95 backdrop-blur-md border border-border"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="p-0 w-80">
                <DashboardSidebar />
              </SheetContent>
            </Sheet>
          </CustomerProvider>
          
          <main className="flex-1 overflow-auto mobile-container py-4 md:p-6">
            <div className="mobile-gap">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
