
import React from "react";
import { Link } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth/AuthContext";

interface MobileNavProps {
  isOpen: boolean;
  isMobile: boolean;
}

const MobileNav: React.FC<MobileNavProps> = ({ isOpen, isMobile }) => {
  const { isAuthenticated, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  return (
    <AnimatePresence>
      {isOpen && isMobile && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-x-0 top-16 bg-background border-b p-4 md:hidden z-50"
        >
          <nav className="flex flex-col gap-4">
            <Link
              to="/#features"
              className="py-2 text-foreground hover:text-primary transition-colors"
            >
              Features
            </Link>
            <Link
              to="/pricing"
              className="py-2 text-foreground hover:text-primary transition-colors"
            >
              Pricing
            </Link>
            <Link
              to="/#testimonials"
              className="py-2 text-foreground hover:text-primary transition-colors"
            >
              Testimonials
            </Link>
            <Link
              to="/#faq"
              className="py-2 text-foreground hover:text-primary transition-colors"
            >
              FAQ
            </Link>
            
            {/* Mobile Auth Buttons */}
            <div className="flex flex-col gap-2 pt-4 border-t">
              {isAuthenticated ? (
                <>
                  <Button asChild>
                    <Link to="/dashboard">Dashboard</Link>
                  </Button>
                  <Button onClick={handleLogout} variant="outline">
                    Logout
                  </Button>
                </>
              ) : (
                <>
                  <Button variant="outline" asChild className="w-full">
                    <Link to="/signin">Sign In</Link>
                  </Button>
                  <Button asChild className="w-full">
                    <Link to="/signup">Sign Up</Link>
                  </Button>
                </>
              )}
            </div>
          </nav>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default MobileNav;
