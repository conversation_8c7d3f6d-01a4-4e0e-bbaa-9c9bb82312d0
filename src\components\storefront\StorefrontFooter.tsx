import React from "react";
import { Container } from "@/components/ui/container";
import { <PERSON> } from "react-router-dom";

const StorefrontFooter: React.FC = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-50 py-12 border-t">
      <Container>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="font-semibold text-lg mb-4">M-Duka Shop</h3>
            <p className="text-muted-foreground text-sm">
              Your one-stop shop for quality products at affordable prices.
            </p>
            <div className="mt-4">
              <a href="mailto:<EMAIL>" className="text-sm text-muted-foreground hover:text-primary">
                <EMAIL>
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Shop</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/shop/new-arrivals" className="hover:text-primary">New Arrivals</Link></li>
              <li><Link to="/shop/categories" className="hover:text-primary">Categories</Link></li>
              <li><Link to="/shop/sale" className="hover:text-primary">Sale</Link></li>
              <li><Link to="/shop/bestsellers" className="hover:text-primary">Best Sellers</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Account</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/shop/account" className="hover:text-primary">My Account</Link></li>
              <li><Link to="/shop/account/orders" className="hover:text-primary">Order History</Link></li>
              <li><Link to="/shop/account/wishlist" className="hover:text-primary">Wishlist</Link></li>
              <li><Link to="/shop/account/addresses" className="hover:text-primary">Addresses</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-semibold mb-4">Customer Service</h3>
            <ul className="space-y-2 text-sm">
              <li><Link to="/shop/contact" className="hover:text-primary">Contact Us</Link></li>
              <li><Link to="/shop/shipping" className="hover:text-primary">Shipping Policy</Link></li>
              <li><Link to="/shop/returns" className="hover:text-primary">Returns Policy</Link></li>
              <li><Link to="/shop/faq" className="hover:text-primary">FAQ</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="mt-12 pt-8 border-t">
          <div className="text-center text-sm text-muted-foreground">
            <p>&copy; {currentYear} Omitech Group Co. Ltd. All rights reserved.</p>
            <div className="mt-4 flex justify-center space-x-6">
              <Link to="/terms" className="hover:text-primary">Terms of Service</Link>
              <Link to="/privacy" className="hover:text-primary">Privacy Policy</Link>
              <Link to="/cookies" className="hover:text-primary">Cookie Policy</Link>
            </div>
          </div>
        </div>
      </Container>
    </footer>
  );
};

export default StorefrontFooter;
