
import { useLocation } from 'react-router-dom';

const useNavigationActive = () => {
  const location = useLocation();
  
  const isActive = (path: string, end = false) => {
    if (end) {
      return location.pathname === path;
    }
    
    // For settings pages
    if (path === "/settings" && location.pathname.startsWith("/settings") && 
        !location.pathname.includes("/delivery") &&
        !location.pathname.includes("/whatsapp") &&
        !location.pathname.includes("/workflow")) {
      return true;
    }
    
    // For specific settings sections
    if (path === "/settings/delivery" && location.pathname.includes("/delivery")) {
      return true;
    }
    
    if (path === "/settings/whatsapp" && location.pathname.includes("/whatsapp")) {
      return true;
    }
    
    if (path === "/settings/workflow" && location.pathname.includes("/workflow")) {
      return true;
    }
    
    // For products section
    if (path === "/products" && location.pathname.startsWith("/products")) {
      return true;
    }
    
    // For marketing section
    if (path === "/marketing" && location.pathname.startsWith("/marketing")) {
      return true;
    }
    
    // For reports section
    if (path === "/reports" && location.pathname.startsWith("/reports")) {
      return true;
    }
    
    // For other paths
    if (path === "/payments" && location.pathname === "/payments") {
      return true;
    }
    
    if (path === "/shop" && location.pathname.startsWith("/shop")) {
      return true;
    }
    
    return location.pathname.startsWith(path);
  };

  return { isActive };
};

export default useNavigationActive;
