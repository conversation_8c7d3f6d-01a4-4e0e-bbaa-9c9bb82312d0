
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface WorkflowHeaderProps {
  title?: string;
}

const WorkflowHeader: React.FC<WorkflowHeaderProps> = ({ title = 'Workflow Automation' }) => {
  const navigate = useNavigate();
  
  const handleBackToSettings = () => {
    navigate('/settings/general');
  };
  
  const handleBackToDashboard = () => {
    navigate('/dashboard');
  };
  
  return (
    <div className="border-b p-4">
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-2" 
            onClick={handleBackToSettings}
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Settings
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="gap-2" 
            onClick={handleBackToDashboard}
          >
            <ArrowLeft className="h-4 w-4" />
            Return to Dashboard
          </Button>
        </div>
        <h1 className="text-xl font-semibold">{title}</h1>
        <div className="w-28" />
      </div>
    </div>
  );
};

export default WorkflowHeader;
