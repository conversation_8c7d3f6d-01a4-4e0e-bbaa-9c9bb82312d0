import { v4 as uuidv4 } from 'uuid';
import { Order, OrderStatus, OrderItem } from '@/types/order';

// Possible order statuses for mock orders
const MOCK_ORDER_STATUSES: OrderStatus[] = [
  'pending', 'processing', 'paid', 'shipped',
  'delivered', 'cancelled', 'refunded', 'payment_failed'
];

// Supported payment methods
const MOCK_PAYMENT_METHODS = ['credit-card', 'paypal', 'mpesa'];

// Generate a random number within range
const getRandomInRange = (min: number, max: number): number =>
  Math.floor(Math.random() * (max - min + 1)) + min;

// Generate a random past date within N days
const getRandomPastDate = (days: number): Date => {
  const date = new Date();
  date.setDate(date.getDate() - getRandomInRange(0, days));
  return date;
};

// Generate mock order items
const generateMockOrderItems = (count: number = 3): OrderItem[] =>
  Array.from({ length: count }).map((_, index) => {
    const price = getRandomInRange(10, 100);
    const quantity = getRandomInRange(1, 3);
    return {
      product_id: uuidv4(),
      name: `Mock Product ${index + 1}`,
      price,
      quantity,
    };
  });

// Generate a single mock order
export const generateMockOrder = (storeId: string, userId: string = 'mock-user'): Order => {
  const items = generateMockOrderItems();
  const totalAmount = items.reduce((sum, item) => sum + item.price * item.quantity, 0);
  const date = new Date();
  const paymentMethod = MOCK_PAYMENT_METHODS[getRandomInRange(0, MOCK_PAYMENT_METHODS.length - 1)];
  const status: OrderStatus = 'pending';

  return {
    id: `mock-${uuidv4()}`,
    store_id: storeId,
    user_id: userId,
    status,
    total_amount: totalAmount,
    currency: 'USD',
    payment_method: paymentMethod,
    items,
    created_at: date,
    updated_at: date,
  };
};

// Generate multiple mock orders
export const generateMockOrders = (storeId: string, count: number = 10): Order[] => {
  return Array.from({ length: count }).map((_, index) => {
    const order = generateMockOrder(storeId, `mock-user-${index}`);
    const randomStatus = MOCK_ORDER_STATUSES[getRandomInRange(0, MOCK_ORDER_STATUSES.length - 1)];
    const pastDate = getRandomPastDate(30);

    return {
      ...order,
      status: randomStatus,
      created_at: pastDate,
      updated_at: pastDate,
    };
  }).sort((a, b) => b.created_at.getTime() - a.created_at.getTime());
};

// Retrieve mock orders from localStorage or generate new ones
export const getMockOrders = (storeId: string): Order[] => {
  try {
    const saved = localStorage.getItem(`mockOrders-${storeId}`);
    if (saved) {
      return JSON.parse(saved).map((order: any) => ({
        ...order,
        created_at: new Date(order.created_at),
        updated_at: new Date(order.updated_at),
      }));
    }
  } catch (error) {
    console.error('Failed to load mock orders:', error);
  }

  const newOrders = generateMockOrders(storeId);
  saveMockOrders(storeId, newOrders);
  return newOrders;
};

// Save mock orders to localStorage
export const saveMockOrders = (storeId: string, orders: Order[]): void => {
  try {
    localStorage.setItem(`mockOrders-${storeId}`, JSON.stringify(orders));
  } catch (error) {
    console.error('Failed to save mock orders:', error);
  }
};
