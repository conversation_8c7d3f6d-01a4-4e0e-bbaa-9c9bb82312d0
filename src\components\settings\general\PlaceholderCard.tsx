
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';

interface PlaceholderCardProps {
  title: string;
  description: string;
}

const PlaceholderCard = ({ title, description }: PlaceholderCardProps) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">{description} will be available here.</p>
      </CardContent>
    </Card>
  );
};

export default PlaceholderCard;
