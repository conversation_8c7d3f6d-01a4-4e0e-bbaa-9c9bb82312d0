
import React, { useState, useEffect } from 'react';
import { useParams, useSearchParams, useNavigate } from 'react-router-dom';
import { Container } from '@/components/ui/container';
import { useProduct } from '@/contexts';
import LightweightProductGrid from '@/components/storefront/product/LightweightProductGrid';
import ProductSearchForm from '@/components/storefront/product/search/ProductSearchForm';
import ProductFilters from '@/components/storefront/product/filters/ProductFilters';
import ActiveFilters from '@/components/storefront/product/filters/ActiveFilters';

const LightweightProductListing: React.FC = () => {
  const { products, isLoading } = useProduct();
  const { category } = useParams<{ category: string }>();
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(category || null);
  const [minPrice, setMinPrice] = useState<string>('');
  const [maxPrice, setMaxPrice] = useState<string>('');
  
  useEffect(() => {
    setSelectedCategory(category || null);
  }, [category]);
  
  const allCategories = [...new Set(products.map(p => p.category).filter(Boolean))];
  
  const filteredProducts = products.filter(product => {
    if (searchQuery && !product.name.toLowerCase().includes(searchQuery.toLowerCase())) {
      return false;
    }
    
    if (selectedCategory && product.category !== selectedCategory) {
      return false;
    }
    
    if (minPrice && product.price < parseFloat(minPrice)) {
      return false;
    }
    
    if (maxPrice && product.price > parseFloat(maxPrice)) {
      return false;
    }
    
    return true;
  });
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    setSearchParams(params);
  };
  
  const handleCategoryClick = (cat: string | null) => {
    if (cat === null) {
      navigate('/shop');
    } else {
      navigate(`/shop/category/${cat}`);
    }
  };
  
  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory(null);
    setMinPrice('');
    setMaxPrice('');
    navigate('/shop');
  };
  
  const pageTitle = selectedCategory 
    ? selectedCategory 
    : searchQuery 
      ? `Search: "${searchQuery}"` 
      : 'All Products';
  
  return (
    <Container className="py-8">
      <div className="mb-6">
        <ProductSearchForm 
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onSubmit={handleSearch}
          onFilterToggle={() => setShowFilters(!showFilters)}
        />
        
        {showFilters && (
          <ProductFilters 
            selectedCategory={selectedCategory}
            minPrice={minPrice}
            maxPrice={maxPrice}
            onCategoryClick={handleCategoryClick}
            onMinPriceChange={setMinPrice}
            onMaxPriceChange={setMaxPrice}
            onClearFilters={clearFilters}
            onApplyPriceFilter={() => {
              setSearchQuery(searchQuery);
              setSearchParams(searchParams);
            }}
            allCategories={allCategories}
          />
        )}
        
        <ActiveFilters 
          selectedCategory={selectedCategory}
          searchQuery={searchQuery}
          minPrice={minPrice}
          maxPrice={maxPrice}
          onClearCategory={() => handleCategoryClick(null)}
          onClearSearch={() => {
            setSearchQuery('');
            setSearchParams(new URLSearchParams());
          }}
          onClearMinPrice={() => setMinPrice('')}
          onClearMaxPrice={() => setMaxPrice('')}
        />
      </div>
      
      <LightweightProductGrid 
        products={filteredProducts} 
        isLoading={isLoading}
        title={pageTitle}
        showCategory={true}
      />
    </Container>
  );
};

export default LightweightProductListing;
