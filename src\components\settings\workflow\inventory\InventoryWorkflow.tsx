
import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Package, Zap, Bell, AlertCircle, ExternalLink, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import ComingSoonPlaceholder from '../ComingSoonPlaceholder';

const InventoryWorkflow = () => {
  return (
    <div className="space-y-6">
      {/* Status Card */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
            <div>
              <p className="font-medium text-blue-800">Inventory Alerts by Pubbly Connect</p>
              <p className="text-sm text-blue-700">
                Get real-time notifications when inventory levels change or reach thresholds.
              </p>
              <div className="mt-2 flex gap-2">
                <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                  Coming Q4 2023
                </Badge>
                <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-200">
                  <Bell className="mr-1 h-3 w-3" />
                  Early Access Available
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Coming Soon Content */}
      <ComingSoonPlaceholder 
        title="Inventory Alerts"
        description="Set up automated notifications for stock levels, product movements, and inventory changes"
        icon={Package}
        expectedDate="Q4 2023"
        actionLabel="Join Early Access Waitlist"
        onAction={() => {
          console.log("User requested early access to inventory alerts");
          // In a real app, this would trigger a notification or add them to a waitlist
        }}
      />

      {/* Preview of what's coming */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">What's Coming in Inventory Alerts</CardTitle>
          <CardDescription>
            Preview the inventory automation features that will be available soon
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="border rounded-md p-4 bg-white">
              <div className="flex items-start gap-2">
                <AlertCircle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <p className="font-medium">Low Stock Alerts</p>
                  <p className="text-sm text-muted-foreground">
                    Get notified when inventory falls below your defined thresholds
                  </p>
                </div>
              </div>
            </div>
            
            <div className="border rounded-md p-4 bg-white">
              <div className="flex items-start gap-2">
                <Bell className="h-5 w-5 text-green-500 mt-0.5" />
                <div>
                  <p className="font-medium">Restock Notifications</p>
                  <p className="text-sm text-muted-foreground">
                    Automatic alerts when new inventory is received
                  </p>
                </div>
              </div>
            </div>
            
            <div className="border rounded-md p-4 bg-white">
              <div className="flex items-start gap-2">
                <Package className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="font-medium">Inventory Reports</p>
                  <p className="text-sm text-muted-foreground">
                    Schedule and automate inventory status reports
                  </p>
                </div>
              </div>
            </div>
            
            <div className="border rounded-md p-4 bg-white">
              <div className="flex items-start gap-2">
                <Zap className="h-5 w-5 text-purple-500 mt-0.5" />
                <div>
                  <p className="font-medium">Multi-channel Alerts</p>
                  <p className="text-sm text-muted-foreground">
                    Send notifications via WhatsApp, email, or SMS
                  </p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center mt-4">
            <Button variant="outline" className="gap-2">
              Learn More About Pubbly Connect 
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default InventoryWorkflow;
