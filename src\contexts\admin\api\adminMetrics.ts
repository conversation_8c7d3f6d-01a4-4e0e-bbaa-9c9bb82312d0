
import { supabase } from '@/integrations/supabase/client';

/**
 * Fetches platform metrics for the admin dashboard
 */
export const fetchAdminMetrics = async () => {
  try {
    // Fetch total users count
    const { count: totalUsers, error: usersError } = await supabase
      .from('profiles')
      .select('*', { count: 'exact', head: true });

    if (usersError) throw usersError;

    // Fetch total stores count
    const { count: totalStores, error: storesError } = await supabase
      .from('stores')
      .select('*', { count: 'exact', head: true });

    if (storesError) throw storesError;

    // Fetch total orders count
    const { count: totalOrders, error: ordersError } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true });

    if (ordersError) throw ordersError;

    // Calculate total revenue from orders
    const { data: orders, error: revenueError } = await supabase
      .from('orders')
      .select('total_amount');

    if (revenueError) throw revenueError;

    const totalRevenue = orders.reduce((sum, order) => sum + parseFloat(order.total_amount.toString()), 0);

    return {
      totalUsers: totalUsers || 0,
      totalStores: totalStores || 0,
      totalOrders: totalOrders || 0,
      totalRevenue: totalRevenue || 0
    };
  } catch (error) {
    console.error('Error fetching admin metrics:', error);
    // Return default values in case of error
    return {
      totalUsers: 0,
      totalStores: 0, 
      totalOrders: 0,
      totalRevenue: 0
    };
  }
};

/**
 * Fetches revenue data by month for charts
 */
export const fetchRevenueData = async () => {
  try {
    // Default to current year
    const currentYear = new Date().getFullYear();
    
    const { data, error } = await supabase
      .from('orders')
      .select('created_at, total_amount')
      .gte('created_at', `${currentYear}-01-01`)
      .lte('created_at', `${currentYear}-12-31`);

    if (error) throw error;

    // Set up months array - using fewer months for lightweight version
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    
    // Initialize monthly revenue data with fewer months
    const monthlyRevenue = months.map(name => ({ name, total: 0 }));
    
    // Aggregate revenue by month
    data.forEach(order => {
      const date = new Date(order.created_at);
      const monthIndex = date.getMonth();
      if (monthIndex < months.length) {
        monthlyRevenue[monthIndex].total += parseFloat(order.total_amount.toString());
      }
    });
    
    return monthlyRevenue;
  } catch (error) {
    console.error('Error fetching revenue data:', error);
    // Return minimal empty data in case of error
    return [
      { name: 'Jan', total: 0 },
      { name: 'Feb', total: 0 },
      { name: 'Mar', total: 0 },
      { name: 'Apr', total: 0 },
      { name: 'May', total: 0 },
      { name: 'Jun', total: 0 },
    ];
  }
};

/**
 * Fetches user growth data by month for charts
 */
export const fetchUserGrowthData = async () => {
  try {
    // Get user registrations by month for current year
    const currentYear = new Date().getFullYear();
    
    const { data, error } = await supabase
      .from('profiles')
      .select('created_at')
      .gte('created_at', `${currentYear}-01-01`)
      .lte('created_at', `${currentYear}-12-31`);

    if (error) throw error;

    // Set up months array - using fewer months for lightweight version
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
    
    // Initialize monthly user data with fewer months
    const monthlyUsers = months.map(name => ({ name, users: 0 }));
    
    // Count users by registration month
    data.forEach(user => {
      const date = new Date(user.created_at);
      const monthIndex = date.getMonth();
      if (monthIndex < months.length) {
        monthlyUsers[monthIndex].users += 1;
      }
    });
    
    // Add cumulative users (growing over time)
    let runningTotal = 0;
    monthlyUsers.forEach((month, index) => {
      runningTotal += month.users;
      monthlyUsers[index].users = runningTotal;
    });
    
    return monthlyUsers;
  } catch (error) {
    console.error('Error fetching user growth data:', error);
    // Return minimal empty data in case of error
    return [
      { name: 'Jan', users: 0 },
      { name: 'Feb', users: 0 },
      { name: 'Mar', users: 0 },
      { name: 'Apr', users: 0 },
      { name: 'May', users: 0 },
      { name: 'Jun', users: 0 },
    ];
  }
};

/**
 * Fetches orders by store data for charts - optimized for lightweight display
 */
export const fetchOrdersByStoreData = async () => {
  try {
    // Get orders grouped by store
    const { data: orders, error: ordersError } = await supabase
      .from('orders')
      .select('store_id, total_amount');

    if (ordersError) throw ordersError;

    // Get store names
    const { data: stores, error: storesError } = await supabase
      .from('stores')
      .select('id, name');

    if (storesError) throw storesError;

    // Create a map of store IDs to names
    const storeMap: Record<string, string> = {};
    stores.forEach(store => {
      storeMap[store.id] = store.name;
    });

    // Count orders by store
    interface OrdersByStoreItem {
      orders: number;
      storeId: string;
    }
    
    const ordersByStore: Record<string, OrdersByStoreItem> = {};
    
    orders.forEach(order => {
      if (!ordersByStore[order.store_id]) {
        ordersByStore[order.store_id] = {
          orders: 0,
          storeId: order.store_id
        };
      }
      ordersByStore[order.store_id].orders += 1;
    });

    // Convert to array format needed for chart
    const result = Object.values(ordersByStore)
      .map(item => ({
        name: storeMap[item.storeId] || 'Unknown Store',
        orders: item.orders
      }))
      .sort((a, b) => b.orders - a.orders) // Sort by most orders
      .slice(0, 3); // Get only top 3 stores for lightweight display

    return result.length > 0 ? result : [{ name: 'No Stores', orders: 0 }];
  } catch (error) {
    console.error('Error fetching orders by store data:', error);
    // Return minimal empty data in case of error
    return [{ name: 'No Data', orders: 0 }];
  }
};
