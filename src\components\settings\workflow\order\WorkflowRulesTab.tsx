
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { RefreshCw, Settings, MessageCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';

interface WorkflowRule {
  id: string;
  name: string;
  trigger: string;
  actions: string[];
  status: 'active' | 'paused' | 'draft';
  lastTriggered?: string;
}

interface WorkflowRulesTabProps {
  workflowRules: WorkflowRule[];
  setWorkflowRules: React.Dispatch<React.SetStateAction<WorkflowRule[]>>;
}

const WorkflowRulesTab: React.FC<WorkflowRulesTabProps> = ({ workflowRules, setWorkflowRules }) => {
  const toggleRuleStatus = (ruleId: string) => {
    setWorkflowRules(prev => 
      prev.map(rule => {
        if (rule.id === ruleId) {
          const newStatus = rule.status === 'active' ? 'paused' : 'active';
          toast.success(`Workflow rule "${rule.name}" ${newStatus === 'active' ? 'activated' : 'paused'}`);
          return {...rule, status: newStatus};
        }
        return rule;
      })
    );
  };
  
  const getStatusBadge = (status: WorkflowRule['status']) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Active</Badge>;
      case 'paused':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Paused</Badge>;
      case 'draft':
        return <Badge className="bg-slate-100 text-slate-800 hover:bg-slate-200">Draft</Badge>;
      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Order Workflow Rules</h3>
          <p className="text-sm text-muted-foreground">Manage automated actions for order events</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Sync Rules
          </Button>
          <Button>
            Create Workflow
          </Button>
        </div>
      </div>
      
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {workflowRules.map(rule => (
              <div key={rule.id} className="border rounded-md p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium">{rule.name}</h4>
                      {getStatusBadge(rule.status)}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      Trigger: <span className="font-medium">{rule.trigger.replace('_', ' ')}</span>
                    </p>
                    <div className="flex flex-wrap gap-1 mt-2">
                      {rule.actions.map((action, index) => (
                        <Badge key={index} variant="outline">
                          {action === 'send_whatsapp_message' && <MessageCircle className="h-3 w-3 mr-1" />}
                          {action === 'send_email' && <MessageCircle className="h-3 w-3 mr-1" />}
                          {action === 'update_customer_record' && <RefreshCw className="h-3 w-3 mr-1" />}
                          {action.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                    {rule.lastTriggered && (
                      <p className="text-xs text-muted-foreground mt-2">
                        Last triggered: {rule.lastTriggered}
                      </p>
                    )}
                  </div>
                  <div className="flex gap-2">
                    <Switch 
                      checked={rule.status === 'active'} 
                      onCheckedChange={() => toggleRuleStatus(rule.id)}
                    />
                    <Button variant="ghost" size="sm">
                      <Settings className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default WorkflowRulesTab;
