import React, { createContext, useContext, useState, useEffect } from 'react';
import { Tables } from '@/integrations/supabase/types';
type DatabaseStore = Tables<'stores'>;
import { Store } from '@/types/store';
import { StoreContextType, StoreProviderProps } from './types';
import { storeApi } from '@/api/storeApi';
import { ensureUIStore, uiToDatabaseStoreUpdate } from '@/utils/typeConverters';
import { useMockStores } from './hooks/useMockStores';
import { useAuth } from '@/contexts/auth/AuthContext';

// Create context with default undefined value
const StoreContext = createContext<StoreContextType | undefined>(undefined);

// DEVELOPMENT ONLY: Mock store for testing
const MOCK_STORE_FOR_DEV = false;

// Store Provider component
export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  const [stores, setStores] = useState<DatabaseStore[]>([]);
  const [currentStore, setCurrentStore] = useState<DatabaseStore | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const { user, isAuthenticated } = useAuth();
  const mockStores = useMockStores();
  
  // Fetch stores when user changes
  useEffect(() => {
    // Development mode: always provide mock store
    if (MOCK_STORE_FOR_DEV) {
      const mockStore: DatabaseStore = {
        id: 'dev-store-123',
        name: 'Development Store',
        description: 'Mock store for development',
        owner_id: 'dev-user-123',
        store_url: 'dev-store',
        store_type: 'retail',
        payment_methods: ['mpesa', 'cash'],
        notifications: { email: true, sms: false },
        logo_url: null,
        theme: { primaryColor: '#FFFFFF', secondaryColor: '#000000' },
        category: 'general',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        domain: null,
        is_active: true,
        slug: null,
        whatsapp_number: null,
      };
      setStores([mockStore]);
      setCurrentStore(mockStore);
      setIsLoading(false);
      return;
    }
    
    if (!isAuthenticated) {
      setStores([]);
      setCurrentStore(null);
      setIsLoading(false);
      return;
    }
    
    const fetchStores = async () => {
      try {
        if (!user) {
          setStores([]);
          setCurrentStore(null);
          setIsLoading(false);
          return;
        }
        
        setIsLoading(true);
        // Use actual API call for real data
        const { data, error } = await storeApi.getStoresByOwner(user.id);
        
        if (error) {
          console.error('Error fetching stores:', error);
          setError(new Error('Failed to fetch stores'));
          setStores([]);
          setCurrentStore(null);
        } else {
          setStores(data || []);
          if (data && data.length > 0) {
            setCurrentStore(data[0]);
          } else {
            setCurrentStore(null);
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch stores'));
        console.error('Error fetching stores:', err);
        setStores([]);
        setCurrentStore(null);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchStores();
  }, [user, isAuthenticated]);
  
  // Create a new store
  const createStore = async (storeData: any): Promise<DatabaseStore> => {
    setLoading(true);
    
    try {
      // Prepare store data for API
      const dbStoreData = {
        name: storeData.name,
        description: storeData.description,
        owner_id: user?.id || 'mock_owner_id',
        store_url: storeData.storeUrl,
        category: storeData.category || 'general',
        store_type: storeData.storeType || 'retail',
        payment_methods: storeData.paymentMethods || [],
        notifications_email: storeData.notificationsEmail,
        theme_options: storeData.themeOptions,
        whatsapp_settings: storeData.whatsappSettings,
        notifications: storeData.notifications || { email: true, sms: false },
        logo_url: storeData.logo || null,
        theme: storeData.theme || { primaryColor: '#FFFFFF', secondaryColor: '#000000' },
      };
      
      // In production, we'd use:
      // const { data, error } = await storeApi.create(dbStoreData);
      
      const newStore = {
        id: `mock_store_${Date.now()}`,
        ...dbStoreData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        domain: null,
        is_active: true,
        slug: null,
        whatsapp_number: null,
      } as unknown as DatabaseStore;
      
      // Update state
      setStores(prev => [...prev, newStore]);
      setCurrentStore(newStore);
      
      return newStore;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to create store'));
      console.error('Error creating store:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Update a store
  const updateStore = async (storeId: string, storeData: any): Promise<DatabaseStore> => {
    setLoading(true);
    
    try {
      // Convert UI-friendly data to database format
      const dbStoreData = uiToDatabaseStoreUpdate(storeData);
      
      // In production, we'd use:
      // const { data, error } = await storeApi.update(storeId, dbStoreData);
      
      // For development, use mock data
      const updatedStore = {
        ...stores.find(s => s.id === storeId),
        ...dbStoreData,
        updated_at: new Date().toISOString()
      } as DatabaseStore;
      
      // Update state
      setStores(prev => prev.map(s => s.id === storeId ? updatedStore : s));
      
      if (currentStore?.id === storeId) {
        setCurrentStore(updatedStore);
      }
      
      return updatedStore;
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update store'));
      console.error('Error updating store:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Delete a store
  const deleteStore = async (storeId: string): Promise<void> => {
    setLoading(true);
    
    try {
      // In production, we'd use:
      // const { data, error } = await storeApi.delete(storeId);
      
      // Update state
      setStores(prev => prev.filter(s => s.id !== storeId));
      
      if (currentStore?.id === storeId) {
        const remainingStores = stores.filter(s => s.id !== storeId);
        setCurrentStore(remainingStores.length > 0 ? remainingStores[0] : null);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to delete store'));
      console.error('Error deleting store:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch a specific store
  const fetchStore = async (storeId: string): Promise<void> => {
    setLoading(true);
    
    try {
      // In production, we'd use:
      // const { data, error } = await storeApi.getById(storeId);
      
      // For development, use mock data
      const store = stores.find(s => s.id === storeId) || null;
      
      // Update state
      if (store) {
        setCurrentStore(store);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch store'));
      console.error('Error fetching store:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Fetch store by URL
  const fetchStoreByUrl = async (url: string): Promise<void> => {
    setLoading(true);
    
    try {
      // In production, we'd use:
      // const { data, error } = await storeApi.getByUrl(url);
      
      // For development, use mock data
      const store = stores.find(s => s.store_url === url) || null;
      
      // Update state
      if (store) {
        setCurrentStore(store);
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to fetch store by URL'));
      console.error('Error fetching store by URL:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };
  
  // Get the current store in UI-friendly format
  const getCurrentUIStore = (): Store | null => {
    return ensureUIStore(currentStore);
  };
  
  return (
    <StoreContext.Provider
      value={{
        currentStore,
        stores,
        isLoading,
        loading,
        error,
        createStore,
        updateStore,
        deleteStore,
        fetchStore,
        fetchStoreByUrl,
        setCurrentStore,
        getCurrentUIStore
      }}
    >
      {children}
    </StoreContext.Provider>
  );
};

// Custom hook to use the store context
export const useStore = (): StoreContextType => {
  const context = useContext(StoreContext);
  
  if (context === undefined) {
    throw new Error('useStore must be used within a StoreProvider');
  }
  
  return context;
};
