import React, { useEffect } from "react";
import MarketingBanner from "./marketing/MarketingBanner";
import StorefrontProductGrid from "@/components/storefront/product/StorefrontProductGrid";
import StorefrontHeader from "@/components/storefront/header/StorefrontHeader";
import { Container } from "@/components/ui/container";
import { useProduct } from "@/contexts";
import { LoadingFallback } from "@/components/ui/loading-fallback";
import { Product } from "@/types/unified-product";

const Shop: React.FC = () => {
  const { 
    products, 
    filteredProducts, 
    isLoading, 
    searchProducts,
    searchTerm,
    clearFilters
  } = useProduct();
  
  useEffect(() => {
    console.log("Shop component mounted");
    console.log("Products loaded:", products.length);
    console.log("Filtered products:", filteredProducts.length);
  }, [products.length, filteredProducts.length]);

  // Show only active products that are in stock for customers
  const availableProducts = filteredProducts.filter(p => p.is_active && (p.stock_quantity || 0) > 0);

  // Handle WhatsApp orders
  const handleWhatsAppOrder = (product: Product) => {
    const message = `Hi! I'm interested in "${product.name}" for ${product.price} ${product.currency}. Can you provide more details?`;
    const whatsappUrl = `https://wa.me/254700000000?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (isLoading) {
    return <LoadingFallback size="large" message="Loading products..." className="py-12" />;
  }

  return (
    <div className="min-h-screen bg-gray-50 pb-16">
      {/* Store Header with Search */}
      <StorefrontHeader onSearch={searchProducts} />
      
      {/* Banner at the top */}
      <MarketingBanner />
      
      {/* Products section */}
      <section className="py-4">
        <Container>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold">Our Products</h1>
              {searchTerm && (
                <p className="text-gray-600 mt-1">
                  {availableProducts.length > 0 
                    ? `Found ${availableProducts.length} product${availableProducts.length === 1 ? '' : 's'} for "${searchTerm}"`
                    : `No products found for "${searchTerm}"`
                  }
                </p>
              )}
            </div>
            {searchTerm && (
              <button
                onClick={clearFilters}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                Clear search
              </button>
            )}
          </div>
          
          {availableProducts.length === 0 ? (
            <div className="text-center py-12">
              {searchTerm ? (
                <div>
                  <p className="text-gray-500 text-lg mb-2">No products found for "{searchTerm}"</p>
                  <p className="text-gray-400 text-sm mb-4">Try adjusting your search terms</p>
                  <button
                    onClick={clearFilters}
                    className="text-blue-600 hover:text-blue-800 font-medium"
                  >
                    View all products
                  </button>
                </div>
              ) : (
                <p className="text-gray-500">No products available at the moment.</p>
              )}
            </div>
          ) : (
            <StorefrontProductGrid 
              products={availableProducts} 
              isLoading={isLoading} 
              onWhatsAppOrder={handleWhatsAppOrder}
            />
          )}
        </Container>
      </section>
    </div>
  );
};

export default Shop;
