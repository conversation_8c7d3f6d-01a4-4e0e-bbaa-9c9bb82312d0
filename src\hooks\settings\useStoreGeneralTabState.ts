
import { useState, useEffect } from 'react';
import { Store } from '@/types/store';

export const useStoreGeneralTabState = (currentStore: Store | null) => {
  const [storeName, setStoreName] = useState(currentStore?.name || '');
  const [orderPrefix, setOrderPrefix] = useState('');
  const [defaultTimeZone, setDefaultTimeZone] = useState('UTC+0');
  const [enableInventoryTracking, setEnableInventoryTracking] = useState(true);
  const [lowStockThreshold, setLowStockThreshold] = useState('5');
  const [enableReviews, setEnableReviews] = useState(
    currentStore?.themeOptions?.showProductRatings !== false
  );
  const [enableWishlist, setEnableWishlist] = useState(true);
  const [defaultMetaTitle, setDefaultMetaTitle] = useState('');
  const [defaultMetaDescription, setDefaultMetaDescription] = useState('');
  const [logo, setLogo] = useState<File | string | null>(currentStore?.logo || null);
  
  // Update state when currentStore changes
  useEffect(() => {
    if (currentStore) {
      setStoreName(currentStore.name || '');
      setEnableReviews(currentStore.themeOptions?.showProductRatings !== false);
      setLogo(currentStore.logo || null);
      
      // Load theme options if available
      if (currentStore.themeOptions) {
        setEnableWishlist(currentStore.themeOptions.enableWishlist !== false);
      }
    }
  }, [currentStore]);

  return {
    storeName,
    setStoreName,
    orderPrefix,
    setOrderPrefix,
    defaultTimeZone,
    setDefaultTimeZone,
    enableInventoryTracking,
    setEnableInventoryTracking,
    lowStockThreshold,
    setLowStockThreshold,
    enableReviews,
    setEnableReviews,
    enableWishlist,
    setEnableWishlist,
    defaultMetaTitle,
    setDefaultMetaTitle,
    defaultMetaDescription,
    setDefaultMetaDescription,
    logo,
    setLogo,
  };
};
