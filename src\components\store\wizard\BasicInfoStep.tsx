import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { StoreFormData } from '@/types/store';
import { generateUrlFromName, checkStoreUrlAvailability } from '@/utils/storeHelpers';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { CheckCircle, XCircle, AlertCircle, Sparkles } from 'lucide-react';
import AIChatWidget from '@/components/AIChatWidget';
import { Popover, PopoverTrigger, PopoverContent } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';

const formSchema = z.object({
  name: z.string().min(2, { message: 'Store name must be at least 2 characters.' })
    .max(50, { message: 'Store name must be at most 50 characters.' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters.' })
    .max(500, { message: 'Description must be at most 500 characters.' }),
  storeUrl: z.string().optional(),
});

interface BasicInfoStepProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({ data, updateData }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: data.name || '',
      description: data.description || '',
      storeUrl: data.storeUrl || '',
    },
  });

  // URL validation states
  const [urlCheckStatus, setUrlCheckStatus] = useState<'checking' | 'available' | 'unavailable' | 'error' | null>(null);
  const [urlSuggestion, setUrlSuggestion] = useState<string | null>(null);

  // Define the debounced values state with explicit non-optional types
  const [debouncedValues, setDebouncedValues] = React.useState({
    name: data.name || '',
    description: data.description || '',
    storeUrl: data.storeUrl || '',
  });
  
  // Track if URL has been manually edited
  const [urlManuallyEdited, setUrlManuallyEdited] = React.useState(false);

  // Handle manual form submission
  const onSubmit = (values: z.infer<typeof formSchema>) => {
    updateData(values);
  };

  // Auto-generate storeUrl when name changes
  React.useEffect(() => {
    const name = form.watch('name');
    if (name) {
      const generatedUrl = generateUrlFromName(name);
      
      // Always update URL if it hasn't been manually changed
      if (!urlManuallyEdited) {
        form.setValue('storeUrl', generatedUrl);
      }
    }
  }, [form.watch('name'), urlManuallyEdited]);

  // Check URL availability when storeUrl changes
  useEffect(() => {
    const checkUrlAvailability = async () => {
      const storeUrl = form.watch('storeUrl');
      if (!storeUrl || storeUrl.length < 2) {
        setUrlCheckStatus(null);
        setUrlSuggestion(null);
        return;
      }

      setUrlCheckStatus('checking');
      
      try {
        const result = await checkStoreUrlAvailability(storeUrl);
        
        if (result.error) {
          setUrlCheckStatus('error');
          console.error(result.error);
          return;
        }
        
        if (result.available) {
          setUrlCheckStatus('available');
          setUrlSuggestion(null);
        } else {
          setUrlCheckStatus('unavailable');
          setUrlSuggestion(result.suggestion || null);
          
          // If there's a sanitized version that's different from the input, update the form
          if (result.sanitized && result.sanitized !== storeUrl) {
            form.setValue('storeUrl', result.sanitized);
            toast.info('The URL has been sanitized to remove invalid characters');
          }
        }
      } catch (error) {
        console.error('Error checking URL availability:', error);
        setUrlCheckStatus('error');
      }
    };

    const timeoutId = setTimeout(() => {
      checkUrlAvailability();
    }, 500); // Debounce URL checks

    return () => clearTimeout(timeoutId);
  }, [form.watch('storeUrl')]);

  // Create a debounced effect to update parent state
  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      const currentValues = form.getValues();
      
      // Only update if values have actually changed
      if (
        currentValues.name !== debouncedValues.name ||
        currentValues.description !== debouncedValues.description ||
        currentValues.storeUrl !== debouncedValues.storeUrl
      ) {
        // Ensure non-optional values with defaults
        setDebouncedValues({
          name: currentValues.name || '',
          description: currentValues.description || '',
          storeUrl: currentValues.storeUrl || '',
        });
        updateData(currentValues);
      }
    }, 1000); // 1 second debounce
    
    return () => clearTimeout(timeoutId);
  }, [form.watch('name'), form.watch('description'), form.watch('storeUrl')]);

  const handleUseSuggestion = () => {
    if (urlSuggestion) {
      form.setValue('storeUrl', urlSuggestion);
      setUrlManuallyEdited(true);
    }
  };

  // Reset URL manual edit state when store name changes significantly
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    const oldName = form.getValues('name');
    
    // If name changes significantly (more than just a character), reset the manual edit flag
    if (Math.abs(newName.length - oldName.length) > 2) {
      setUrlManuallyEdited(false);
    }
    
    form.setValue('name', newName);
  };

  // Handle AI-generated content
  const handleDescriptionGenerated = (content: string) => {
    form.setValue('description', content);
  };

  // Handle AI-generated title
  const handleNameGenerated = (content: string) => {
    // Extract just the name part (before any dash or separator)
    const nameOnly = content.split('-')[0].trim();
    form.setValue('name', nameOnly);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Basic Store Information</h2>
      <p className="text-muted-foreground mb-6">
        Let's start with the essential details about your store.
      </p>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <div className="flex justify-between items-center">
                  <FormLabel>Store Name</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="h-7 gap-1">
                        <Sparkles className="h-3.5 w-3.5" />
                        <span className="text-xs">Generate Name</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[380px] p-0" align="end">
                      <AIChatWidget
                        contentType="title"
                        placeholder="What kind of products do you sell?"
                        label="Generate Store Name"
                        onContentGenerated={handleNameGenerated}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <FormControl>
                  <Input 
                    placeholder="My Awesome Store" 
                    {...field} 
                    onChange={handleNameChange}
                  />
                </FormControl>
                <FormDescription>
                  This is your store's name as it will appear to customers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <div className="flex justify-between items-center">
                  <FormLabel>Store Description</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="outline" size="sm" className="h-7 gap-1">
                        <Sparkles className="h-3.5 w-3.5" />
                        <span className="text-xs">Generate with AI</span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-[380px] p-0" align="end">
                      <AIChatWidget
                        contentType="description"
                        placeholder="What kind of store are you creating?"
                        label="Generate Store Description"
                        initialValue={field.value}
                        onContentGenerated={handleDescriptionGenerated}
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <FormControl>
                  <Textarea 
                    placeholder="Describe what your store sells and what makes it special..."
                    className="resize-none min-h-[120px]"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Help customers understand what your store offers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="storeUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Store URL</FormLabel>
                <div className="flex items-center">
                  <span className="bg-muted px-3 py-2 rounded-l-md border border-r-0 border-input text-muted-foreground">
                    m-duka.app/
                  </span>
                  <div className="flex-1 relative">
                    <Input 
                      className="rounded-l-none" 
                      placeholder="my-awesome-store" 
                      {...field} 
                      onChange={(e) => {
                        setUrlManuallyEdited(true);
                        field.onChange(e);
                      }}
                    />
                    {urlCheckStatus && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        {urlCheckStatus === 'checking' && (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        )}
                        {urlCheckStatus === 'available' && (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                        {urlCheckStatus === 'unavailable' && (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        {urlCheckStatus === 'error' && (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                {urlCheckStatus === 'unavailable' && urlSuggestion && (
                  <div className="mt-2 text-sm text-red-500">
                    This URL is already taken. 
                    <button 
                      type="button"
                      onClick={handleUseSuggestion}
                      className="ml-2 text-blue-500 hover:underline"
                    >
                      Use "m-duka.app/{urlSuggestion}" instead?
                    </button>
                  </div>
                )}
                
                <FormDescription>
                  This will be used to create a unique URL for your store.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
};

export default BasicInfoStep;
