-- Fix RLS policies to prevent infinite recursion and ensure proper security

-- 1. Create security definer function to get current user role safely
CREATE OR REPLACE FUNCTION public.get_my_role()
RETURNS text
LANGUAGE sql
STABLE SECURITY DEFINER
SET search_path = 'public'
AS $$
  SELECT COALESCE(role, 'customer')
  FROM public.profiles
  WHERE user_id = auth.uid();
$$;

-- 2. Fix potential nullable user_id issues in critical tables
-- Make user_id NOT NULL where RLS depends on it
ALTER TABLE public.customers ALTER COLUMN user_id SET NOT NULL;
ALTER TABLE public.profiles ALTER COLUMN user_id SET NOT NULL;

-- 3. Update RLS policies to use the secure function and ensure proper user_id handling
-- Update profiles policies to prevent potential access issues
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
CREATE POLICY "Users can view own profile" 
ON public.profiles 
FOR SELECT 
USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile" 
ON public.profiles 
FOR UPDATE 
USING (auth.uid() = user_id);

-- Fix customers table policies to ensure proper user_id validation
DROP POLICY IF EXISTS "Allow customer manage own record" ON public.customers;
CREATE POLICY "Allow customer manage own record" 
ON public.customers 
FOR ALL
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- Fix stores table policies for proper ownership validation
DROP POLICY IF EXISTS "Store owners can manage own store" ON public.stores;
CREATE POLICY "Store owners can manage own store" 
ON public.stores 
FOR ALL
USING ((get_my_role() = 'store_owner' OR get_my_role() = 'admin') AND owner_id = auth.uid())
WITH CHECK ((get_my_role() = 'store_owner' OR get_my_role() = 'admin') AND owner_id = auth.uid());

-- Fix products table policies
DROP POLICY IF EXISTS "Store owners can manage own products" ON public.products;
CREATE POLICY "Store owners can manage own products" 
ON public.products 
FOR ALL
USING (
  (get_my_role() = 'store_owner' OR get_my_role() = 'admin') 
  AND store_id IN (
    SELECT id FROM public.stores WHERE owner_id = auth.uid()
  )
)
WITH CHECK (
  (get_my_role() = 'store_owner' OR get_my_role() = 'admin') 
  AND store_id IN (
    SELECT id FROM public.stores WHERE owner_id = auth.uid()
  )
);

-- Fix orders table policies
DROP POLICY IF EXISTS "Allow store_owner manage own orders" ON public.orders;
CREATE POLICY "Store owners can manage their orders" 
ON public.orders 
FOR ALL
USING (
  (get_my_role() = 'store_owner' OR get_my_role() = 'admin') 
  AND store_id IN (
    SELECT id FROM public.stores WHERE owner_id = auth.uid()
  )
)
WITH CHECK (
  (get_my_role() = 'store_owner' OR get_my_role() = 'admin') 
  AND store_id IN (
    SELECT id FROM public.stores WHERE owner_id = auth.uid()
  )
);

-- Add constraint to ensure user_id is always set for customers
ALTER TABLE public.customers 
ADD CONSTRAINT check_user_id_not_empty 
CHECK (user_id IS NOT NULL AND user_id != '');

-- Add constraint to ensure owner_id is always set for stores
ALTER TABLE public.stores 
ADD CONSTRAINT check_owner_id_not_empty 
CHECK (owner_id IS NOT NULL AND owner_id != '');