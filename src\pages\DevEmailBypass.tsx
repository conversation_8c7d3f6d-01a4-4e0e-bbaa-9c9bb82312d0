import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Mail, AlertTriangle, CheckCircle } from 'lucide-react';

const DevEmailBypass: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleManualConfirm = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      // Try to get the user by email (this is a development workaround)
      const { data: { users }, error } = await supabase.auth.admin.listUsers();
      
      if (error) {
        console.error('Error listing users:', error);
        toast.error('Unable to access user list. This feature requires admin access.');
        return;
      }

      const user = users.find(u => u.email === email);
      
      if (!user) {
        toast.error('User not found. Please check the email address.');
        return;
      }

      if (user.email_confirmed_at) {
        toast.info('User email is already confirmed!');
        return;
      }

      // Attempt to confirm the user (requires admin privileges)
      const { error: confirmError } = await supabase.auth.admin.updateUserById(user.id, {
        email_confirm: true
      });

      if (confirmError) {
        console.error('Error confirming user:', confirmError);
        toast.error('Unable to confirm user. This requires admin privileges.');
        return;
      }

      toast.success('User email confirmed successfully!');
      setEmail('');
    } catch (error) {
      console.error('Manual confirm error:', error);
      toast.error('An error occurred while confirming the user.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendEmail = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) {
        console.error('Resend error:', error);
        toast.error('Failed to resend email: ' + error.message);
        return;
      }

      toast.success('Verification email resent! Check your inbox.');
    } catch (error) {
      console.error('Resend error:', error);
      toast.error('An error occurred while resending the email.');
    } finally {
      setIsLoading(false);
    }
  };

  // Only show this component in development
  const isDevelopment = import.meta.env.DEV || window.location.hostname === 'localhost';
  
  if (!isDevelopment) {
    return null;
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Development Email Tools
          </CardTitle>
          <CardDescription>
            Tools to help with email verification during development
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <strong>Development Only:</strong> These tools are only available in development mode.
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="text-sm font-medium">
              User Email Address
            </label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Button 
              onClick={handleResendEmail}
              disabled={isLoading}
              className="w-full"
              variant="outline"
            >
              {isLoading ? 'Sending...' : 'Resend Verification Email'}
            </Button>
            
            <Button 
              onClick={handleManualConfirm}
              disabled={isLoading}
              className="w-full"
            >
              {isLoading ? 'Confirming...' : 'Manual Email Confirmation'}
            </Button>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-start gap-2">
              <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5" />
              <div className="text-sm text-blue-800">
                <strong>Better Solution:</strong> Disable email confirmation in Supabase Dashboard → Authentication → Settings
              </div>
            </div>
          </div>

          <div className="text-xs text-gray-500 space-y-1">
            <p><strong>Note:</strong> Manual confirmation requires admin privileges.</p>
            <p>For production, configure proper SMTP settings in Supabase.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DevEmailBypass;
