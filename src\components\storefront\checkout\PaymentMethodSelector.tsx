
import React from 'react';
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { CreditCard, Phone, Wallet, CheckCircle2, Globe } from "lucide-react";

interface PaymentMethodSelectorProps {
  selectedMethod: string;
  onSelectMethod: (method: string) => void;
}

const PaymentMethodSelector: React.FC<PaymentMethodSelectorProps> = ({ 
  selectedMethod,
  onSelectMethod
}) => {
  const paymentMethods = [
    { 
      id: 'pesapal', 
      name: 'Pesapal', 
      description: 'Pay using Pesapal payment gateway. Supports mobile money, credit cards, and more.',
      icon: Globe,
      popular: true
    },
    { 
      id: 'credit-card', 
      name: 'Credit Card', 
      description: 'Pay with Visa, Mastercard, or American Express. Secure payment.',
      icon: CreditCard,
      popular: false
    },
    { 
      id: 'paypal', 
      name: 'PayPal', 
      description: 'Pay using your PayPal account. Fast and secure.',
      icon: Wallet,
      popular: false
    }
  ];

  return (
    <RadioGroup 
      value={selectedMethod} 
      onValueChange={onSelectMethod}
      className="space-y-4"
    >
      {paymentMethods.map(method => {
        const Icon = method.icon;
        return (
          <div key={method.id} className={`
            flex items-start space-x-3 p-4 rounded-md border relative
            ${selectedMethod === method.id ? 'border-green-500 bg-green-50' : 'border-input hover:border-green-500/50 transition-colors'}
          `}>
            <RadioGroupItem value={method.id} id={method.id} className="mt-1" />
            <div className="flex-1">
              <div className="flex items-center">
                <Label htmlFor={method.id} className="flex items-center text-base font-medium cursor-pointer">
                  <Icon className="mr-2 h-5 w-5" />
                  {method.name}
                </Label>
                {method.popular && (
                  <span className="ml-2 bg-green-100 text-green-800 text-xs px-2 py-0.5 rounded-full">
                    Popular
                  </span>
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {method.description}
              </p>
            </div>
            {selectedMethod === method.id && (
              <CheckCircle2 className="h-5 w-5 text-green-500 absolute top-4 right-4" />
            )}
          </div>
        );
      })}
    </RadioGroup>
  );
};

export default PaymentMethodSelector;
