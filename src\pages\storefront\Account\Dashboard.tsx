
import React from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ClipboardList, Heart, MapPin, ShoppingBag } from "lucide-react";
import { useAuth, useWishlist } from "@/contexts";

const AccountDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { items: wishlistItems } = useWishlist();
  
  // Mock data
  const recentOrders = [
    { id: "ORD-12345", date: "2023-10-15", status: "Delivered", total: 125.99 },
    { id: "ORD-12346", date: "2023-10-10", status: "Processing", total: 79.99 },
  ];

  return (
    <div>
      <h2 className="text-2xl font-bold mb-4">Dashboard</h2>
      <p className="text-muted-foreground">Welcome to your account dashboard</p>
      
      <div className="mb-6">
        <p className="text-muted-foreground">
          Hello, <span className="font-medium text-foreground">{user?.name || 'Customer'}</span>! 
          Welcome to your account dashboard.
        </p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <ShoppingBag className="mr-2 h-5 w-5" />
              Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">2</p>
            <p className="text-sm text-muted-foreground">Total orders placed</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <Heart className="mr-2 h-5 w-5" />
              Wishlist
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">{wishlistItems.length}</p>
            <p className="text-sm text-muted-foreground">Products in wishlist</p>
            <Button 
              variant="link" 
              className="px-0 mt-1" 
              onClick={() => navigate("/shop/account/wishlist")}
            >
              View Wishlist
            </Button>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-lg flex items-center">
              <MapPin className="mr-2 h-5 w-5" />
              Addresses
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-3xl font-bold">1</p>
            <p className="text-sm text-muted-foreground">Saved addresses</p>
          </CardContent>
        </Card>
      </div>
      
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-xl font-semibold">Recent Orders</h3>
          <Button variant="outline" size="sm" onClick={() => navigate("/shop/account/orders")}>
            View All
          </Button>
        </div>
        
        {recentOrders.length > 0 ? (
          <div className="border rounded-lg overflow-hidden">
            <table className="w-full">
              <thead className="bg-gray-50 text-xs">
                <tr>
                  <th className="px-4 py-3 text-left">Order</th>
                  <th className="px-4 py-3 text-left">Date</th>
                  <th className="px-4 py-3 text-left">Status</th>
                  <th className="px-4 py-3 text-right">Total</th>
                  <th className="px-4 py-3 text-right">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y text-sm">
                {recentOrders.map(order => (
                  <tr key={order.id}>
                    <td className="px-4 py-3">{order.id}</td>
                    <td className="px-4 py-3">{order.date}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                        order.status === 'Delivered' 
                          ? 'bg-green-100 text-green-700' 
                          : 'bg-blue-100 text-blue-700'
                      }`}>
                        {order.status}
                      </span>
                    </td>
                    <td className="px-4 py-3 text-right">${order.total.toFixed(2)}</td>
                    <td className="px-4 py-3 text-right">
                      <Button 
                        variant="link" 
                        size="sm" 
                        onClick={() => navigate(`/shop/account/orders/${order.id}`)}
                      >
                        View
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <Card>
            <CardContent className="py-8 text-center">
              <ClipboardList className="mx-auto h-12 w-12 text-gray-300 mb-2" />
              <p className="text-muted-foreground mb-4">You haven't placed any orders yet.</p>
              <Button onClick={() => navigate("/shop")}>
                Start Shopping
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default AccountDashboard;
