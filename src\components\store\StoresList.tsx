
import React from 'react';
import { Link } from 'react-router-dom';
import { useStore } from '@/contexts';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { Store as StoreIcon, Plus, Edit, Trash } from 'lucide-react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { toast } from 'sonner';

const StoresList: React.FC = () => {
  const { stores, deleteStore, isLoading } = useStore();
  const navigate = useNavigate();

  const handleDelete = async (storeId: string) => {
    try {
      await deleteStore(storeId);
    } catch (error) {
      toast.error('Failed to delete store');
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="border border-border animate-pulse">
            <CardHeader className="h-32 bg-muted/30" />
            <CardContent className="p-6">
              <div className="h-5 w-2/3 bg-muted/50 rounded mb-4" />
              <div className="h-3 w-full bg-muted/50 rounded mb-2" />
              <div className="h-3 w-4/5 bg-muted/50 rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (stores.length === 0) {
    return (
      <Card className="border-dashed border-2">
        <CardContent className="p-6 flex flex-col items-center justify-center min-h-[260px] text-center">
          <StoreIcon className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No stores yet</h3>
          <p className="text-muted-foreground mb-4">
            Get started by creating your first online store.
          </p>
          <Button onClick={() => navigate('/create-store')}>
            <Plus className="mr-2 h-4 w-4" />
            Create Store
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {stores.map((store) => (
        <Card key={store.id} className="overflow-hidden">
          <CardHeader className="p-6 bg-primary/5">
            <div className="flex justify-between items-start">
              <div className="flex items-center">
                {store.logo_url ? (
                  <img 
                    src={store.logo_url} 
                    alt={`${store.name} logo`} 
                    className="w-12 h-12 rounded-md mr-3 object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-md bg-primary/10 text-primary flex items-center justify-center mr-3">
                    <StoreIcon className="h-6 w-6" />
                  </div>
                )}
              </div>
              <div className="flex space-x-2">
                <Button 
                  variant="ghost" 
                  size="icon" 
                  onClick={() => navigate(`/edit-store/${store.id}`)}
                >
                  <Edit className="h-4 w-4" />
                  <span className="sr-only">Edit</span>
                </Button>
                
                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Trash className="h-4 w-4" />
                      <span className="sr-only">Delete</span>
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Delete Store</DialogTitle>
                      <DialogDescription>
                        Are you sure you want to delete the store "{store.name}"? 
                        This action cannot be undone.
                      </DialogDescription>
                    </DialogHeader>
                    <DialogFooter className="mt-4">
                      <DialogClose asChild>
                        <Button variant="outline">Cancel</Button>
                      </DialogClose>
                      <Button 
                        variant="destructive" 
                        onClick={() => handleDelete(store.id || '')}
                      >
                        Delete Store
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <CardTitle className="mb-2 text-xl">{store.name}</CardTitle>
            <CardDescription className="line-clamp-3">
              {store.description}
            </CardDescription>
          </CardContent>
          <CardFooter className="p-6 pt-0 flex justify-between">
            <Button 
              variant="outline" 
              onClick={() => navigate(`/store/${store.id}/dashboard`)}
            >
              Manage Store
            </Button>
            <Button 
              variant="ghost" 
              onClick={() => {
                // Use the store URL if available, otherwise fallback to ID
                const storeUrl = store.store_url || store.id;
                // Open in a new tab with the correct path
                window.open(`/shop?storeId=${storeUrl}`, '_blank');
              }}
            >
              Preview
            </Button>
          </CardFooter>
        </Card>
      ))}
      
      <Card className="border-dashed border-2">
        <CardContent className="p-6 flex flex-col items-center justify-center min-h-[260px] text-center">
          <Plus className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">Create a new store</h3>
          <p className="text-muted-foreground mb-4">
            Add another store to your portfolio.
          </p>
          <Button onClick={() => navigate('/create-store')}>
            Create Store
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default StoresList;
