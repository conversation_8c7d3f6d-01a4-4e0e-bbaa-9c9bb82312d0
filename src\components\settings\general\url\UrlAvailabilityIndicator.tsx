
import React from 'react';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';

type UrlCheckStatus = 'checking' | 'available' | 'unavailable' | 'error' | null;

interface UrlAvailabilityIndicatorProps {
  status: UrlCheckStatus;
}

const UrlAvailabilityIndicator = ({ status }: UrlAvailabilityIndicatorProps) => {
  if (!status) return null;
  
  switch (status) {
    case 'checking':
      return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    case 'available':
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    case 'unavailable':
      return <XCircle className="h-5 w-5 text-red-500" />;
    case 'error':
      return <AlertCircle className="h-5 w-5 text-red-500" />;
    default:
      return null;
  }
};

export default UrlAvailabilityIndicator;
