
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { AdminStoreType } from '@/types/admin';

export const fetchAllStores = async (): Promise<AdminStoreType[]> => {
  try {
    // Fetch stores with owner information
    const { data: stores, error } = await supabase
      .from('stores')
      .select(`
        id,
        name,
        store_url,
        store_type,
        created_at,
        owner_id,
        is_active,
        profiles:owner_id (
          name,
          email
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching stores:', error);
      toast.error('Failed to load stores');
      return [];
    }

    if (!stores) {
      return [];
    }

    // Transform the data to match our AdminStoreType
    const storesWithStats = await Promise.all(stores.map(async store => {
      const stats = await fetchStoreStatistics(store.id);
      
      // Handle the case where profile relationship might not be properly returned
      let ownerName = 'Unknown';
      try {
        // Define a type for the profiles property to help TypeScript
        interface ProfileData {
          name?: string | null;
          email?: string | null;
        }
        
        // Properly type check the profiles object
        if (store.profiles && typeof store.profiles === 'object') {
          // Cast to the defined interface
          const profileData = store.profiles as ProfileData;
          
          if (profileData.name && typeof profileData.name === 'string') {
            ownerName = profileData.name;
          }
        } 
        // If profile relationship failed, fetch the profile directly
        else if (store.owner_id) {
          const { data: profileData } = await supabase
            .from('profiles')
            .select('name')
            .eq('id', store.owner_id)
            .single();
          
          if (profileData && profileData.name) {
            ownerName = profileData.name;
          }
        }
      } catch (e) {
        console.error('Error resolving owner name:', e);
      }
      
      // Use the is_active field we just added through migration
      const isActive = typeof store.is_active === 'boolean' ? store.is_active : true;
      
      return {
        id: store.id,
        name: store.name,
        owner: ownerName,
        ownerId: store.owner_id,
        url: store.store_url || '',
        status: isActive ? 'active' : 'suspended',
        productCount: stats.productCount,
        orderCount: stats.orderCount,
        createdAt: store.created_at
      };
    }));

    return storesWithStats;
  } catch (error) {
    console.error('Error in fetchAllStores:', error);
    toast.error('Failed to load stores');
    return [];
  }
};

// Function to get store counts and statistics
export const fetchStoreStatistics = async (storeId: string): Promise<{
  productCount: number;
  orderCount: number;
}> => {
  try {
    // Get product count
    const { count: productCount, error: productError } = await supabase
      .from('products')
      .select('id', { count: 'exact', head: true })
      .eq('store_id', storeId);

    // Get order count
    const { count: orderCount, error: orderError } = await supabase
      .from('orders')
      .select('id', { count: 'exact', head: true })
      .eq('store_id', storeId);

    if (productError || orderError) {
      console.error('Error fetching store statistics:', productError || orderError);
      return { productCount: 0, orderCount: 0 };
    }

    return {
      productCount: productCount || 0,
      orderCount: orderCount || 0
    };
  } catch (error) {
    console.error('Error in fetchStoreStatistics:', error);
    return { productCount: 0, orderCount: 0 };
  }
};

// Function to update store status
export const updateStoreStatus = async (storeId: string, isActive: boolean): Promise<boolean> => {
  try {
    // Update the store status using the is_active field we just added
    const { error } = await supabase
      .from('stores')
      .update({ is_active: isActive })
      .eq('id', storeId);

    if (error) {
      console.error('Error updating store status:', error);
      toast.error('Failed to update store status');
      return false;
    }

    toast.success(`Store ${isActive ? 'activated' : 'suspended'} successfully`);
    return true;
  } catch (error) {
    console.error('Error in updateStoreStatus:', error);
    toast.error('Failed to update store status');
    return false;
  }
};
