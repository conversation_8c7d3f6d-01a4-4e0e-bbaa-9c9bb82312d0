
import React from "react";
import { useNavigate } from "react-router-dom";
import { Store, ChevronDown, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { useStore } from "@/contexts";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";

interface StoreSelectorProps {
  variant?: "desktop" | "mobile";
}

const StoreSelector = ({ variant = "desktop" }: StoreSelectorProps) => {
  const navigate = useNavigate();
  const { stores, currentStore, setCurrentStore } = useStore();

  const handleStoreChange = (store: any) => {
    setCurrentStore(store);
    localStorage.setItem('m-duka-current-store', store.id);
    toast.success(`Switched to ${store.name}`);
  };

  if (!currentStore) return null;

  return variant === "desktop" ? (
    <div className="mt-2">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="w-full justify-between">
            <span className="truncate mr-2">{currentStore.name}</span>
            <ChevronDown className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-56">
          <DropdownMenuLabel>Your Stores</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {stores.map((store) => (
            <DropdownMenuItem
              key={store.id}
              onClick={() => handleStoreChange(store)}
              className={store.id === currentStore.id ? "bg-muted" : ""}
            >
              <Store className="mr-2 h-4 w-4" />
              <span className="truncate">{store.name}</span>
              {store.id === currentStore.id && (
                <Badge variant="outline" className="ml-auto">Active</Badge>
              )}
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => navigate('/create-store')}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create New Store
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  ) : (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="mr-2">
          <Store className="h-4 w-4 mr-1" />
          <span className="max-w-[100px] truncate">{currentStore.name}</span>
          <ChevronDown className="h-3 w-3 ml-1" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Your Stores</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {stores.map((store) => (
          <DropdownMenuItem
            key={store.id}
            onClick={() => handleStoreChange(store)}
          >
            <Store className="mr-2 h-4 w-4" />
            <span>{store.name}</span>
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => navigate('/create-store')}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create New Store
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StoreSelector;
