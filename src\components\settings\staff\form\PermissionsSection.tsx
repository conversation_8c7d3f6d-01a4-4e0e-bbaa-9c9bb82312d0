
import React from 'react';
import { useFormContext } from 'react-hook-form';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
} from '@/components/ui/form';
import { Checkbox } from '@/components/ui/checkbox';
import { StaffFormValues } from '../types';

export const permissionOptions = [
  { id: 'orders', label: 'Manage Orders' },
  { id: 'products', label: 'Manage Products' },
  { id: 'customers', label: 'Manage Customers' },
  { id: 'settings', label: 'View Settings' },
  { id: 'reports', label: 'View Reports' },
];

const PermissionsSection = () => {
  const form = useFormContext<StaffFormValues>();
  
  return (
    <div className="space-y-4">
      <div className="font-medium">Permissions</div>
      <div className="space-y-2">
        {permissionOptions.map((permission) => (
          <FormField
            key={permission.id}
            control={form.control}
            name={`permissions.${permission.id}`}
            render={({ field }) => (
              <FormItem className="flex flex-row items-center space-x-3 space-y-0">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormLabel className="font-normal">
                  {permission.label}
                </FormLabel>
              </FormItem>
            )}
          />
        ))}
      </div>
    </div>
  );
};

export default PermissionsSection;
