
import { useStore } from '@/contexts/store/StoreContext';
import { Store } from '@/types/store';
import { ensureUIStore, uiToDatabaseStoreUpdate } from '@/utils/typeConverters';

/**
 * Hook to access the store context with UI-friendly types
 * This is a wrapper around useStore that ensures both camelCase and snake_case properties
 * are available for backward compatibility
 */
export const useUIStore = () => {
  const storeContext = useStore();
  
  // Convert currentStore to UI store format
  const uiStore = ensureUIStore(storeContext.currentStore);
  
  // Convert stores array to UI store format
  const uiStores = storeContext.stores.map(store => ensureUIStore(store)).filter(Boolean) as Store[];
  
  // Create a updateStore function that converts the data
  const updateUIStore = async (storeId: string, data: Partial<Store>) => {
    const dbData = uiToDatabaseStoreUpdate(data);
    return storeContext.updateStore(storeId, dbData);
  };
  
  return {
    ...storeContext,
    currentStore: uiStore,
    stores: uiStores,
    updateStore: updateUIStore
  };
};
