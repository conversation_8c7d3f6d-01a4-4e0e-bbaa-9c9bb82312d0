
import React from 'react';
import { cn } from '@/lib/utils';

interface HeroSectionProps {
  storeName: string;
  storeDescription: string;
  secondaryColor: string;
  textColor: string;
  cornerRadius: number;
  fontFamily: string;
  heroStyle?: 'image' | 'carousel' | 'video' | 'simple';
}

const HeroSection: React.FC<HeroSectionProps> = ({
  storeName,
  storeDescription,
  secondaryColor,
  textColor,
  cornerRadius,
  fontFamily,
  heroStyle = 'image'
}) => {
  const renderHeroContent = () => {
    switch (heroStyle) {
      case 'simple':
        return (
          <div className={cn(
            "py-10 px-4 text-center",
            secondaryColor,
            `rounded-[${cornerRadius}px]`
          )}>
            <h1 className={cn(
              "text-3xl md:text-4xl font-bold",
              `font-${fontFamily}`
            )}>
              Welcome to {storeName}
            </h1>
            <p className="max-w-2xl mx-auto mt-4">{storeDescription}</p>
            <button className={cn(
              "mt-6 px-6 py-2",
              textColor.replace('text', 'bg').replace('bg-primary-custom', 'bg-primary-custom'),
              "text-white rounded-full hover:opacity-90 transition-opacity"
            )}>
              Shop Now
            </button>
          </div>
        );
        
      case 'carousel':
        return (
          <div className={cn(
            "relative overflow-hidden",
            `rounded-[${cornerRadius}px]`,
            "h-[300px] md:h-[400px]"
          )}>
            <div className="absolute inset-0 bg-black/20 z-10 flex items-center justify-center">
              <div className="text-center text-white">
                <h1 className={cn(
                  "text-3xl md:text-4xl font-bold",
                  `font-${fontFamily}`
                )}>
                  {storeName}
                </h1>
                <p className="max-w-2xl mx-auto mt-4">{storeDescription}</p>
                <button className={cn(
                  "mt-6 px-6 py-2 bg-white text-gray-900",
                  `rounded-[${cornerRadius}px]`,
                  "hover:bg-gray-100 transition-colors"
                )}>
                  Shop Now
                </button>
              </div>
            </div>
            
            <div className="absolute inset-0 flex">
              <img
                src="/placeholder.svg"
                alt="Hero Image 1"
                className="w-1/3 h-full object-cover"
              />
              <img
                src="/placeholder.svg"
                alt="Hero Image 2"
                className="w-1/3 h-full object-cover"
              />
              <img
                src="/placeholder.svg"
                alt="Hero Image 3"
                className="w-1/3 h-full object-cover"
              />
            </div>
            
            {/* Carousel Dots */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-20">
              <div className="w-2 h-2 bg-white rounded-full opacity-100"></div>
              <div className="w-2 h-2 bg-white rounded-full opacity-50"></div>
              <div className="w-2 h-2 bg-white rounded-full opacity-50"></div>
            </div>
          </div>
        );
        
      case 'video':
        return (
          <div className={cn(
            "relative overflow-hidden",
            `rounded-[${cornerRadius}px]`,
            "h-[300px] md:h-[400px]"
          )}>
            <div className="absolute inset-0 bg-black/40 z-10 flex items-center justify-center">
              <div className="text-center text-white">
                <h1 className={cn(
                  "text-3xl md:text-4xl font-bold",
                  `font-${fontFamily}`
                )}>
                  {storeName}
                </h1>
                <p className="max-w-2xl mx-auto mt-4">{storeDescription}</p>
                <button className={cn(
                  "mt-6 px-6 py-2 bg-white text-gray-900",
                  `rounded-[${cornerRadius}px]`,
                  "hover:bg-gray-100 transition-colors"
                )}>
                  Shop Now
                </button>
              </div>
            </div>
            
            <div className="absolute inset-0 bg-gray-800 flex items-center justify-center">
              <div className="text-6xl text-white/10">Video Background</div>
            </div>
          </div>
        );
        
      case 'image':
      default:
        return (
          <div className={cn(
            "relative overflow-hidden",
            `rounded-[${cornerRadius}px]`,
            "h-[300px] md:h-[400px]"
          )}>
            <div className="absolute inset-0 bg-black/30 z-10 flex items-center justify-center">
              <div className="text-center text-white">
                <h1 className={cn(
                  "text-3xl md:text-4xl font-bold",
                  `font-${fontFamily}`
                )}>
                  {storeName}
                </h1>
                <p className="max-w-2xl mx-auto mt-4">{storeDescription}</p>
                <button className={cn(
                  "mt-6 px-6 py-2 bg-white text-gray-900",
                  `rounded-[${cornerRadius}px]`,
                  "hover:bg-gray-100 transition-colors"
                )}>
                  Shop Now
                </button>
              </div>
            </div>
            
            <img
              src="/placeholder.svg"
              alt="Hero"
              className="absolute inset-0 w-full h-full object-cover"
            />
          </div>
        );
    }
  };

  return (
    <section className="py-6">
      {renderHeroContent()}
    </section>
  );
};

export default HeroSection;
