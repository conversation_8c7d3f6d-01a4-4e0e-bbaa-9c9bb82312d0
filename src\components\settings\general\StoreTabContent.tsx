
import React from 'react';
import StoreInformationCard from './StoreInformationCard';
import WhatsAppSettingsCard from './WhatsAppSettingsCard';
import RegionalSettingsCard from './RegionalSettingsCard';
import TaxSettingsCard from './TaxSettingsCard';
import CommunitiesCard from './CommunitiesCard';
import DangerZoneCard from './DangerZoneCard';

interface StoreTabContentProps {
  storeName: string;
  setStoreName: (value: string) => void;
  storeDescription: string;
  setStoreDescription: (value: string) => void;
  storeUrl: string;
  setStoreUrl: (value: string) => void;
  email: string;
  setEmail: (value: string) => void;
  address: string;
  setAddress: (value: string) => void;
  country: string;
  setCountry: (value: string) => void;
  whatsappNumber: string;
  setWhatsappNumber: (value: string) => void;
  phoneNumber: string;
  setPhoneNumber: (value: string) => void;
  language: string;
  setLanguage: (value: string) => void;
  currency: string;
  setCurrency: (value: string) => void;
  taxRate: string;
  setTaxRate: (value: string) => void;
  taxMethod: string;
  setTaxMethod: (value: string) => void;
  taxId: string;
  setTaxId: (value: string) => void;
  whatsappLink: string;
  setWhatsappLink: (value: string) => void;
  instagramLink: string;
  setInstagramLink: (value: string) => void;
  facebookLink: string;
  setFacebookLink: (value: string) => void;
  loading: boolean;
  setLoading: (value: boolean) => void;
  mapProvider: string;
  setMapProvider: (value: string) => void;
  disableCheckout: boolean;
  setDisableCheckout: (value: boolean) => void;
  disableStore: boolean;
  setDisableStore: (value: boolean) => void;
  passwordProtect: boolean;
  setPasswordProtect: (value: boolean) => void;
  handleSubmit: (e: React.FormEvent) => Promise<void>;
  enableOrderNotifications?: boolean;
  setEnableOrderNotifications?: (value: boolean) => void;
  enableCustomerUpdates?: boolean;
  setEnableCustomerUpdates?: (value: boolean) => void;
  customMessage?: string;
  setCustomMessage?: (value: string) => void;
  autoReply?: boolean;
  setAutoReply?: (value: boolean) => void;
}

const StoreTabContent = ({
  storeName,
  setStoreName,
  storeUrl,
  setStoreUrl,
  email,
  setEmail,
  address,
  setAddress,
  country,
  setCountry,
  whatsappNumber,
  setWhatsappNumber,
  phoneNumber,
  setPhoneNumber,
  language,
  setLanguage,
  currency,
  setCurrency,
  taxRate,
  setTaxRate,
  taxMethod,
  setTaxMethod,
  taxId,
  setTaxId,
  whatsappLink,
  setWhatsappLink,
  instagramLink,
  setInstagramLink,
  facebookLink,
  setFacebookLink,
  loading,
  mapProvider,
  setMapProvider,
  disableCheckout,
  setDisableCheckout,
  disableStore,
  setDisableStore,
  passwordProtect,
  setPasswordProtect,
  handleSubmit,
  enableOrderNotifications = false,
  setEnableOrderNotifications = () => {},
  enableCustomerUpdates = false,
  setEnableCustomerUpdates = () => {},
  customMessage = '',
  setCustomMessage = () => {},
  autoReply = false,
  setAutoReply = () => {}
}: StoreTabContentProps) => {
  return (
    <div className="space-y-6">
      <StoreInformationCard
        storeName={storeName}
        setStoreName={setStoreName}
        storeUrl={storeUrl}
        setStoreUrl={setStoreUrl}
        email={email}
        setEmail={setEmail}
        address={address}
        setAddress={setAddress}
        loading={loading}
        onSubmit={handleSubmit}
      />
      
      <WhatsAppSettingsCard
        whatsappNumber={whatsappNumber}
        setWhatsappNumber={setWhatsappNumber}
        phoneNumber={phoneNumber}
        enableOrderNotifications={enableOrderNotifications}
        setEnableOrderNotifications={setEnableOrderNotifications}
        enableCustomerUpdates={enableCustomerUpdates}
        setEnableCustomerUpdates={setEnableCustomerUpdates}
        customMessage={customMessage}
        setCustomMessage={setCustomMessage}
        autoReply={autoReply}
        setAutoReply={setAutoReply}
        onSave={handleSubmit}
        isSaving={loading}
      />
      
      <RegionalSettingsCard
        country={country}
        setCountry={setCountry}
        language={language}
        setLanguage={setLanguage}
        currency={currency}
        setCurrency={setCurrency}
        mapProvider={mapProvider}
        setMapProvider={setMapProvider}
      />
      
      <TaxSettingsCard
        taxRate={taxRate}
        setTaxRate={setTaxRate}
        taxMethod={taxMethod}
        setTaxMethod={setTaxMethod}
        taxId={taxId}
        setTaxId={setTaxId}
      />
      
      <CommunitiesCard
        whatsappLink={whatsappLink}
        setWhatsappLink={setWhatsappLink}
        instagramLink={instagramLink}
        setInstagramLink={setInstagramLink}
        facebookLink={facebookLink}
        setFacebookLink={setFacebookLink}
      />
      
      <DangerZoneCard
        disableCheckout={disableCheckout}
        setDisableCheckout={setDisableCheckout}
        disableStore={disableStore}
        setDisableStore={setDisableStore}
        passwordProtect={passwordProtect}
        setPasswordProtect={setPasswordProtect}
      />
    </div>
  );
};

export default StoreTabContent;
