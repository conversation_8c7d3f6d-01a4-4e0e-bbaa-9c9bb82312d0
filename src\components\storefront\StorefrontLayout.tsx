import React, { Suspense, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import StorefrontHeader from "./StorefrontHeader";
import StorefrontFooter from "./StorefrontFooter";
import BottomNavigation from "./navigation/BottomNavigation";
import WhatsAppContact from "./WhatsAppContact";
import { useUIStore } from "@/hooks/useUIStore";
import FloatingCartButton from "./layout/FloatingCartButton";
import PromotionalBanner from "./marketing/PromotionalBanner";
import { LoadingFallback } from "@/components/ui/loading-fallback";
import { isStoreSubdomain } from "@/utils/authRedirects";

const StorefrontLayout: React.FC = () => {
  const isMobile = useIsMobile();
  const { currentStore } = useUIStore();
  const location = useLocation();
  const isProductDetailPage = location.pathname.includes('/product/');
  
  // Don't show WhatsApp button on product detail pages to avoid duplication
  const shouldShowWhatsApp = currentStore && !isProductDetailPage;
  
  useEffect(() => {
    console.log("StorefrontLayout mounted at path:", location.pathname);
    console.log("StorefrontLayout rendering with store:", currentStore?.name || "No store loaded");
    console.log("Is mobile:", isMobile);
    console.log("Is product detail page:", isProductDetailPage);
    console.log("Should show WhatsApp button:", shouldShowWhatsApp);
  }, [location.pathname, currentStore, isMobile, isProductDetailPage, shouldShowWhatsApp]);
  
  const onError = (error: Error) => {
    console.error("Error in StorefrontLayout:", error);
    return (
      <div className="mobile-full-height flex items-center justify-center bg-background px-4">
        <div className="text-center mobile-card max-w-md w-full">
          <h2 className="text-responsive-xl font-bold text-destructive mb-4">Something went wrong</h2>
          <p className="mb-4 text-muted-foreground text-responsive-sm">
            We encountered an error loading the store content.
          </p>
          <p className="text-xs text-muted-foreground">{error.message}</p>
        </div>
      </div>
    );
  };
  
  return (
    <div className="flex flex-col mobile-full-height bg-background">
      <PromotionalBanner />
      <StorefrontHeader />
      
      <main className="flex-1 overflow-auto">
        <Suspense fallback={
          <LoadingFallback 
            size="large" 
            message="Loading storefront content..." 
            className="mobile-full-height" 
          />
        }>
          <div className="mobile-container mobile-section">
            <Outlet />
          </div>
        </Suspense>
      </main>
      
      {/* Show footer on desktop, navigation on mobile */}
      {!isMobile && <StorefrontFooter />}
      {isMobile && <BottomNavigation />}
      
      {/* Only show WhatsApp contact button if store exists and not on product detail page */}
      {shouldShowWhatsApp && <WhatsAppContact store={currentStore} />}
      
      {/* Show floating cart button */}
      <FloatingCartButton />
    </div>
  );
};

export default StorefrontLayout;
