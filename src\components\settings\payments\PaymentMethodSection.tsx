
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Info, ExternalLink } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  region?: string;
  popular?: boolean;
  fields?: {
    id: string;
    name: string;
    placeholder: string;
    type: string;
    required?: boolean;
    tooltip?: string;
  }[];
  setupUrl?: string;
  testMode?: boolean;
}

interface PaymentMethodProps {
  method: PaymentMethod;
  enabled: boolean;
  onToggle: (methodId: string, enabled: boolean) => void;
}

const PaymentMethodSection: React.FC<PaymentMethodProps> = ({
  method,
  enabled,
  onToggle
}) => {
  const { toast } = useToast();
  
  const handleSaveDetails = (e: React.FormEvent) => {
    e.preventDefault();
    toast({
      title: "Settings saved",
      description: `${method.name} configuration has been updated.`,
    });
  };
  
  return (
    <div className={`border rounded-lg p-5 mb-4 transition-all ${enabled ? 'border-green-200 bg-green-50/30' : 'border-gray-200'}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <div className="flex items-center justify-center w-10 h-10 bg-white rounded-full border shadow-sm">
            {method.icon}
          </div>
          <div>
            <div className="flex items-center gap-2">
              <h3 className="font-medium text-lg">{method.name}</h3>
              {method.popular && (
                <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">
                  Popular
                </Badge>
              )}
              {method.testMode && (
                <Badge variant="outline" className="text-amber-600 border-amber-200 bg-amber-50">
                  Test Mode
                </Badge>
              )}
            </div>
            <p className="text-sm text-muted-foreground">{method.description}</p>
          </div>
        </div>
        <Switch 
          checked={enabled} 
          onCheckedChange={(checked) => onToggle(method.id, checked)}
        />
      </div>
      
      {enabled && method.fields && (
        <form onSubmit={handleSaveDetails} className="mt-4 border-t pt-4">
          <div className="grid gap-4">
            {method.fields.map((field) => (
              <div key={field.id} className="grid gap-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor={`${method.id}-${field.id}`}>
                    {field.name} {field.required && <span className="text-red-500">*</span>}
                  </Label>
                  {field.tooltip && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="w-60">{field.tooltip}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
                <Input 
                  id={`${method.id}-${field.id}`}
                  type={field.type} 
                  placeholder={field.placeholder}
                  required={field.required}
                />
              </div>
            ))}
            <div className="flex items-center justify-between mt-2">
              <div>
                {method.setupUrl && (
                  <Button type="button" variant="link" size="sm" className="px-0" onClick={() => window.open(method.setupUrl, '_blank')}>
                    <ExternalLink className="h-4 w-4 mr-1" />
                    Setup Account
                  </Button>
                )}
              </div>
              <Button type="submit" size="sm">
                <CheckCircle className="h-4 w-4 mr-1" />
                Save Details
              </Button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
};

export default PaymentMethodSection;
