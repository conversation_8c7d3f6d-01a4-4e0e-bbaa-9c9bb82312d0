
import React from 'react';
import { Link } from 'react-router-dom';
import { Clock } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

export interface WorkflowNavItemProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href: string;
  active?: boolean;
  status?: 'available' | 'coming-soon' | 'beta';
  date?: string;
}

const WorkflowNavItem: React.FC<WorkflowNavItemProps> = ({ 
  icon, 
  title, 
  description, 
  href, 
  active = false,
  status = 'available',
  date
}) => {
  return (
    <Link
      to={href}
      className={cn(
        "flex items-start gap-3 rounded-md p-3 transition-colors",
        active ? "bg-secondary" : "hover:bg-secondary/50"
      )}
    >
      <div className={cn(
        "mt-0.5 rounded-md bg-primary/10 p-1.5 text-primary",
        active && "bg-primary/20"
      )}>
        {icon}
      </div>
      <div className="space-y-0.5 flex-1">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium">{title}</p>
          {status === 'coming-soon' && (
            <Badge variant="outline" className="text-xs font-normal">
              Soon
            </Badge>
          )}
          {status === 'beta' && (
            <Badge variant="outline" className="bg-amber-100 text-amber-800 border-amber-200 text-xs font-normal">
              Beta
            </Badge>
          )}
        </div>
        <p className="text-xs text-muted-foreground">{description}</p>
        {date && status === 'coming-soon' && (
          <div className="flex items-center text-xs text-muted-foreground mt-1">
            <Clock className="h-3 w-3 mr-1" />
            <span>{date}</span>
          </div>
        )}
      </div>
    </Link>
  );
};

export default WorkflowNavItem;
