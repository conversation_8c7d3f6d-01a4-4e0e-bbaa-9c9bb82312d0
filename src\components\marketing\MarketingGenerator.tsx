import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { generateAIContent } from '@/services/aiGenerationService';
import { 
  Copy, 
  Check, 
  Sparkles, 
  Loader2, 
  MessageSquare, 
  BadgeDollarSign, 
  HelpCircle 
} from 'lucide-react';
import { toast } from 'sonner';

type ContentType = 'marketing' | 'seo' | 'faq' | 'pricing';

interface MarketingGeneratorProps {
  defaultProductName?: string;
  defaultProductDescription?: string;
  defaultCategory?: string;
  defaultPrice?: number;
}

const MarketingGenerator: React.FC<MarketingGeneratorProps> = ({
  defaultProductName = '',
  defaultProductDescription = '',
  defaultCategory = '',
  defaultPrice,
}) => {
  const [productName, setProductName] = useState(defaultProductName);
  const [productDescription, setProductDescription] = useState(defaultProductDescription);
  const [category, setCategory] = useState(defaultCategory);
  const [promoType, setPromoType] = useState('discount');
  const [generatedContent, setGeneratedContent] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentType, setContentType] = useState<ContentType>('marketing');
  const [copied, setCopied] = useState(false);

  const generateContent = async () => {
    if (!productName) {
      toast.error('Please enter a product name');
      return;
    }

    setIsGenerating(true);

    try {
      const context = [
        productDescription && `Description: ${productDescription}`,
        category && `Category: ${category}`,
        defaultPrice && `Price: $${defaultPrice}`,
        promoType && contentType === 'marketing' ? `Promotion Type: ${promoType}` : ''
      ].filter(Boolean).join('\n');

      const prompt = contentType === 'pricing' 
        ? `${productName} priced around $${defaultPrice || '49.99'}`
        : productName;

      const response = await generateAIContent({
        prompt,
        context,
        type: contentType
      });

      if (response.isError) {
        toast.error('Failed to generate content');
        return;
      }

      setGeneratedContent(response.content);
      toast.success(`${getContentTypeLabel()} generated successfully!`);
    } catch (error) {
      console.error('Error generating content:', error);
      toast.error('Something went wrong');
    } finally {
      setIsGenerating(false);
    }
  };

  const getContentTypeLabel = () => {
    switch (contentType) {
      case 'marketing':
        return 'Marketing message';
      case 'seo':
        return 'SEO description';
      case 'faq':
        return 'Product FAQs';
      case 'pricing':
        return 'Pricing suggestion';
      default:
        return 'Content';
    }
  };

  const getContentTypeIcon = () => {
    switch (contentType) {
      case 'marketing':
        return <MessageSquare className="h-4 w-4" />;
      case 'seo':
        return <Sparkles className="h-4 w-4" />;
      case 'faq':
        return <HelpCircle className="h-4 w-4" />;
      case 'pricing':
        return <BadgeDollarSign className="h-4 w-4" />;
      default:
        return <Sparkles className="h-4 w-4" />;
    }
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generatedContent);
    setCopied(true);
    toast.info('Content copied to clipboard');
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          AI Marketing Assistant
        </CardTitle>
        <CardDescription>
          Generate professional marketing content for your products
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        <Tabs
          value={contentType}
          onValueChange={(value) => setContentType(value as ContentType)}
          className="w-full"
        >
          <TabsList className="grid grid-cols-4 mb-4">
            <TabsTrigger value="marketing">
              <MessageSquare className="h-4 w-4 mr-2" />
              Promotional
            </TabsTrigger>
            <TabsTrigger value="seo">
              <Sparkles className="h-4 w-4 mr-2" />
              SEO
            </TabsTrigger>
            <TabsTrigger value="faq">
              <HelpCircle className="h-4 w-4 mr-2" />
              FAQs
            </TabsTrigger>
            <TabsTrigger value="pricing">
              <BadgeDollarSign className="h-4 w-4 mr-2" />
              Pricing
            </TabsTrigger>
          </TabsList>
        </Tabs>

        <div className="space-y-4">
          <div>
            <Label htmlFor="product-name">Product Name</Label>
            <Input
              id="product-name"
              value={productName}
              onChange={(e) => setProductName(e.target.value)}
              placeholder="e.g., Ultra Comfort Running Shoes"
              className="mt-1"
            />
          </div>

          {contentType === 'marketing' && (
            <div>
              <Label htmlFor="promo-type">Promotion Type</Label>
              <Select
                value={promoType}
                onValueChange={setPromoType}
              >
                <SelectTrigger id="promo-type" className="mt-1">
                  <SelectValue placeholder="Select promotion type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="discount">Discount/Sale</SelectItem>
                  <SelectItem value="holiday">Holiday Special</SelectItem>
                  <SelectItem value="launch">Product Launch</SelectItem>
                  <SelectItem value="limited">Limited Edition</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label htmlFor="product-description">Product Description (Optional)</Label>
            <Textarea
              id="product-description"
              value={productDescription}
              onChange={(e) => setProductDescription(e.target.value)}
              placeholder="Provide details about your product to generate more targeted content"
              className="mt-1 resize-none"
              rows={3}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category">Category (Optional)</Label>
              <Input
                id="category"
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                placeholder="e.g., Footwear, Electronics"
                className="mt-1"
              />
            </div>
            
            {contentType === 'pricing' && (
              <div>
                <Label htmlFor="price">Current Price (Optional)</Label>
                <Input
                  id="price"
                  type="number"
                  value={defaultPrice?.toString() || ''}
                  onChange={(e) => {
                    const value = e.target.value ? parseFloat(e.target.value) : undefined;
                    if (value !== undefined) {
                      // This is a dummy onChange since we're not actually updating defaultPrice in this component
                    }
                  }}
                  placeholder="e.g., 49.99"
                  className="mt-1"
                />
              </div>
            )}
          </div>

          <Button
            onClick={generateContent}
            className="w-full"
            disabled={isGenerating || !productName}
          >
            {isGenerating ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating...
              </>
            ) : (
              <>
                {getContentTypeIcon()}
                <span className="ml-2">Generate {getContentTypeLabel()}</span>
              </>
            )}
          </Button>

          {generatedContent && (
            <div className="mt-4 space-y-2">
              <Label>Generated Content</Label>
              <div className="relative">
                <Textarea
                  value={generatedContent}
                  onChange={(e) => setGeneratedContent(e.target.value)}
                  className="min-h-[200px] pr-10"
                  rows={8}
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-2 top-2"
                  onClick={copyToClipboard}
                >
                  {copied ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="flex justify-between bg-muted/50">
        <div className="text-xs text-muted-foreground">
          AI-generated content may need review and editing
        </div>
      </CardFooter>
    </Card>
  );
};

export default MarketingGenerator;