
import { supabase } from "@/integrations/supabase/client";

/**
 * Update user profile information
 */
export const updateUserProfile = async (userId: string, data: { name?: string; phone?: string; bio?: string; avatar_url?: string }) => {
  try {
    const { error } = await supabase
      .from("profiles")
      .update(data)
      .eq("user_id", userId);
    
    if (error) {
      console.error("Profile update error:", error);
      throw error;
    }
    
    return { success: true };
  } catch (error) {
    console.error("Error updating profile:", error);
    throw error;
  }
};

/**
 * Fetch full user profile
 */
export const getUserProfile = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("user_id", userId)
      .single();
    
    if (error) {
      console.error("Profile fetch error:", error);
      throw error;
    }
    
    return data;
  } catch (error) {
    console.error("Error fetching profile:", error);
    throw error;
  }
};

/**
 * Upload a profile avatar
 */
export const uploadProfileAvatar = async (userId: string, file: File) => {
  try {
    const fileExt = file.name.split('.').pop();
    const filePath = `avatars/${userId}.${fileExt}`;
    
    // Upload the file to Supabase storage
    const { error: uploadError } = await supabase.storage
      .from('profiles')
      .upload(filePath, file, {
        upsert: true,
        cacheControl: '3600',
        contentType: file.type,
      });
      
    if (uploadError) {
      throw uploadError;
    }
    
    // Get the public URL
    const { data: publicUrlData } = supabase.storage
      .from('profiles')
      .getPublicUrl(filePath);
      
    // Update the profile with the avatar URL
    const { error: updateError } = await supabase
      .from('profiles')
      .update({ avatar_url: publicUrlData.publicUrl })
      .eq('user_id', userId);
      
    if (updateError) {
      throw updateError;
    }
    
    return { success: true, avatarUrl: publicUrlData.publicUrl };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    throw error;
  }
};
