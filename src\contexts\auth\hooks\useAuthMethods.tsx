
import { User } from '../types';
import { supabase } from '@/lib/supabase';
import { UserRole } from '@/constants/roles';
import { signupUser } from '../api/registerApi';
import { signInWithSocialProvider } from '../api/socialAuthApi';
import { loginUser } from '../api/loginApi';
import { sendMagicLink, inviteUser as inviteUserApi, updateUserEmail, reauthenticateUser, confirmSignUp as confirmSignUpApi } from '../api';
import { sendNotificationEmail } from "@/services/notificationService";

export const useAuthMethods = () => {
  const login = async (email: string, password: string) => {
    try {
      const response = await loginUser(email, password);
      
      if (response.error) {
        return { user: null, error: response.error };
      }
      
      if (response.data.user) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('user_id', response.data.user.id)
          .single();
          
        if (profile) {
          const userData: User = {
            id: response.data.user.id,
            email: response.data.user.email || '',
            name: profile.name || '',
            role: profile.role,
            avatar_url: profile.avatar_url
          };

          return { user: userData, error: null };
        } else {
          // TEMPORARY FIX: Create a basic user object if profile is missing
          console.warn('⚠️ Profile not found, creating temporary user object');
          const tempUser: User = {
            id: response.data.user.id,
            email: response.data.user.email || '',
            name: response.data.user.user_metadata?.name || response.data.user.email?.split('@')[0] || 'User',
            role: response.data.user.user_metadata?.role || 'store_owner',
            avatar_url: null
          };

          // Try to create the missing profile in the background
          try {
            await supabase.from('profiles').insert({
              user_id: response.data.user.id,
              email: response.data.user.email,
              name: tempUser.name,
              role: tempUser.role
            });
            console.log('✅ Profile created in background');
          } catch (profileError) {
            console.warn('Failed to create profile in background:', profileError);
          }

          return { user: tempUser, error: null };
        }
      }

      return { user: null, error: new Error('Login failed') };
    } catch (error) {
      console.error("Error during login:", error);
      return { user: null, error };
    }
  };

  const signup = async (name: string, email: string, password: string, role = UserRole.StoreOwner) => {
    try {
      await signupUser(name, email, password, role);
      
      const { data: settings } = await supabase
        .from("notification_settings")
        .select("admin_email")
        .single();

      if (settings?.admin_email) {
        await sendNotificationEmail(
          "signup",
          { name, email },
          settings.admin_email
        );
      }

      return { success: true, error: null };
    } catch (error) {
      console.error("Error during signup:", error);
      return { success: false, error };
    }
  };

  const signInWithSocial = async (provider: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider as 'google' | 'github' | 'facebook' | 'apple',
      });

      if (error) throw error;

      return { success: true, url: data.url };
    } catch (error) {
      return { success: false, error };
    }
  };

  const logout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  };

  const loginAsAdmin = async () => {
    return { success: true };
  };

  const resetPassword = async (email: string) => {
    const { error } = await supabase.auth.resetPasswordForEmail(email);
    if (error) throw error;
  };

  const updatePassword = async (password: string) => {
    const { error } = await supabase.auth.updateUser({ password });
    if (error) throw error;
  };

  return {
    login,
    signup,
    signInWithSocial,
    logout,
    loginAsAdmin,
    resetPassword,
    updatePassword,
    sendMagicLink,
    inviteUser: inviteUserApi,
    updateEmail: updateUserEmail,
    reauthenticate: reauthenticateUser,
    confirmSignUp: confirmSignUpApi,
  };
};
