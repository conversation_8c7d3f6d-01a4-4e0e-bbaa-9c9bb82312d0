
import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Package } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const AddOnsCard = () => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" /> 
          Add-ons
        </CardTitle>
        <CardDescription>
          Enable additional features beyond your plan's quota
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="text-center py-6">
          <Package className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
          <h3 className="text-lg font-medium mb-2">Add-ons available with paid plans</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Upgrade to a paid plan to access additional features and resources
          </p>
          <Button>Choose a Plan</Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default AddOnsCard;
