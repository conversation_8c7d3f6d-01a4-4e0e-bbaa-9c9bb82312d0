
import React from "react";
import { Container } from "@/components/ui/container";
import { Heading } from "@/components/ui/heading";
import { ShoppingBag } from "lucide-react";
import FeaturedProductsPage from "./storefront/FeaturedProducts";

// This is a wrapper component that redirects to the storefront FeaturedProducts page
const FeaturedProducts: React.FC = () => {
  return (
    <Container>
      <Heading 
        title="Featured Products" 
        description="View and manage featured products"
        icon={<ShoppingBag className="h-5 w-5" />}
        className=""
      />
      <div className="mt-6">
        <FeaturedProductsPage />
      </div>
    </Container>
  );
};

export default FeaturedProducts;
