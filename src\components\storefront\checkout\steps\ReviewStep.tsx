
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Address } from "@/types/order";
import { useCart } from "@/contexts";
import { formatCurrency } from "@/utils/formatters";
import { ChevronLeft, CreditCard, Phone, Wallet, Globe } from "lucide-react";

interface ReviewStepProps {
  shippingAddress: Address;
  paymentMethod: string;
  onPrev: () => void;
  onPlaceOrder: () => void;
  isProcessing: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({
  shippingAddress,
  paymentMethod,
  onPrev,
  onPlaceOrder,
  isProcessing
}) => {
  const { items, subtotal, shipping, total } = useCart();

  const getPaymentIcon = () => {
    switch (paymentMethod) {
      case "mpesa":
        return <Phone className="h-4 w-4" />;
      case "pesapal":
        return <Globe className="h-4 w-4" />;
      case "credit-card":
        return <CreditCard className="h-4 w-4" />;
      case "paypal":
        return <Wallet className="h-4 w-4" />;
      default:
        return null;
    }
  };

  const getPaymentMethodName = () => {
    switch (paymentMethod) {
      case "mpesa":
        return "M-Pesa";
      case "pesapal":
        return "Pesapal";
      case "credit-card":
        return "Credit Card";
      case "paypal":
        return "PayPal";
      default:
        return "";
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="p-6">
          <h2 className="text-xl font-bold mb-4">Order Review</h2>
          
          <div className="space-y-6">
            {/* Shipping Address */}
            <div>
              <h3 className="font-medium text-base mb-2">Shipping Address</h3>
              <div className="bg-muted p-3 rounded-md">
                <p>{shippingAddress.firstName} {shippingAddress.lastName}</p>
                <p>{shippingAddress.addressLine1}</p>
                {shippingAddress.addressLine2 && <p>{shippingAddress.addressLine2}</p>}
                <p>{shippingAddress.city}, {shippingAddress.state} {shippingAddress.postalCode}</p>
                <p>{shippingAddress.country}</p>
                <p className="mt-1">{shippingAddress.phone}</p>
                <p>{shippingAddress.email}</p>
              </div>
            </div>
            
            {/* Payment Method */}
            <div>
              <h3 className="font-medium text-base mb-2">Payment Method</h3>
              <div className="bg-muted p-3 rounded-md flex items-center">
                <div className="mr-2">
                  {getPaymentIcon()}
                </div>
                <div className="text-sm">
                  <p>{getPaymentMethodName()}</p>
                </div>
              </div>
            </div>
            
            {/* Order Items */}
            <div>
              <h3 className="font-medium text-base mb-2">Items ({items.length})</h3>
              <div className="space-y-3">
                {items.map((item) => (
                  <div key={item.id} className="flex justify-between border-b pb-2">
                    <div className="flex">
                      <div className="text-sm">
                        <p className="font-medium">{item.product.name}</p>
                        {item.variant && (
                          <p className="text-muted-foreground text-xs">
                            {item.variant.name}
                          </p>
                        )}
                        <p className="text-xs">Qty: {item.quantity}</p>
                      </div>
                    </div>
                    <div className="text-sm font-medium">
                      {formatCurrency(item.product.price * item.quantity, "USD")}
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Order Summary */}
            <div>
              <h3 className="font-medium text-base mb-2">Order Summary</h3>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Subtotal</span>
                  <span>{formatCurrency(subtotal, "USD")}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Shipping</span>
                  <span>{shipping === 0 ? "Free" : formatCurrency(shipping, "USD")}</span>
                </div>
                <div className="border-t pt-2 flex justify-between font-medium">
                  <span>Total</span>
                  <span>{formatCurrency(total, "USD")}</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="flex justify-between pt-4">
        <Button 
          variant="outline" 
          onClick={onPrev}
          disabled={isProcessing}
        >
          <ChevronLeft className="h-4 w-4 mr-2" />
          Back to Payment
        </Button>
        <Button 
          onClick={onPlaceOrder} 
          disabled={isProcessing}
        >
          {isProcessing ? "Processing..." : "Place Order"}
        </Button>
      </div>
    </div>
  );
};

export default ReviewStep;
