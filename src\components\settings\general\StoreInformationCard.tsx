
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import StoreUrlInput from './url/StoreUrlInput';
import { useStoreUrlValidation } from '@/hooks/settings/useStoreUrlValidation';

interface StoreInformationCardProps {
  storeName: string;
  setStoreName: (value: string) => void;
  storeUrl: string;
  setStoreUrl: (value: string) => void;
  email: string;
  setEmail: (value: string) => void;
  address: string;
  setAddress: (value: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
}

const StoreInformationCard = ({
  storeName,
  setStoreName,
  storeUrl,
  setStoreUrl,
  email,
  setEmail,
  address,
  setAddress,
  loading,
  onSubmit
}: StoreInformationCardProps) => {
  const {
    urlCheckStatus,
    urlSuggestion,
    handleUrlChange,
    handleStoreNameChange,
    handleUseSuggestion
  } = useStoreUrlValidation(storeUrl, setStoreUrl, storeName);

  const onStoreNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = handleStoreNameChange(e);
    setStoreName(e.target.value);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Store Information</CardTitle>
        <CardDescription>
          Basic details about your store
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={onSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="store-name">Name</Label>
            <Input
              id="store-name"
              value={storeName}
              onChange={onStoreNameChange}
              placeholder="Enter your store name"
              className="font-medium"
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="store-link">Store link</Label>
            <StoreUrlInput
              storeUrl={storeUrl}
              onUrlChange={handleUrlChange}
              urlCheckStatus={urlCheckStatus}
              urlSuggestion={urlSuggestion}
              onUseSuggestion={handleUseSuggestion}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="store-email">Email</Label>
            <Input 
              id="store-email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your contact email"
            />
            <p className="text-xs text-muted-foreground">
              Customers will receive emails from this address
            </p>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="store-address">Address</Label>
            <Textarea 
              id="store-address"
              value={address}
              onChange={(e) => setAddress(e.target.value)}
              placeholder="Enter your store address"
              rows={2}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="country-code">Country</Label>
            <Input 
              id="country-code" 
              value="TZ" 
              disabled
              className="w-20 bg-muted" 
            />
          </div>

          <Button type="submit" disabled={loading || urlCheckStatus === 'unavailable' || urlCheckStatus === 'checking'}>
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default StoreInformationCard;
