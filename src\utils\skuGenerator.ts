
/**
 * Generates a SKU for a product or variant
 * @param baseSku - Base SKU for the product
 * @param size - Size variant (optional)
 * @param color - Color variant (optional)
 * @returns Generated SKU string
 */
export const generateSku = (baseSku: string | undefined, size?: string, color?: string): string => {
  if (!baseSku) return '';
  
  const sizeCode = size && size !== 'N/A' ? `-${size}` : '';
  const colorCode = color && color !== 'N/A' ? `-${color.substring(0, 3).toUpperCase()}` : '';
  
  return `${baseSku}${sizeCode}${colorCode}`;
};
