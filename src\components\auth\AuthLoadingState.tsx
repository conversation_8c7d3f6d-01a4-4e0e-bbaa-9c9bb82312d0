import React from 'react';
import { Loader2, Shield } from 'lucide-react';

interface AuthLoadingStateProps {
  message?: string;
}

export const AuthLoadingState: React.FC<AuthLoadingStateProps> = ({ 
  message = "Authenticating..." 
}) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen bg-gray-50">
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full mx-auto">
          <Shield className="w-8 h-8 text-blue-600" />
        </div>
        <div className="flex items-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
          <p className="text-gray-700 font-medium">{message}</p>
        </div>
        <p className="text-sm text-gray-500 max-w-md">
          Please wait while we securely authenticate your account and sync your profile.
        </p>
      </div>
    </div>
  );
}; 