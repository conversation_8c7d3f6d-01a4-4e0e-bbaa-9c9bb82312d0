
import React from 'react';
import { useFormContext } from 'react-hook-form';
import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import { MessageSquare } from 'lucide-react';
import { StaffFormValues } from '../types';

const ContactInfoSection = ({ isEditMode = false }: { isEditMode?: boolean }) => {
  const form = useFormContext<StaffFormValues>();
  
  return (
    <>
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Email Address</FormLabel>
            <FormControl>
              <Input 
                placeholder="<EMAIL>" 
                {...field} 
                disabled={isEditMode}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="whatsappNumber"
        render={({ field }) => (
          <FormItem>
            <FormLabel>WhatsApp Number</FormLabel>
            <FormControl>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <MessageSquare className="absolute left-3 top-2.5 h-4 w-4 text-green-600" />
                  <Input 
                    placeholder="+254700000000" 
                    className="pl-10"
                    {...field} 
                  />
                </div>
              </div>
            </FormControl>
            <FormDescription>
              Enter the full phone number with country code (e.g., +254700000000)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="receiveNotifications"
        render={({ field }) => (
          <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
            <FormControl>
              <Checkbox
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <div className="space-y-1 leading-none">
              <FormLabel>Receive WhatsApp Notifications</FormLabel>
              <FormDescription>
                Staff member will receive notifications about relevant events based on their role and permissions.
              </FormDescription>
            </div>
          </FormItem>
        )}
      />
    </>
  );
};

export default ContactInfoSection;
