
import React from 'react';
import ProductItem from './components/ProductItem';
import { ProductsSectionProps } from './types/product-section';
import { cn } from '@/lib/utils';

const ProductsSection: React.FC<ProductsSectionProps> = ({
  title,
  products,
  showRatings,
  showQuickAdd,
  layoutType,
  cornerRadius,
  fontFamily,
  darkMode,
  buttonClasses,
  primaryColor,
  imageClasses,
  displayCurrency = true,
  enableNewBadge = true,
  enableShareButtons = true,
}) => {
  return (
    <section className="py-6">
      <h2 className={`text-2xl font-bold mb-4 font-${fontFamily}`}>
        {title}
      </h2>
      
      <div className={layoutType === 'grid' ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6' : 'space-y-4'}>
        {products.length > 0 ? (
          products.map((product) => (
            <ProductItem
              key={product.id}
              product={product}
              showRatings={showRatings}
              showQuickAdd={showQuickAdd}
              layoutType={layoutType}
              cornerRadius={cornerRadius}
              fontFamily={fontFamily}
              darkMode={darkMode}
              buttonClasses={buttonClasses}
              primaryColor={primaryColor}
              imageClasses={imageClasses}
              displayCurrency={displayCurrency}
              enableNewBadge={enableNewBadge}
              enableShareButtons={enableShareButtons}
            />
          ))
        ) : (
          <div className="col-span-full text-center py-6">
            <p className={cn(
              "text-muted-foreground",
              darkMode ? "text-gray-400" : "text-gray-500"
            )}>
              No products available.
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default ProductsSection;
