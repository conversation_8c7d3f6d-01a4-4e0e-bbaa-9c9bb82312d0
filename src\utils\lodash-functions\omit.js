
/**
 * Creates an object composed of the own and inherited enumerable property paths of object that are not omitted.
 *
 * @param {Object} object - The source object
 * @param {Array|string} paths - The property paths to omit
 * @returns {Object} Returns the new object
 */
function omit(object, paths) {
  if (!object) return {};
  
  // Ensure paths is an array
  const pathsArray = Array.isArray(paths) ? paths : [paths];
  
  // Create a new object with the properties that aren't omitted
  const result = {};
  
  // If object is null or undefined, return empty object
  if (object == null) return result;
  
  // Get all own enumerable property names
  const props = Object.keys(object);
  
  for (let i = 0; i < props.length; i++) {
    const prop = props[i];
    if (!pathsArray.includes(prop)) {
      result[prop] = object[prop];
    }
  }
  
  return result;
}

// Support both ESM and CJS
export default omit;
