
import React from "react";

// A simple starry night effect using absolutely-positioned divs and CSS animation.
const NUM_STARS = 70;

// Create a random position and size for each star
const generateStars = () => {
  return Array.from({ length: NUM_STARS }, (_, i) => {
    const size = Math.random() * 1.6 + 1.2;
    const left = Math.random() * 100;
    const top = Math.random() * 100;
    const duration = Math.random() * 1.5 + 2.5; // twinkle duration
    const delay = Math.random() * 2;
    return (
      <div
        key={i}
        className="absolute bg-white rounded-full opacity-80 pointer-events-none"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          filter: "drop-shadow(0 0 4px rgba(255,255,255,0.6))",
          animation: `star-twinkle ${duration}s infinite alternate`,
          animationDelay: `${delay}s`,
        }}
      />
    );
  });
};

const StarryBackground: React.FC = () => {
  return (
    <div
      className="fixed inset-0 z-0 bg-[#111926] overflow-hidden"
      aria-hidden="true"
      style={{
        background: "radial-gradient(ellipse at 70% 25%, #313a53 0%, transparent 80%), #111926",
      }}
    >
      {generateStars()}
      {/* Add overlay for glass effect */}
      <div className="absolute inset-0 bg-black/20 backdrop-blur-[2px]"></div>
      {/* Star twinkle animation */}
      <style>{`
        @keyframes star-twinkle {
          0% { opacity: 0.5; transform: scale(1); }
          70% { opacity: 0.98; transform: scale(1.25);}
          100% { opacity: 0.3; transform: scale(0.86);}
        }
      `}</style>
    </div>
  );
};

export default StarryBackground;
