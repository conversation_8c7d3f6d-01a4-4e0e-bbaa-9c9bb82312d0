
/**
 * Checks if value is classified as a String primitive or object.
 *
 * @param {*} value - The value to check
 * @returns {boolean} Returns true if value is a string, else false
 */
function isString(value) {
  return typeof value === 'string' || (value && typeof value === 'object' && Object.prototype.toString.call(value) === '[object String]');
}

// Support both ESM and CJS
export default isString;
