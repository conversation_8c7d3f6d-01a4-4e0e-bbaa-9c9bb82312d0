
import { motion } from "framer-motion";
import { Check } from "lucide-react";

const TrustedBy = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.5 }}
      className="mt-20"
    >
      <div className="text-center mb-6">
        <p className="text-sm font-medium text-muted-foreground">
          TRUSTED BY THOUSANDS OF BUSINESSES ACROSS TANZANIA
        </p>
      </div>
      
      <div className="bg-white rounded-lg shadow-md border border-gray-100 p-6">
        <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
          {[
            'Dar Groceries',
            'Dodoma Fashion Hub',
            'Arusha Eats',
            'Zanzibar Crafts',
            'Mwanza Essentials'
          ].map((brand, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 + (index * 0.1) }}
              className="flex flex-col items-center"
            >
              <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center mb-3">
                <span className="text-green-600 font-bold text-lg">{brand.charAt(0)}</span>
              </div>
              <div className="text-sm font-medium text-center">{brand}</div>
              <div className="flex items-center mt-1 text-yellow-500">
                {[1, 2, 3, 4, 5].map(i => (
                  <svg key={i} className="w-3 h-3 fill-current" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
          {[
            { number: '5,000+', text: 'Active businesses using M-Duka in Tanzania' },
            { number: '150k+', text: 'Orders processed monthly' },
            { number: '98%', text: 'Customer satisfaction rating' },
          ].map((stat, index) => (
            <motion.div 
              key={index}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1 + (index * 0.1) }}
              className="text-center p-3 rounded-lg bg-accent/50"
            >
              <p className="text-2xl font-bold text-green-600">{stat.number}</p>
              <p className="text-sm text-gray-700">{stat.text}</p>
            </motion.div>
          ))}
        </div>
        
        <div className="mt-8 bg-green-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                <Check className="h-5 w-5 text-green-600" />
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-1">Verified by Tanzania Business Review</h4>
              <p className="text-xs text-gray-600">M-Duka has been verified as one of the top 10 e-commerce solutions for small businesses in Tanzania in 2023.</p>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default TrustedBy;
