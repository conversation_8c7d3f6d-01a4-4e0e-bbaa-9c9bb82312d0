
import React from 'react';
import CustomDomainCard from '@/components/settings/domains/CustomDomainCard';
import { useStore } from '@/contexts';
import { ExternalLink } from 'lucide-react';
import { Button } from '@/components/ui/button';

const DomainsSettings: React.FC = () => {
  const { currentStore } = useStore();
  
  // Generate default store URL
  const getDefaultStoreUrl = () => {
    if (!currentStore?.storeUrl) return null;
    return `${currentStore.storeUrl}.m-duka.app`;
  };

  return (
    <div className="space-y-6">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xl font-semibold">Custom Domains</h2>
          {currentStore?.storeUrl && (
            <Button 
              variant="outline" 
              size="sm"
              className="flex items-center gap-1"
              asChild
            >
              <a 
                href={`https://${getDefaultStoreUrl()}`} 
                target="_blank" 
                rel="noopener noreferrer"
              >
                <span>Preview Store</span>
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          )}
        </div>
        <p className="text-muted-foreground">
          Connect your own domain to your store. Custom domains give your store a professional look and make it easier for customers to find you.
        </p>
      </div>
      
      <CustomDomainCard />
      
      <div className="bg-muted p-4 rounded-md border mt-6">
        <h3 className="font-medium mb-2">How custom domains work</h3>
        <p className="text-sm text-muted-foreground mb-2">
          Custom domains allow you to use your own domain for your store instead of using the default store URL.
        </p>
        <ol className="text-sm space-y-2 list-decimal pl-5">
          <li>Purchase a domain from any domain registrar (GoDaddy, Namecheap, etc.)</li>
          <li>Add your domain in the form above</li>
          <li>Create a CNAME record with your domain provider pointing to <code className="bg-background px-1 rounded">stores.m-duka.app</code></li>
          <li>Verify domain ownership using the provided TXT record</li>
          <li>Once verified, your store will be accessible via your custom domain</li>
        </ol>
      </div>
    </div>
  );
};

export default DomainsSettings;
