/**
 * Standardized error handling utilities
 */

import { logger } from './logger';

// Standard error response interface
export interface ErrorResponse {
  success: false;
  error: string;
  code?: string;
  details?: any;
  timestamp: string;
}

// Standard success response interface
export interface SuccessResponse<T = any> {
  success: true;
  data: T;
  timestamp: string;
}

// Union type for API responses
export type ApiResponse<T = any> = SuccessResponse<T> | ErrorResponse;

// Error types
export enum ErrorType {
  VALIDATION = 'VALIDATION_ERROR',
  AUTHENTICATION = 'AUTHENTICATION_ERROR',
  AUTHORIZATION = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND_ERROR',
  NETWORK = 'NETWORK_ERROR',
  DATABASE = 'DATABASE_ERROR',
  UNKNOWN = 'UNKNOWN_ERROR'
}

// Custom error class
export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly code?: string;
  public readonly details?: any;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
  }
}

// Error handler utility
export class ErrorHandler {
  static createError(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN,
    code?: string,
    details?: any
  ): ErrorResponse {
    return {
      success: false,
      error: message,
      code,
      details,
      timestamp: new Date().toISOString()
    };
  }

  static createSuccess<T>(data: T): SuccessResponse<T> {
    return {
      success: true,
      data,
      timestamp: new Date().toISOString()
    };
  }

  static handleError(error: unknown, context?: string): ErrorResponse {
    const timestamp = new Date().toISOString();
    
    if (error instanceof AppError) {
      logger.error(`${context ? `[${context}] ` : ''}AppError: ${error.message}`, {
        type: error.type,
        code: error.code,
        details: error.details
      });
      
      return {
        success: false,
        error: error.message,
        code: error.code,
        details: error.details,
        timestamp
      };
    }

    if (error instanceof Error) {
      logger.error(`${context ? `[${context}] ` : ''}Error: ${error.message}`, {
        stack: error.stack
      });
      
      return {
        success: false,
        error: error.message,
        code: ErrorType.UNKNOWN,
        timestamp
      };
    }

    // Handle string errors
    if (typeof error === 'string') {
      logger.error(`${context ? `[${context}] ` : ''}String error: ${error}`);
      
      return {
        success: false,
        error,
        code: ErrorType.UNKNOWN,
        timestamp
      };
    }

    // Handle unknown error types
    logger.error(`${context ? `[${context}] ` : ''}Unknown error:`, error);
    
    return {
      success: false,
      error: 'An unexpected error occurred',
      code: ErrorType.UNKNOWN,
      details: error,
      timestamp
    };
  }

  // Async wrapper for consistent error handling
  static async wrapAsync<T>(
    fn: () => Promise<T>,
    context?: string
  ): Promise<ApiResponse<T>> {
    try {
      const result = await fn();
      return ErrorHandler.createSuccess(result);
    } catch (error) {
      return ErrorHandler.handleError(error, context);
    }
  }

  // Sync wrapper for consistent error handling
  static wrap<T>(
    fn: () => T,
    context?: string
  ): ApiResponse<T> {
    try {
      const result = fn();
      return ErrorHandler.createSuccess(result);
    } catch (error) {
      return ErrorHandler.handleError(error, context);
    }
  }
}

// Utility functions for common error scenarios
export const createValidationError = (message: string, details?: any) =>
  new AppError(message, ErrorType.VALIDATION, 'VALIDATION_FAILED', details);

export const createAuthError = (message: string = 'Authentication failed') =>
  new AppError(message, ErrorType.AUTHENTICATION, 'AUTH_FAILED');

export const createNotFoundError = (resource: string) =>
  new AppError(`${resource} not found`, ErrorType.NOT_FOUND, 'NOT_FOUND');

export const createNetworkError = (message: string = 'Network request failed') =>
  new AppError(message, ErrorType.NETWORK, 'NETWORK_ERROR');

export const createDatabaseError = (message: string, details?: any) =>
  new AppError(message, ErrorType.DATABASE, 'DATABASE_ERROR', details);

// Type guards
export const isErrorResponse = (response: any): response is ErrorResponse =>
  response && response.success === false;

export const isSuccessResponse = <T>(response: any): response is SuccessResponse<T> =>
  response && response.success === true;

export default ErrorHandler;
