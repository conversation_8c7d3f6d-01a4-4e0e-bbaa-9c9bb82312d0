
import React from 'react';
import { Link } from 'react-router-dom';
import { ShieldAlert } from 'lucide-react';
import NavLink from './NavLink';

interface DesktopNavProps {
  isActive: (path: string) => boolean;
}

const DesktopNav: React.FC<DesktopNavProps> = ({ isActive }) => {
  return (
    <>
      <Link to="/admin" className="flex items-center gap-2 mr-4 md:mr-8">
        <ShieldAlert className="h-6 w-6 text-amber-500" />
        <span className="font-bold text-xl hidden sm:inline-block">Admin Panel</span>
      </Link>

      <nav className="hidden md:flex gap-6 text-sm font-medium">
        <NavLink href="/admin" label="Dashboard" active={isActive('/admin')} />
        <NavLink href="/admin/users" label="Users" active={isActive('/admin/users')} />
        <NavLink href="/admin/stores" label="Stores" active={isActive('/admin/stores')} />
        <NavLink href="/admin/orders" label="Orders" active={isActive('/admin/orders')} />
        <NavLink href="/admin/settings" label="Settings" active={isActive('/admin/settings')} />
      </nav>
    </>
  );
};

export default DesktopNav;
