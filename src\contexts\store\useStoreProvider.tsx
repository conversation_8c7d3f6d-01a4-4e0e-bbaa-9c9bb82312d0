
import React, { useState } from 'react';
import { Store } from '@/types/store';
import { useAuth } from '@/contexts/auth/AuthContext';
import { useStoreLoading } from './hooks/useStoreLoading';
import { useStoreCreate } from './hooks/useStoreCreate';
import { useStoreUpdate } from './hooks/useStoreUpdate';
import { useStoreDelete } from './hooks/useStoreDelete';

export const useStoreProvider = () => {
  const [stores, setStores] = useState<Store[]>([]);
  const [currentStore, setCurrentStore] = useState<Store | null>(null);
  const { user, isAuthenticated } = useAuth();

  // Use separate hooks for loading, creating, updating, and deleting stores
  const { isLoading } = useStoreLoading({ 
    user, 
    isAuthenticated, 
    setStores, 
    setCurrentStore 
  });

  const storeCreate = useStoreCreate();

  const { updateStore } = useStoreUpdate({
    user,
    stores, 
    setStores,
    currentStore
  });

  const { deleteStore } = useStoreDelete({
    user,
    stores,
    setStores,
    currentStore,
    setCurrentStore
  });

  return {
    stores,
    currentStore,
    isLoading,
    createStore: storeCreate.handleSubmit,
    updateStore,
    deleteStore,
    setCurrentStore
  };
};
