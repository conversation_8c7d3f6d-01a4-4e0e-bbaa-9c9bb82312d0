import React, { ReactNode } from "react";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import StarryBackground from "@/components/StarryBackground";

interface AuthLayoutProps {
  children: ReactNode;
  title: string;
  subtitle: string;
}

const AuthLayout: React.FC<AuthLayoutProps> = ({ children, title, subtitle }) => {
  return (
    <div className="relative min-h-screen flex flex-col md:flex-row">
      {/* Starry night background under all content */}
      <StarryBackground />

      {/* Left side - Logo only */}
      <div className="hidden md:flex md:w-1/2 p-10 flex-col justify-center items-center relative z-10 overflow-hidden">
        {/* Professional gradient background */}
        <div
          className="absolute inset-0 -z-10"
          style={{
            background: "linear-gradient(135deg, #221F26 0%, #343857 60%, #403E43 100%)",
          }}
        />
        {/* Soft corner glow overlay */}
        <div
          className="absolute top-0 left-0 w-2/3 h-1/3 pointer-events-none -z-10"
          style={{
            background: "radial-gradient(circle at top left,rgba(114,159,255,0.13) 0%,rgba(58,57,106,0) 75%)",
            filter: "blur(12px)",
          }}
        />
        {/* Subtle white shimmer diagonally */}
        <div
          className="absolute bottom-0 right-0 w-4/6 h-1/3 pointer-events-none -z-10"
          style={{
            background: "linear-gradient(120deg,rgba(255,255,255,0.08) 30%,rgba(65,82,125,0.09) 55%,rgba(52,38,70,0) 90%)",
            filter: "blur(10px)",
          }}
        />

        {/* Logo - Centered vertically and horizontally */}
        <div className="relative z-10 flex flex-col items-center">
          <Link
            to="/"
            className="text-4xl font-extrabold tracking-tight text-green-400 drop-shadow-[0_4px_20px_rgba(34,197,94,0.25)]"
            style={{ fontFamily: 'Poppins, Inter, sans-serif', textShadow: '0 2px 12px rgba(28,255,164,0.14)' }}
            aria-label="Home"
          >
            M-Duka
          </Link>
        </div>
      </div>

      {/* Right side - Auth form with solid white background */}
      <div className="w-full min-h-screen md:w-1/2 flex flex-col justify-center items-center relative z-10 bg-white">
        <div className="w-full max-w-md mx-auto px-4 sm:px-0">
          <div className="md:hidden mb-10 text-center">
            <Link to="/" className="text-3xl font-extrabold tracking-tight text-green-500" style={{fontFamily: 'Poppins, Inter, sans-serif'}} aria-label="Home">
              M-Duka
            </Link>
          </div>
          {/* Card Container */}
          <div className="shadow-xl rounded-2xl px-8 pt-8 pb-6 border border-white/20 bg-white">
            <div className="text-center space-y-2 mb-5">
              <h2 className="text-3xl font-extrabold text-green-500" style={{fontFamily: 'Poppins, sans-serif'}}>{title}</h2>
              <p className="text-base text-gray-700">{subtitle}</p>
            </div>
            <div>
              {children}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;

