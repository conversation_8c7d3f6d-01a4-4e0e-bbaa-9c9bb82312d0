
import { Store } from '@/types/store';
import { Store as DatabaseStore } from '@/types/database';

// Converts database representation to UI representation (camelCase properties)
export const databaseToUIStore = (store: DatabaseStore | null): Store | null => {
  if (!store) return null;
  
  return {
    id: store.id,
    name: store.name,
    description: store.description || '',
    logo: store.logo_url || null,
    logo_url: store.logo_url || null, // Added logo_url to match Store type
    bannerImage: null, // Not available in database schema
    storeUrl: store.store_url || '',
    category: store.category || '',
    paymentMethods: Array.isArray(store.payment_methods) ? store.payment_methods as string[] : [],
    notificationsEmail: '', // Not available directly
    owner_id: store.owner_id,
    created_at: store.created_at,
    updated_at: store.updated_at,
    notifications: store.notifications as any || { email: false, sms: false },
    themeOptions: store.theme as any || { 
      primaryColor: '#ffffff', 
      secondaryColor: '#000000' 
    },
    is_active: store.is_active !== undefined ? store.is_active : true,
    whatsappSettings: store.whatsapp_number ? {
      enabled: true,
      number: store.whatsapp_number,
      message: ''
    } : {
      enabled: false,
      number: '',
      message: ''
    },
    // Map database properties to UI properties for backward compatibility
    store_type: store.store_type || 'physical',
    store_url: store.store_url || '',
    payment_methods: Array.isArray(store.payment_methods) ? store.payment_methods as string[] : [],
    theme_options: store.theme as any || {
      primaryColor: '#ffffff',
      secondaryColor: '#000000'
    },
    whatsapp_settings: store.whatsapp_number ? {
      enabled: true,
      number: store.whatsapp_number,
      message: ''
    } : {
      enabled: false,
      number: '',
      message: ''
    }
  };
};

// Converts UI representation to database representation (snake_case properties)
export const uiToDatabaseStore = (store: Partial<Store>): Partial<DatabaseStore> => {
  const dbStore: Partial<DatabaseStore> = {
    name: store.name,
    description: store.description,
    logo_url: store.logo || store.logo_url, // Use either logo or logo_url
    store_url: store.storeUrl || store.store_url,
    category: store.category,
    payment_methods: (store.paymentMethods || store.payment_methods || []) as any,
    notifications: (store.notifications || { email: false, sms: false }) as any,
    theme: (store.themeOptions || store.theme_options || {
      primaryColor: '#ffffff',
      secondaryColor: '#000000'
    }) as any,
    is_active: store.is_active,
    whatsapp_number: (store.whatsappSettings?.number || store.whatsapp_settings?.number) || null
  };

  return dbStore;
};

// Updated version for partial store updates
export const uiToDatabaseStoreUpdate = (store: Partial<Store>): Partial<DatabaseStore> => {
  return uiToDatabaseStore(store);
};

// Ensure we have a Store object with both camelCase and snake_case properties for backward compatibility
export const ensureUIStore = (store: DatabaseStore | null): Store | null => {
  return databaseToUIStore(store);
};

// Product converters
export const databaseToUIProduct = (product: any): any => {
  if (!product) return null;
  
  return {
    id: product.id,
    name: product.name,
    description: product.description || '',
    price: product.price,
    stock_quantity: product.stock_quantity,
    stockQuantity: product.stock_quantity,
    is_active: product.is_active,
    isActive: product.is_active,
    created_at: product.created_at,
    updated_at: product.updated_at,
    currency: product.currency,
    sku: product.sku,
    images: product.images || [],
    category: product.category,
    tags: product.tags || [],
    store_id: product.store_id,
    storeId: product.store_id,
    // Add any other fields that might be needed
  };
};
