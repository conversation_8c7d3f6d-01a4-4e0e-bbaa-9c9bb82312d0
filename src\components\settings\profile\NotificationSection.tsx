
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Bell } from 'lucide-react';

interface NotificationSectionProps {
  receiveMarketingEmails: boolean;
  setReceiveMarketingEmails: (value: boolean) => void;
  receiveOrderUpdates: boolean;
  setReceiveOrderUpdates: (value: boolean) => void;
}

const NotificationSection: React.FC<NotificationSectionProps> = ({
  receiveMarketingEmails,
  setReceiveMarketingEmails,
  receiveOrderUpdates,
  setReceiveOrderUpdates
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Bell className="h-5 w-5" />
          Notification Preferences
        </CardTitle>
        <CardDescription>
          Manage how and when we notify you
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="marketing-emails" className="text-base">Marketing Emails</Label>
              <p className="text-sm text-muted-foreground">
                Receive emails about new features, promotions, and updates
              </p>
            </div>
            <Switch
              id="marketing-emails"
              checked={receiveMarketingEmails}
              onCheckedChange={setReceiveMarketingEmails}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <div>
              <Label htmlFor="order-updates" className="text-base">Order Updates</Label>
              <p className="text-sm text-muted-foreground">
                Receive notifications about your orders and deliveries
              </p>
            </div>
            <Switch
              id="order-updates"
              checked={receiveOrderUpdates}
              onCheckedChange={setReceiveOrderUpdates}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default NotificationSection;
