
import React from 'react';
import { toast } from 'sonner';
import { useNavigate } from 'react-router-dom';
import ProfileContent from '@/components/settings/profile/ProfileContent';
import { useProfileTabState } from '@/hooks/settings/useProfileTabState';
import { useAuth } from '@/contexts';
import { updateUserProfile, uploadProfileAvatar } from '@/contexts/auth/api/profileApi';

const ProfileTab = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = React.useState(false);
  const [isUploadingAvatar, setIsUploadingAvatar] = React.useState(false);
  
  const profileState = useProfileTabState(user);
  
  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      if (user?.id) {
        // Update user profile with all fields
        await updateUserProfile(user.id, {
          name: profileState.userName,
          phone: profileState.userPhone,
          bio: profileState.userBio
        });
        
        toast.success('Profile updated successfully!');
      }
    } catch (error) {
      toast.error('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleAvatarUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0 || !user?.id) return;
    
    setIsUploadingAvatar(true);
    
    try {
      const result = await uploadProfileAvatar(user.id, files[0]);
      profileState.setAvatarUrl(result.avatarUrl);
      toast.success('Avatar uploaded successfully!');
    } catch (error) {
      toast.error('Failed to upload avatar');
    } finally {
      setIsUploadingAvatar(false);
    }
  };

  const handlePasswordChange = () => {
    // Redirect to the profile page with the password tab pre-selected
    navigate('/profile');
  };

  return (
    <ProfileContent
      userName={profileState.userName}
      setUserName={profileState.setUserName}
      userEmail={user?.email || ''}
      userPhone={profileState.userPhone}
      setUserPhone={profileState.setUserPhone}
      userBio={profileState.userBio}
      setUserBio={profileState.setUserBio}
      receiveMarketingEmails={profileState.receiveMarketingEmails}
      setReceiveMarketingEmails={profileState.setReceiveMarketingEmails}
      receiveOrderUpdates={profileState.receiveOrderUpdates}
      setReceiveOrderUpdates={profileState.setReceiveOrderUpdates}
      twoFactorEnabled={profileState.twoFactorEnabled}
      setTwoFactorEnabled={profileState.setTwoFactorEnabled}
      loading={loading}
      onSubmit={handleProfileSubmit}
      onPasswordChange={handlePasswordChange}
      avatarUrl={profileState.avatarUrl}
      onAvatarUpload={handleAvatarUpload}
      isUploadingAvatar={isUploadingAvatar}
    />
  );
};

export default ProfileTab;
