
import React from "react";
import { ShoppingCart, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const OrderEmptyState: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <div className="flex flex-col items-center justify-center h-full p-6">
      <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
        <ShoppingCart className="h-10 w-10 text-primary" />
      </div>
      <h2 className="text-2xl font-bold mb-2">No Orders Yet</h2>
      <p className="text-muted-foreground text-center mb-6 max-w-md">
        When customers place orders, they will appear here. You can view and manage all your orders from this dashboard.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button 
          variant="outline" 
          onClick={() => navigate('/dashboard')}
          className="flex items-center"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Return to Dashboard
        </Button>
        <Button 
          onClick={() => navigate('/products')}
          className="flex items-center"
        >
          View Products
        </Button>
      </div>
    </div>
  );
};

export default OrderEmptyState;
