
declare const __dirname: string;

// Add any other global declarations here

// Add the Pabbly property for popup-checkout support
interface Window {
  Pabbly?: {
    open(options: { type: string; plan_id: string }): void;
  };
}

// Add global type for VITE_RECAPTCHA_SITE_KEY
interface ImportMetaEnv {
  readonly VITE_RECAPTCHA_SITE_KEY: string;
  // ...other envs...
}

// To use the backend secret, access SUPABASE secrets in your Supabase Edge Functions as process.env.RECAPTCHA_SECRET_KEY
