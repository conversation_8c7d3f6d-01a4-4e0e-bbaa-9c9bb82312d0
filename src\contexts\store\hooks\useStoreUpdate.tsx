
import { useState } from 'react';
import { Store, StoreFormData } from '@/types/store';
import { updateStoreInDb } from '../api/updateStore';

type UseStoreUpdateProps = {
  user: any;
  stores: Store[];
  setStores: React.Dispatch<React.SetStateAction<Store[]>>;
  currentStore: Store | null;
};

export const useStoreUpdate = ({
  user,
  stores,
  setStores,
  currentStore
}: UseStoreUpdateProps) => {
  const [isUpdating, setIsUpdating] = useState<boolean>(false);

  const updateStore = async (storeId: string, storeData: Partial<StoreFormData>): Promise<Store> => {
    if (!user) throw new Error('You must be logged in to update a store');
    
    setIsUpdating(true);
    try {
      const updatedStore = await updateStoreInDb(storeId, storeData, user.id);
      
      // Update state
      const updatedStores = stores.map(store => 
        store.id === storeId ? updatedStore : store
      );
      
      setStores(updatedStores);
      
      return updatedStore;
    } finally {
      setIsUpdating(false);
    }
  };

  return { updateStore, isUpdating };
};
