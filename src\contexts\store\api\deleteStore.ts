
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { MOCK_STORES_FOR_DEVELOPMENT } from '../mockData';
import { deleteMockStore } from '../utils/mockStoreOperations';

export const deleteStoreFromDb = async (storeId: string, userId: string): Promise<void> => {
  // For development, delete a mock store
  if (MOCK_STORES_FOR_DEVELOPMENT) {
    deleteMockStore(storeId);
    return;
  }

  try {
    const { error } = await supabase
      .from('stores')
      .delete()
      .eq('id', storeId)
      .eq('owner_id', userId);

    if (error) throw error;
    
    toast.success('Store deleted successfully');
  } catch (error: any) {
    console.error('Error deleting store:', error);
    toast.error(error.message || 'Failed to delete store');
    throw error;
  }
};
