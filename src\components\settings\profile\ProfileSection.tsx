
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON><PERSON>er, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { User2, Loader2, Upload } from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface ProfileSectionProps {
  userName: string;
  setUserName: (value: string) => void;
  userEmail: string;
  userPhone: string;
  setUserPhone: (value: string) => void;
  userBio: string;
  setUserBio: (value: string) => void;
  loading: boolean;
  onSubmit: (e: React.FormEvent) => Promise<void>;
  avatarUrl: string;
  onAvatarUpload: (e: React.ChangeEvent<HTMLInputElement>) => Promise<void>;
  isUploadingAvatar: boolean;
}

const ProfileSection: React.FC<ProfileSectionProps> = ({
  userName,
  setUserName,
  userEmail,
  userPhone,
  setUserPhone,
  userBio,
  setUserBio,
  loading,
  onSubmit,
  avatarUrl,
  onAvatarUpload,
  isUploadingAvatar
}) => {
  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User2 className="h-5 w-5" />
          Profile Information
        </CardTitle>
        <CardDescription>
          Update your personal information and how we can contact you
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={onSubmit} className="space-y-6">
          <div className="flex flex-col md:flex-row md:items-start gap-8">
            {/* Avatar Section */}
            <div className="flex flex-col items-center">
              <Avatar className="h-24 w-24 border-2 border-primary/20">
                <AvatarImage src={avatarUrl} alt={userName || "User"} />
                <AvatarFallback className="text-lg bg-primary/10 text-primary">
                  {getInitials(userName || "User")}
                </AvatarFallback>
              </Avatar>
              <div className="relative mt-4">
                <input
                  type="file"
                  id="avatar-upload"
                  className="hidden"
                  accept="image/*"
                  onChange={onAvatarUpload}
                  disabled={isUploadingAvatar}
                />
                <label 
                  htmlFor="avatar-upload" 
                  className="inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-md bg-secondary text-secondary-foreground hover:bg-secondary/80 cursor-pointer"
                >
                  {isUploadingAvatar ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Uploading...
                    </>
                  ) : (
                    <>
                      <Upload className="h-3 w-3 mr-1" /> Change Photo
                    </>
                  )}
                </label>
              </div>
            </div>

            {/* Profile Form */}
            <div className="flex-1 space-y-4">
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={userName}
                    onChange={(e) => setUserName(e.target.value)}
                    placeholder="Enter your full name"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={userEmail}
                    placeholder="Your email address"
                    disabled
                  />
                  <p className="text-xs text-muted-foreground">
                    Your email address cannot be changed
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={userPhone}
                    onChange={(e) => setUserPhone(e.target.value)}
                    placeholder="Enter your phone number"
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={userBio}
                  onChange={(e) => setUserBio(e.target.value)}
                  placeholder="Tell us a little about yourself"
                  rows={3}
                />
                <p className="text-xs text-muted-foreground">
                  Brief description for your profile. This will be visible to other users.
                </p>
              </div>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex justify-end">
            <Button type="submit" disabled={loading}>
              {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
              {loading ? 'Saving...' : 'Save Profile Information'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProfileSection;
