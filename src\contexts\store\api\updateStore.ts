
import { supabase } from '@/integrations/supabase/client';
import { Store, StoreFormData } from '@/types/store';
import { Json } from '@/integrations/supabase/types';
import { toast } from 'sonner';
import { MOCK_STORES_FOR_DEVELOPMENT } from '../mockData';
import { formatStoreFromDb } from '../utils/formatters';
import { updateMockStore } from '../utils/mockStoreOperations';

export const updateStoreInDb = async (storeId: string, storeData: Partial<StoreFormData>, userId: string): Promise<Store> => {
  // For development, update a mock store
  if (MOCK_STORES_FOR_DEVELOPMENT) {
    return updateMockStore(storeId, storeData);
  }

  try {
    // Convert the partial store data to DB format
    // We need to convert the partial StoreFormData to a partial DB format
    const partialDbData: Record<string, any> = {};
    
    // Helper function for safe JSON conversion
    const safeJsonify = (obj: any): Json => {
      if (obj === null || obj === undefined) return null;
      if (Array.isArray(obj)) return obj.map(item => safeJsonify(item)) as Json[];
      if (typeof obj === 'object' && obj !== null) {
        const result: Record<string, Json> = {};
        for (const key in obj) {
          if (Object.prototype.hasOwnProperty.call(obj, key)) {
            result[key] = safeJsonify(obj[key]);
          }
        }
        return result;
      }
      return obj as Json;
    };
    
    // Basic store properties
    if (storeData.name !== undefined) partialDbData.name = storeData.name;
    if (storeData.description !== undefined) partialDbData.description = storeData.description;
    if (storeData.storeUrl !== undefined) partialDbData.store_url = storeData.storeUrl;
    if (storeData.category !== undefined) partialDbData.category = storeData.category;
    if (storeData.themeId !== undefined) partialDbData.theme_id = storeData.themeId;
    if (storeData.storeType !== undefined) partialDbData.store_type = storeData.storeType;
    if (storeData.paymentMethods !== undefined) partialDbData.payment_methods = storeData.paymentMethods;
    if (storeData.notificationsEmail !== undefined) partialDbData.notifications_email = storeData.notificationsEmail;
    
    // Logo and banner image
    if (storeData.logo !== undefined) partialDbData.logo = storeData.logo;
    if (storeData.bannerImage !== undefined) partialDbData.banner_image = storeData.bannerImage;
    
    // JSON objects - need to ensure they are properly formatted for Supabase
    if (storeData.notifications !== undefined) partialDbData.notifications = safeJsonify(storeData.notifications);
    if (storeData.themeOptions !== undefined) partialDbData.theme_options = safeJsonify(storeData.themeOptions);
    if (storeData.whatsappSettings !== undefined) partialDbData.whatsapp_settings = safeJsonify(storeData.whatsappSettings);
    if (storeData.workflowSettings !== undefined) partialDbData.workflow_settings = safeJsonify(storeData.workflowSettings);
    
    console.log('Updating store with data:', partialDbData);
    
    const { data, error } = await supabase
      .from('stores')
      .update(partialDbData)
      .eq('id', storeId)
      .eq('owner_id', userId)
      .select('*')
      .single();

    if (error) throw error;

    toast.success('Store updated successfully');
    return formatStoreFromDb(data);
  } catch (error: any) {
    console.error('Error updating store:', error);
    toast.error(error.message || 'Failed to update store');
    throw error;
  }
};
