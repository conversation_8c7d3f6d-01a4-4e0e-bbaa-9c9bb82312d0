
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ShoppingCart, Heart } from 'lucide-react';
import { Product } from '@/types/unified-product';
import { formatCurrency } from '@/utils/formatters';
import { Button } from '@/components/ui/button';
import { useCart } from '@/contexts/cart/CartContext';
import { useWishlist } from '@/contexts';
import { toast } from 'sonner';

interface ProductCardProps {
  product: Product;
  onDelete?: (id: string) => Promise<void>;
}

const ProductCard: React.FC<ProductCardProps> = ({ product }) => {
  const navigate = useNavigate();
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist();
  const productInWishlist = product.id ? isInWishlist(product.id) : false;
  
  const handleViewProduct = () => {
    navigate(`/shop/product/${product.id}`);
  };
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.stopPropagation();
    addToCart(product, 1);
    toast.success(`Added ${product.name} to cart`);
  };
  
  const handleToggleWishlist = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (productInWishlist && product.id) {
      removeFromWishlist(product.id);
      toast.success(`Removed ${product.name} from wishlist`);
    } else {
      addToWishlist(product);
      toast.success(`Added ${product.name} to wishlist`);
    }
  };

  return (
    <div 
      className="bg-white rounded-lg shadow-sm border border-gray-100 h-full flex flex-col overflow-hidden hover:shadow-md transition-shadow cursor-pointer"
      onClick={handleViewProduct}
    >
      {/* Product Image */}
      <div className="relative h-36 bg-gray-50 flex items-center justify-center overflow-hidden">
        {product.images && product.images.length > 0 ? (
          <img 
            src={product.images[0]} 
            alt={product.name}
            className="h-full w-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-gray-100 flex items-center justify-center text-gray-400">
            No image
          </div>
        )}
        
        {/* Sale badge if needed */}
        {product.tags?.includes('sale') && (
          <span className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full">
            Sale
          </span>
        )}
        
        {/* Stock indicator */}
        {product.stock_quantity !== undefined && product.stock_quantity <= 0 && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <span className="bg-red-500 text-white px-2 py-1 rounded-sm text-xs font-semibold">
              Out of Stock
            </span>
          </div>
        )}
      </div>
      
      {/* Product Info */}
      <div className="p-3 flex-grow flex flex-col">
        <div className="flex-grow">
          <h3 className="font-medium text-sm line-clamp-2">{product.name}</h3>
          <p className="text-green-600 font-bold mt-1">
            {formatCurrency(product.price, product.currency)}
          </p>
          {product.stock_quantity !== undefined && product.stock_quantity > 0 && product.stock_quantity < 5 && (
            <p className="text-red-500 text-xs mt-1">Only {product.stock_quantity} left</p>
          )}
        </div>
        
        {/* Action Buttons */}
        <div className="mt-3 flex gap-1">
          <Button 
            onClick={handleAddToCart} 
            size="sm" 
            className="flex-grow text-xs"
            disabled={product.stock_quantity !== undefined && product.stock_quantity <= 0}
          >
            <ShoppingCart className="h-3.5 w-3.5 mr-1" />
            Add to Cart
          </Button>
          <Button 
            onClick={handleToggleWishlist} 
            size="sm" 
            variant={productInWishlist ? "default" : "outline"}
            className="p-1"
          >
            <Heart className={`h-4 w-4 ${productInWishlist ? 'fill-current' : ''}`} />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProductCard;
