import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { toast } from 'sonner';
import { User, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>C<PERSON>cle, Wrench } from 'lucide-react';
import { createMissingProfile, fixAllMissingProfiles, createProfileSimple } from '@/utils/fixUserProfile';
import { supabase } from '@/integrations/supabase/client';

const ProfileFixPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);

  React.useEffect(() => {
    // Get current user info
    const getCurrentUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
      if (user?.email) {
        setEmail(user.email);
        setName(user.user_metadata?.name || user.email.split('@')[0]);
      }
    };
    getCurrentUser();
  }, []);

  const handleCreateProfile = async () => {
    if (!email || !name) {
      toast.error('Please enter both email and name');
      return;
    }

    setIsLoading(true);
    try {
      const result = await createProfileSimple(email, name, 'store_owner');
      
      if (result.success) {
        toast.success('Profile created successfully! Try logging in now.');
        setTimeout(() => {
          window.location.href = '/signin';
        }, 2000);
      } else {
        toast.error(result.error || 'Failed to create profile');
      }
    } catch (error) {
      toast.error('An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFixMissingProfile = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    try {
      const result = await createMissingProfile(email);
      
      if (result.success) {
        toast.success('Profile fixed successfully! Try logging in now.');
        setTimeout(() => {
          window.location.href = '/signin';
        }, 2000);
      } else {
        toast.error(result.error || 'Failed to fix profile');
      }
    } catch (error) {
      toast.error('An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleFixAllProfiles = async () => {
    setIsLoading(true);
    try {
      const result = await fixAllMissingProfiles();
      
      if (result.success) {
        toast.success(`Fixed profiles for ${result.created || 0} users!`);
      } else {
        toast.error(result.error || 'Failed to fix profiles');
      }
    } catch (error) {
      toast.error('An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleTestLogin = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      // Check if profile exists
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('email', email)
        .single();

      if (profile) {
        toast.success('Profile found! You should be able to login now.');
        console.log('Profile data:', profile);
      } else {
        toast.error('Profile not found. Use the fix buttons below.');
        console.log('Profile check error:', error);
      }
    } catch (error) {
      toast.error('Error checking profile');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-2xl space-y-6">
        
        {/* Problem Explanation */}
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              Profile Missing Issue
            </CardTitle>
            <CardDescription className="text-red-700">
              Your account exists in Supabase Auth but there's no profile record in the database.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3 text-sm text-red-700">
            <div>
              <strong>What happened:</strong>
              <ul className="list-disc list-inside ml-4 mt-1">
                <li>User account was created in Supabase Auth ✅</li>
                <li>Profile record was not created in profiles table ❌</li>
                <li>Login fails because app expects both to exist</li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* Current User Info */}
        {currentUser && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <User className="h-5 w-5" />
                Current User Info
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-blue-700">
              <div><strong>Email:</strong> {currentUser.email}</div>
              <div><strong>User ID:</strong> {currentUser.id}</div>
              <div><strong>Created:</strong> {new Date(currentUser.created_at).toLocaleString()}</div>
            </CardContent>
          </Card>
        )}

        {/* Fix Tools */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Profile Fix Tools
            </CardTitle>
            <CardDescription>
              Use these tools to create the missing profile record
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            
            <div className="space-y-2">
              <label htmlFor="fix-email" className="text-sm font-medium">
                Email Address
              </label>
              <Input
                id="fix-email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="fix-name" className="text-sm font-medium">
                Full Name
              </label>
              <Input
                id="fix-name"
                type="text"
                placeholder="Your Full Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button 
                onClick={handleTestLogin}
                variant="outline"
                disabled={isLoading}
              >
                Test Profile Status
              </Button>
              
              <Button 
                onClick={handleCreateProfile}
                disabled={isLoading}
                className="bg-green-600 hover:bg-green-700"
              >
                {isLoading ? 'Creating...' : 'Create Profile (Simple)'}
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <Button 
                onClick={handleFixMissingProfile}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? 'Fixing...' : 'Fix Missing Profile (Admin)'}
              </Button>
              
              <Button 
                onClick={handleFixAllProfiles}
                disabled={isLoading}
                variant="outline"
              >
                {isLoading ? 'Fixing...' : 'Fix All Missing Profiles'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Success Instructions */}
        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              After Profile is Created
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm text-green-700">
            <div>1. <strong>Try logging in</strong> with your email and password</div>
            <div>2. <strong>You should reach the dashboard</strong> without "User profile not found" error</div>
            <div>3. <strong>If it still doesn't work</strong>, check the browser console for errors</div>
          </CardContent>
        </Card>

        <div className="text-center space-x-4">
          <Button 
            onClick={() => window.location.href = '/signin'}
            variant="outline"
          >
            ← Back to Sign In
          </Button>
          
          <Button 
            onClick={() => window.location.href = '/signup'}
            variant="outline"
          >
            Try Signup Again
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ProfileFixPage;
