import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Grid3X3, 
  List, 
  Maximize2, 
  ChevronLeft, 
  ChevronRight,
  Home,
  ShoppingBag,
  Settings,
  Users,
  Shield,
  Store,
  X
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface PageInfo {
  path: string;
  name: string;
  category: 'public' | 'store-owner' | 'admin' | 'storefront' | 'customer';
  requiresAuth: boolean;
  role?: string;
}

const allPages: PageInfo[] = [
  // Public Pages
  { path: '/', name: 'Home/Landing', category: 'public', requiresAuth: false },
  { path: '/signin', name: 'Sign In', category: 'public', requiresAuth: false },
  { path: '/signup', name: 'Sign Up', category: 'public', requiresAuth: false },
  { path: '/forgot-password', name: 'Forgot Password', category: 'public', requiresAuth: false },
  { path: '/reset-password', name: 'Reset Password', category: 'public', requiresAuth: false },
  { path: '/confirm-signup', name: 'Confirm Sign Up', category: 'public', requiresAuth: false },
  { path: '/pricing', name: 'Pricing', category: 'public', requiresAuth: false },
  { path: '/terms', name: 'Terms of Service', category: 'public', requiresAuth: false },
  { path: '/privacy', name: 'Privacy Policy', category: 'public', requiresAuth: false },
  { path: '/cookies', name: 'Cookie Policy', category: 'public', requiresAuth: false },
  
  // Store Owner Pages
  { path: '/dashboard', name: 'Dashboard', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/products', name: 'Products', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/products/create', name: 'Create Product', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/products/categories', name: 'Categories', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/products/discounts', name: 'Discounts', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/products/featured', name: 'Featured Products', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/orders', name: 'Orders', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/customers', name: 'Customers', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/marketing', name: 'Marketing', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/reports', name: 'Reports', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/payments', name: 'Payments', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/profile', name: 'Profile', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/create-store', name: 'Create Store', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  
  // Settings Pages
  { path: '/settings', name: 'Settings Main', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/general', name: 'General Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/billing', name: 'Billing Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/payments', name: 'Payment Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/delivery', name: 'Delivery Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/domains', name: 'Domains Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/whatsapp', name: 'WhatsApp Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/workflow', name: 'Workflow Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/staff', name: 'Staff Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/checkout', name: 'Checkout Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/membership', name: 'Membership Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/seo', name: 'SEO Settings', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  { path: '/settings/plans', name: 'Plans', category: 'store-owner', requiresAuth: true, role: 'StoreOwner' },
  
  // Admin Pages
  { path: '/admin', name: 'Admin Dashboard', category: 'admin', requiresAuth: true, role: 'Admin' },
  { path: '/admin/users', name: 'Admin Users', category: 'admin', requiresAuth: true, role: 'Admin' },
  { path: '/admin/stores', name: 'Admin Stores', category: 'admin', requiresAuth: true, role: 'Admin' },
  { path: '/admin/orders', name: 'Admin Orders', category: 'admin', requiresAuth: true, role: 'Admin' },
  { path: '/admin/settings', name: 'Admin Settings', category: 'admin', requiresAuth: true, role: 'Admin' },
  
  // Storefront Pages
  { path: '/shop', name: 'Shop Home', category: 'storefront', requiresAuth: false },
  { path: '/shop/products', name: 'Product Listing', category: 'storefront', requiresAuth: false },
  { path: '/shop/featured', name: 'Featured Products', category: 'storefront', requiresAuth: false },
  { path: '/shop/categories', name: 'Categories', category: 'storefront', requiresAuth: false },
  { path: '/shop/cart', name: 'Shopping Cart', category: 'storefront', requiresAuth: false },
  { path: '/shop/checkout', name: 'Checkout', category: 'storefront', requiresAuth: false },
  
  // Customer Account Pages
  { path: '/shop/account', name: 'Account Dashboard', category: 'customer', requiresAuth: true, role: 'Customer' },
  { path: '/shop/account/wishlist', name: 'Wishlist', category: 'customer', requiresAuth: true, role: 'Customer' },
  { path: '/shop/account/profile', name: 'Account Profile', category: 'customer', requiresAuth: true, role: 'Customer' },
  { path: '/shop/account/settings', name: 'Account Settings', category: 'customer', requiresAuth: true, role: 'Customer' },
  { path: '/shop/account/orders', name: 'Order History', category: 'customer', requiresAuth: true, role: 'Customer' },
  { path: '/shop/account/addresses', name: 'Addresses', category: 'customer', requiresAuth: true, role: 'Customer' },
];

const DevPageViewer: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedPage, setSelectedPage] = useState<PageInfo | null>(null);
  const [currentCategory, setCurrentCategory] = useState<string>('all');

  const filteredPages = currentCategory === 'all' 
    ? allPages 
    : allPages.filter(page => page.category === currentCategory);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'public': return <Home className="h-4 w-4" />;
      case 'store-owner': return <Store className="h-4 w-4" />;
      case 'admin': return <Shield className="h-4 w-4" />;
      case 'storefront': return <ShoppingBag className="h-4 w-4" />;
      case 'customer': return <Users className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'public': return 'bg-blue-100 text-blue-800';
      case 'store-owner': return 'bg-green-100 text-green-800';
      case 'admin': return 'bg-red-100 text-red-800';
      case 'storefront': return 'bg-purple-100 text-purple-800';
      case 'customer': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold">Development Page Viewer</h1>
              <p className="text-sm text-gray-600">Browse and test all application pages</p>
            </div>
            <div className="flex items-center gap-4">
              <Badge variant="outline" className="bg-amber-50">
                Auth Bypass: ON
              </Badge>
              <div className="flex gap-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Tabs */}
      <div className="container mx-auto px-4 py-6">
        <Tabs value={currentCategory} onValueChange={setCurrentCategory}>
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="all">All Pages</TabsTrigger>
            <TabsTrigger value="public">Public</TabsTrigger>
            <TabsTrigger value="store-owner">Store Owner</TabsTrigger>
            <TabsTrigger value="admin">Admin</TabsTrigger>
            <TabsTrigger value="storefront">Storefront</TabsTrigger>
            <TabsTrigger value="customer">Customer</TabsTrigger>
          </TabsList>

          <TabsContent value={currentCategory} className="mt-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                {filteredPages.map((page) => (
                  <Card 
                    key={page.path} 
                    className="hover:shadow-lg transition-shadow cursor-pointer"
                    onClick={() => setSelectedPage(page)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="text-base">{page.name}</CardTitle>
                        <Badge className={getCategoryColor(page.category)}>
                          {getCategoryIcon(page.category)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-gray-600 font-mono">{page.path}</p>
                        <div className="flex items-center gap-2">
                          {page.requiresAuth && (
                            <Badge variant="outline" className="text-xs">
                              Auth Required
                            </Badge>
                          )}
                          {page.role && (
                            <Badge variant="secondary" className="text-xs">
                              {page.role}
                            </Badge>
                          )}
                        </div>
                        <div className="flex gap-2 mt-3">
                          <Link to={page.path} target="_blank">
                            <Button size="sm" variant="outline">
                              Open in New Tab
                            </Button>
                          </Link>
                          <Link to={page.path}>
                            <Button size="sm">
                              Navigate
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg shadow">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b">
                    <tr>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Page Name</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Path</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Category</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Auth</th>
                      <th className="px-4 py-3 text-left text-sm font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {filteredPages.map((page) => (
                      <tr key={page.path} className="hover:bg-gray-50">
                        <td className="px-4 py-3 text-sm font-medium">{page.name}</td>
                        <td className="px-4 py-3 text-sm font-mono text-gray-600">{page.path}</td>
                        <td className="px-4 py-3">
                          <Badge className={getCategoryColor(page.category)}>
                            <span className="flex items-center gap-1">
                              {getCategoryIcon(page.category)}
                              {page.category}
                            </span>
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-sm">
                          {page.requiresAuth ? (
                            <Badge variant="outline">{page.role || 'Required'}</Badge>
                          ) : (
                            <span className="text-gray-400">None</span>
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex gap-2">
                            <Link to={page.path}>
                              <Button size="sm" variant="outline">View</Button>
                            </Link>
                            <Link to={page.path} target="_blank">
                              <Button size="sm" variant="outline">
                                <Maximize2 className="h-3 w-3" />
                              </Button>
                            </Link>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Page Preview Modal */}
      {selectedPage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg w-full max-w-6xl h-[90vh] flex flex-col">
            <div className="flex items-center justify-between p-4 border-b">
              <div>
                <h2 className="text-lg font-semibold">{selectedPage.name}</h2>
                <p className="text-sm text-gray-600">{selectedPage.path}</p>
              </div>
              <div className="flex items-center gap-2">
                <Link to={selectedPage.path} target="_blank">
                  <Button size="sm" variant="outline">
                    Open in New Tab
                  </Button>
                </Link>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setSelectedPage(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="flex-1 overflow-hidden">
              <iframe
                src={selectedPage.path}
                className="w-full h-full border-0"
                title={selectedPage.name}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DevPageViewer; 