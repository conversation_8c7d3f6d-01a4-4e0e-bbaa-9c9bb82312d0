
import React from "react";
import { Link } from "react-router-dom";
import { Badge } from "@/components/ui/badge";

interface SidebarNavItemProps {
  item: {
    name: string;
    path: string;
    icon: React.ReactNode;
    soon?: boolean;
    isExternal?: boolean;
  };
  isActive: boolean;
}

const SidebarNavItem = ({ item, isActive }: SidebarNavItemProps) => {
  // Update the list of implemented paths
  const implementedPaths = [
    '/dashboard', 
    '/products', 
    '/orders', 
    '/customers', 
    '/profile',
    '/settings', 
    '/settings/general',
    '/settings/billing', 
    '/settings/payments', 
    '/settings/delivery', 
    '/settings/domains',
    '/payments',
    '/shop'
  ];
  
  // External links are always considered implemented
  const isImplemented = item.isExternal || implementedPaths.includes(item.path);
  
  if (!isImplemented) {
    return (
      <div
        className="flex items-center px-4 py-2 rounded-md text-gray-400 cursor-not-allowed"
        title="Coming soon"
      >
        {item.icon}
        {item.name}
        <Badge variant="outline" className="ml-2">Soon</Badge>
      </div>
    );
  }
  
  // For external links
  if (item.isExternal) {
    return (
      <a 
        href={item.path}
        target="_blank"
        rel="noopener noreferrer"
        className="flex items-center px-4 py-2 rounded-md text-gray-600 hover:bg-gray-100 transition-colors"
      >
        {item.icon}
        {item.name}
      </a>
    );
  }
  
  // For internal links, always use React Router <Link to>
  return (
    <Link 
      to={item.path}
      className={`flex items-center px-4 py-2 rounded-md transition-colors ${
        isActive
          ? "bg-primary/10 text-primary"
          : "text-gray-600 hover:bg-gray-100"
      }`}
      // Prevent full page reloads and focus for SPA
      tabIndex={0}
      draggable={false}
    >
      {item.icon}
      {item.name}
    </Link>
  );
};

export default SidebarNavItem;

