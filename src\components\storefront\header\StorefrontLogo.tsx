
import React from "react";
import { Link } from "react-router-dom";

const StorefrontLogo: React.FC = () => {
  return (
    <Link to="/shop" className="text-2xl font-bold flex items-center">
      <div className="relative">
        <div className="bg-green-500 rounded-xl text-white mr-1.5 px-3 py-2 inline-block shadow-sm text-xl">
          M
          {/* SMS-style tail */}
          <div className="absolute -bottom-[6px] left-1/2 transform -translate-x-1/2 w-4 h-4 bg-green-500 rotate-45 rounded-sm"></div>
        </div>
      </div>
      <span>-Duka Shop</span>
    </Link>
  );
};

export default StorefrontLogo;
