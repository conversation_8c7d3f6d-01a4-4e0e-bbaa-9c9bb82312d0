import { UserRole } from '@/constants/roles';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Dashboard from '@/pages/Dashboard';
import Settings from '@/pages/Settings';
import Products from '@/pages/Products';
import StoreOrders from '@/pages/Orders';
import Profile from '@/pages/Profile';
import CreateStore from '@/pages/CreateStore';
import Payments from '@/pages/Payments';
import Customers from '@/pages/Customers';
import CustomerDetails from '@/pages/CustomerDetails';
import CategoryManagement from '@/pages/CategoryManagement';
import DiscountManagement from '@/pages/DiscountManagement';
import FeaturedProducts from '@/pages/FeaturedProducts';
import Marketing from '@/pages/Marketing';
import Reports from '@/pages/Reports';
import WhatsAppSettings from '@/pages/settings/WhatsAppSettings';
import WorkflowSettings from '@/pages/settings/WorkflowSettings';
import StaffSettings from '@/pages/settings/StaffSettings';
import CheckoutSettings from '@/pages/settings/CheckoutSettings';
import MembershipSettings from '@/pages/settings/MembershipSettings';
import SeoSettings from '@/pages/settings/SeoSettings';
import DetailsSettings from '@/pages/settings/DetailsSettings';
import FilesSettings from '@/pages/settings/FilesSettings';
import IntegrationsSettings from '@/pages/settings/IntegrationsSettings';
import AffiliateSettings from '@/pages/settings/AffiliateSettings';
import DemoStore from '@/pages/DemoStore';

export const storeOwnerRoutes = [
  {
    path: "/dashboard",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Dashboard /></ProtectedRoute>,
  },
  {
    path: "/store-owner/dashboard",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Dashboard /></ProtectedRoute>,
  },
  {
    path: "/demo-store",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><DemoStore /></ProtectedRoute>,
  },
  {
    path: "/settings/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Settings /></ProtectedRoute>,
  },
  {
    path: "/store-owner/settings/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Settings /></ProtectedRoute>,
  },
  {
    path: "/products/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Products /></ProtectedRoute>,
  },
  {
    path: "/store-owner/products/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Products /></ProtectedRoute>,
  },
  {
    path: "/products/categories",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CategoryManagement /></ProtectedRoute>,
  },
  {
    path: "/store-owner/products/categories",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CategoryManagement /></ProtectedRoute>,
  },
  {
    path: "/products/discounts",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><DiscountManagement /></ProtectedRoute>,
  },
  {
    path: "/store-owner/products/discounts",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><DiscountManagement /></ProtectedRoute>,
  },
  {
    path: "/products/featured",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><FeaturedProducts /></ProtectedRoute>,
  },
  {
    path: "/store-owner/products/featured",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><FeaturedProducts /></ProtectedRoute>,
  },
  {
    path: "/orders",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><StoreOrders /></ProtectedRoute>,
  },
  {
    path: "/store-owner/orders",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><StoreOrders /></ProtectedRoute>,
  },
  {
    path: "/profile",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner, UserRole.Admin]}><Profile /></ProtectedRoute>,
  },
  {
    path: "/store-owner/profile",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner, UserRole.Admin]}><Profile /></ProtectedRoute>,
  },
  {
    path: "/create-store",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CreateStore /></ProtectedRoute>,
  },
  {
    path: "/store-owner/create-store",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CreateStore /></ProtectedRoute>,
  },
  {
    path: "/payments",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Payments /></ProtectedRoute>,
  },
  {
    path: "/store-owner/payments",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Payments /></ProtectedRoute>,
  },
  {
    path: "/customers",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Customers /></ProtectedRoute>,
  },
  {
    path: "/store-owner/customers",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Customers /></ProtectedRoute>,
  },
  {
    path: "/customers/:id",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CustomerDetails /></ProtectedRoute>,
  },
  {
    path: "/store-owner/customers/:id",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CustomerDetails /></ProtectedRoute>,
  },
  {
    path: "/marketing/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Marketing /></ProtectedRoute>,
  },
  {
    path: "/store-owner/marketing/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Marketing /></ProtectedRoute>,
  },
  {
    path: "/reports/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Reports /></ProtectedRoute>,
  },
  {
    path: "/store-owner/reports/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><Reports /></ProtectedRoute>,
  },
  {
    path: "/settings/whatsapp",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><WhatsAppSettings /></ProtectedRoute>,
  },
  {
    path: "/store-owner/settings/whatsapp",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><WhatsAppSettings /></ProtectedRoute>,
  },
  {
    path: "/settings/workflow/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><WorkflowSettings /></ProtectedRoute>,
  },
  {
    path: "/store-owner/settings/workflow/*",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><WorkflowSettings /></ProtectedRoute>,
  },
  {
    path: "/settings/staff",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><StaffSettings /></ProtectedRoute>,
  },
  {
    path: "/store-owner/settings/staff",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><StaffSettings /></ProtectedRoute>,
  },
  {
    path: "/settings/checkout",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CheckoutSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/checkout",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><CheckoutSettings /></ProtectedRoute>
  },
  {
    path: "/settings/membership",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><MembershipSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/membership",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><MembershipSettings /></ProtectedRoute>
  },
  {
    path: "/settings/seo",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><SeoSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/seo",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><SeoSettings /></ProtectedRoute>
  },
  {
    path: "/settings/details",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><DetailsSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/details",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><DetailsSettings /></ProtectedRoute>
  },
  {
    path: "/settings/files",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><FilesSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/files",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><FilesSettings /></ProtectedRoute>
  },
  {
    path: "/settings/integrations",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><IntegrationsSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/integrations",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><IntegrationsSettings /></ProtectedRoute>
  },
  {
    path: "/settings/affiliate",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><AffiliateSettings /></ProtectedRoute>
  },
  {
    path: "/store-owner/settings/affiliate",
    element: <ProtectedRoute requiredRoles={[UserRole.StoreOwner]}><AffiliateSettings /></ProtectedRoute>
  },
];
