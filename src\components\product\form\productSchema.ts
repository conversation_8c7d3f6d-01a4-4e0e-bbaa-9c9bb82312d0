
import * as z from 'zod';

// Validation schema with improved validation
export const productSchema = z.object({
  name: z.string().min(3, 'Product name must be at least 3 characters').max(100, 'Product name cannot exceed 100 characters'),
  description: z.string().max(1000, 'Description cannot exceed 1000 characters').optional(),
  price: z.coerce.number()
    .positive('Price must be a positive number')
    .min(0.01, 'Price must be at least 0.01')
    .max(9999999.99, 'Price is too high'),
  currency: z.string().min(1, 'Currency is required'),
  sku: z.string().max(50, 'SKU cannot exceed 50 characters').optional(),
  stock_quantity: z.coerce.number()
    .int('Stock quantity must be a whole number')
    .nonnegative('Stock quantity must be a non-negative integer')
    .max(999999, 'Stock quantity is too high')
    .default(0),
  category: z.string().optional(),
  is_active: z.boolean().default(true),
  tags: z.array(z.string()).max(10, 'Maximum 10 tags allowed').optional(),
  images: z.array(z.string()).max(5, 'Maximum 5 images allowed').optional(),
  has_variants: z.boolean().default(false),
  variants: z.array(
    z.object({
      size: z.string().optional(),
      color: z.string().optional(),
      price: z.number().positive('Variant price must be positive').optional(),
      stock_quantity: z.number().nonnegative('Stock quantity must be non-negative').optional(),
      sku: z.string().optional(),
      is_active: z.boolean().default(true)
    })
  ).optional()
});

export type ProductFormValues = z.infer<typeof productSchema>;
