
import React from "react";
import { Separator } from "@/components/ui/separator";

interface DividerWithTextProps {
  text: string;
  className?: string;
}

const DividerWithText: React.FC<DividerWithTextProps> = ({ text, className = "my-6" }) => {
  return (
    <div className={`relative ${className}`}>
      <div className="absolute inset-0 flex items-center">
        <Separator className="w-full" />
      </div>
      <div className="relative flex justify-center">
        <span className="bg-background px-2 text-sm text-muted-foreground">
          {text}
        </span>
      </div>
    </div>
  );
};

export default DividerWithText;
