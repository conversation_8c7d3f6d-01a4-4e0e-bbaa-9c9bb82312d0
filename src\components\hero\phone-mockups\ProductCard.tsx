
import { motion } from "framer-motion";
import { ShoppingBag } from "lucide-react";

interface ProductCardProps {
  name: string;
  price: string;
  image: string;
  emoji: string;
  index: number;
}

const ProductCard = ({ name, price, image, emoji, index }: ProductCardProps) => {
  return (
    <motion.div 
      initial={{ opacity: 0, x: -10 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.3, delay: 0.5 + (index * 0.1) }}
      className="flex gap-3 items-center p-2 bg-gray-50 rounded-lg border border-gray-100 shadow-sm"
    >
      <div className={`w-12 h-12 rounded-md ${image} flex items-center justify-center`}>
        <span className="text-xl">{emoji}</span>
      </div>
      <div className="flex-1">
        <div className="text-xs font-medium">{name}</div>
        <div className="text-xs text-green-600">{price}</div>
      </div>
      <button className="bg-green-600 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
        <ShoppingBag className="h-3 w-3" />
        <span>Add</span>
      </button>
    </motion.div>
  );
};

export default ProductCard;
