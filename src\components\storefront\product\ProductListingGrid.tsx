
import React from 'react';
import { Product } from '@/types/unified-product';
import { Skeleton } from '@/components/ui/skeleton';
import ProductCard from '@/components/storefront/ProductCard';
import ProductPagination from './ProductPagination';

interface ProductListingGridProps {
  products: Product[];
  isLoading: boolean;
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const ProductListingGrid: React.FC<ProductListingGridProps> = ({
  products,
  isLoading,
  currentPage,
  totalPages,
  onPageChange
}) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="h-[320px] bg-gray-100 rounded-lg animate-pulse"></div>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
        {products.map(product => (
          <ProductCard key={product.id} product={product} />
        ))}
      </div>
      
      {totalPages > 1 && (
        <ProductPagination 
          currentPage={currentPage} 
          totalPages={totalPages}
          onPageChange={onPageChange} 
        />
      )}
    </>
  );
};

export default ProductListingGrid;
