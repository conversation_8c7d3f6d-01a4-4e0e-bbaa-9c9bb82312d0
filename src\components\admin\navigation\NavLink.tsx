
import React from 'react';
import { Link } from 'react-router-dom';

interface NavLinkProps {
  href: string;
  label: string;
  active: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({ 
  href, 
  label, 
  active 
}) => {
  return (
    <Link 
      to={href}
      className={`transition-colors hover:text-foreground ${
        active 
          ? 'text-foreground font-semibold' 
          : 'text-muted-foreground'
      }`}
    >
      {label}
    </Link>
  );
};

export default NavLink;
