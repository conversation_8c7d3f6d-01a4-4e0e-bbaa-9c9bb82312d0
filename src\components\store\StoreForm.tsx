
import React, { useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useStore } from '@/contexts'; 
import { StoreFormData } from '@/types/store';
import { generateUrlFromName, checkStoreUrlAvailability } from '@/utils/storeHelpers';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react';

const formSchema = z.object({
  name: z.string().min(2, { message: 'Store name must be at least 2 characters.' })
    .max(50, { message: 'Store name must be at most 50 characters.' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters.' })
    .max(500, { message: 'Description must be at most 500 characters.' }),
  storeUrl: z.string().optional(),
});

interface StoreFormProps {
  data: StoreFormData;
  updateData: (data: Partial<StoreFormData>) => void;
}

const StoreForm: React.FC<StoreFormProps> = ({ data, updateData }) => {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: data.name || '',
      description: data.description || '',
      storeUrl: data.storeUrl || '',
    },
  });
  
  // URL validation states
  const [urlCheckStatus, setUrlCheckStatus] = useState<'checking' | 'available' | 'unavailable' | 'error' | null>(null);
  const [urlSuggestion, setUrlSuggestion] = useState<string | null>(null);
  
  // State to track manual URL edits
  const [urlManuallyEdited, setUrlManuallyEdited] = React.useState(false);

  // Define the debounced values state with explicit non-optional types
  const [debouncedValues, setDebouncedValues] = React.useState({
    name: data.name || '',
    description: data.description || '',
    storeUrl: data.storeUrl || '',
  });

  const onSubmit = (values: z.infer<typeof formSchema>) => {
    updateData(values);
  };

  // Auto-generate storeUrl when name changes
  useEffect(() => {
    const name = form.watch('name');
    if (name && !urlManuallyEdited) {
      const generatedUrl = generateUrlFromName(name);
      form.setValue('storeUrl', generatedUrl);
    }
  }, [form.watch('name'), urlManuallyEdited]);

  // Check URL availability when storeUrl changes
  useEffect(() => {
    const checkUrlAvailability = async () => {
      const storeUrl = form.watch('storeUrl');
      if (!storeUrl || storeUrl.length < 2) {
        setUrlCheckStatus(null);
        setUrlSuggestion(null);
        return;
      }

      setUrlCheckStatus('checking');
      
      try {
        const result = await checkStoreUrlAvailability(storeUrl);
        
        if (result.error) {
          setUrlCheckStatus('error');
          console.error(result.error);
          return;
        }
        
        if (result.available) {
          setUrlCheckStatus('available');
          setUrlSuggestion(null);
        } else {
          setUrlCheckStatus('unavailable');
          setUrlSuggestion(result.suggestion || null);
          
          // If there's a sanitized version that's different from the input, update the form
          if (result.sanitized && result.sanitized !== storeUrl) {
            form.setValue('storeUrl', result.sanitized);
            toast.info('The URL has been sanitized to remove invalid characters');
          }
        }
      } catch (error) {
        console.error('Error checking URL availability:', error);
        setUrlCheckStatus('error');
      }
    };

    const timeoutId = setTimeout(() => {
      checkUrlAvailability();
    }, 500); // Debounce URL checks

    return () => clearTimeout(timeoutId);
  }, [form.watch('storeUrl')]);

  // Auto-save as user types
  React.useEffect(() => {
    const subscription = form.watch((value) => {
      onSubmit(value as z.infer<typeof formSchema>);
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const handleUseSuggestion = () => {
    if (urlSuggestion) {
      form.setValue('storeUrl', urlSuggestion);
      setUrlManuallyEdited(true);
    }
  };

  // Reset URL manual edit state when store name changes significantly  
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newName = e.target.value;
    const oldName = form.getValues('name');
    
    // If name changes significantly (more than just a character), reset the manual edit flag
    if (Math.abs(newName.length - oldName.length) > 2) {
      setUrlManuallyEdited(false);
    }
    
    form.setValue('name', newName);
  };

  return (
    <div>
      <h2 className="text-2xl font-bold mb-6">Basic Store Information</h2>
      <p className="text-muted-foreground mb-6">
        Let's start with the essential details about your store.
      </p>

      <Form {...form}>
        <form onChange={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Store Name</FormLabel>
                <FormControl>
                  <Input 
                    placeholder="My Awesome Store" 
                    {...field} 
                    onChange={handleNameChange}
                  />
                </FormControl>
                <FormDescription>
                  This is your store's name as it will appear to customers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Store Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Describe what your store sells and what makes it special..."
                    className="resize-none min-h-[120px]"
                    {...field} 
                  />
                </FormControl>
                <FormDescription>
                  Help customers understand what your store offers.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="storeUrl"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Store URL</FormLabel>
                <div className="flex items-center">
                  <span className="bg-muted px-3 py-2 rounded-l-md border border-r-0 border-input text-muted-foreground">
                    m-duka.app/
                  </span>
                  <div className="flex-1 relative">
                    <Input 
                      className="rounded-l-none" 
                      placeholder="my-awesome-store" 
                      {...field} 
                      onChange={(e) => {
                        setUrlManuallyEdited(true);
                        field.onChange(e);
                      }}
                    />
                    {urlCheckStatus && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        {urlCheckStatus === 'checking' && (
                          <AlertCircle className="h-5 w-5 text-yellow-500" />
                        )}
                        {urlCheckStatus === 'available' && (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        )}
                        {urlCheckStatus === 'unavailable' && (
                          <XCircle className="h-5 w-5 text-red-500" />
                        )}
                        {urlCheckStatus === 'error' && (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    )}
                  </div>
                </div>
                
                {urlCheckStatus === 'unavailable' && urlSuggestion && (
                  <div className="mt-2 text-sm text-red-500">
                    This URL is already taken. 
                    <button 
                      type="button"
                      onClick={handleUseSuggestion}
                      className="ml-2 text-blue-500 hover:underline"
                    >
                      Use "m-duka.app/{urlSuggestion}" instead?
                    </button>
                  </div>
                )}
                
                <FormDescription>
                  This will be used to create a unique URL for your store.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </form>
      </Form>
    </div>
  );
};

export default StoreForm;
