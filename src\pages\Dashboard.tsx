import React, { useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import { useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useStore, useProduct, useOrder, useAuth } from '@/contexts';
import { BarChart, ExternalLink, Package, ShoppingCart, Tag, Users, CreditCard, Percent } from 'lucide-react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatCurrency } from '@/utils/formatters';
import MetricCard from '@/components/admin/dashboard/MetricCard';
import { SupabaseConnectionStatus } from '@/components/SupabaseConnectionStatus';

const Dashboard: React.FC = () => {
  const navigate = useNavigate();
  const { currentStore, isLoading: storeLoading } = useStore();
  const { products, fetchProducts, isLoading: productsLoading } = useProduct();
  const { orders, fetchOrders, isLoading: ordersLoading } = useOrder();
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setHasError(false);
      try {
        console.log("Dashboard loadData:", { 
          currentStore, 
          storeId: currentStore?.id,
          isAuthenticated,
          user
        });
        
        if (currentStore?.id) {
          const promises = [
            fetchProducts(currentStore.id),
            fetchOrders()
          ];
          
          await Promise.allSettled(promises);
        } else {
          console.log("No current store selected or store ID is missing");
        }
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
        setHasError(true);
      } finally {
        setIsLoading(false);
      }
    };

    if (!authLoading) {
      if (isAuthenticated && currentStore?.id) {
        loadData();
      } else if (isAuthenticated) {
        console.log("User authenticated but no store selected");
        setIsLoading(false);
      } else {
        setIsLoading(false);
      }
    }
  }, [currentStore?.id, authLoading, isAuthenticated, fetchProducts, fetchOrders]);

  const totalRevenue = orders?.reduce((sum, order) => sum + (Number(order.total_amount) || 0), 0) || 0;
  const totalOrders = orders?.length || 0;
  const totalProducts = products?.length || 0;
  const totalCustomers = 18;

  const recentOrders = orders?.slice(0, 5) || [];

  const topProducts = products
    ?.map((product) => {
      // Order items not available in current schema - using placeholder
      const sales = 0;
      return {
        product,
        sales,
      };
    })
    .sort((a, b) => b.sales - a.sales)
    .slice(0, 5) || [];

  const isPageLoading = authLoading || storeLoading || isLoading;

  if (isPageLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64 p-6">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          <span className="ml-3">Loading dashboard data...</span>
        </div>
      </DashboardLayout>
    );
  }

  if (isAuthenticated && !currentStore) {
    return (
      <DashboardLayout>
        <Helmet>
          <title>Welcome to M-Duka - Get Started</title>
        </Helmet>
        <div className="p-6">
          {/* Welcome Header */}
          <div className="mb-8">
            <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 text-white">
              <h1 className="text-3xl font-bold mb-2">
                Welcome to M-Duka, {user?.name?.split(' ')[0] || 'Friend'}! 🎉
              </h1>
              <p className="text-lg opacity-90">
                You're one step away from turning your WhatsApp into a powerful mobile store. 
                Let's explore what M-Duka can do for your business!
              </p>
            </div>
          </div>

          {/* Quick Start Options */}
          <div className="grid md:grid-cols-2 gap-6 mb-8">
            <Card className="border-2 border-green-200 hover:border-green-300 transition-colors">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Package className="h-5 w-5 text-green-600" />
                  </div>
                  Create Your First Store
                </CardTitle>
                <CardDescription>
                  Set up your online store in under 3 minutes. Start selling immediately!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  onClick={() => navigate('/create-store')} 
                  className="w-full bg-green-500 hover:bg-green-600"
                  size="lg"
                >
                  Create Store Now →
                </Button>
                <p className="text-xs text-muted-foreground mt-2 text-center">
                  Free forever • No credit card required
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 border-blue-200 hover:border-blue-300 transition-colors">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <BarChart className="h-5 w-5 text-blue-600" />
                  </div>
                  Explore Demo Store
                </CardTitle>
                <CardDescription>
                  See how M-Duka works with our interactive demo. No setup required!
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button 
                  variant="outline" 
                  onClick={() => navigate('/demo-store')} 
                  className="w-full border-blue-300 text-blue-600 hover:bg-blue-50"
                  size="lg"
                >
                  View Demo →
                </Button>
                <p className="text-xs text-muted-foreground mt-2 text-center">
                  Browse • Add products • See features
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Features Preview */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>What You'll Get with M-Duka</CardTitle>
              <CardDescription>
                Everything you need to run a successful online business through WhatsApp
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4">
                <div className="text-center p-4">
                  <div className="p-3 bg-green-100 rounded-full w-fit mx-auto mb-3">
                    <ShoppingCart className="h-6 w-6 text-green-600" />
                  </div>
                  <h3 className="font-semibold mb-2">WhatsApp Integration</h3>
                  <p className="text-sm text-muted-foreground">
                    Turn your WhatsApp into a complete mobile store
                  </p>
                </div>
                <div className="text-center p-4">
                  <div className="p-3 bg-blue-100 rounded-full w-fit mx-auto mb-3">
                    <CreditCard className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold mb-2">M-Pesa Payments</h3>
                  <p className="text-sm text-muted-foreground">
                    Accept payments instantly with M-Pesa integration
                  </p>
                </div>
                <div className="text-center p-4">
                  <div className="p-3 bg-purple-100 rounded-full w-fit mx-auto mb-3">
                    <Users className="h-6 w-6 text-purple-600" />
                  </div>
                  <h3 className="font-semibold mb-2">Customer Management</h3>
                  <p className="text-sm text-muted-foreground">
                    Track orders, customers, and grow your business
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Add Products
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Learn how to add and manage your products
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Watch Tutorial
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <ExternalLink className="h-4 w-4" />
                  Connect WhatsApp
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Link your WhatsApp Business account
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Setup Guide
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Percent className="h-4 w-4" />
                  Payment Setup
                </h3>
                <p className="text-sm text-muted-foreground mb-3">
                  Configure M-Pesa and other payments
                </p>
                <Button variant="outline" size="sm" className="w-full">
                  Configure Now
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (hasError) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <Card>
            <CardHeader>
              <CardTitle>Error Loading Dashboard</CardTitle>
              <CardDescription>We encountered a problem while loading your dashboard data.</CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={() => window.location.reload()}>Retry</Button>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  // Plan handling - will be dynamic later when subscription system is implemented

  return (
    <DashboardLayout>
      <Helmet>
        <title>Dashboard - {currentStore?.name || 'm-duka'}</title>
      </Helmet>
      <div className="p-6">
        <div className="mb-6 rounded-lg p-4 flex items-center justify-between bg-blue-50 border-l-4 border-blue-500">
          <div>
            <strong>Welcome to M-Duka Free Plan!</strong>
            <span className="block text-sm text-muted-foreground mt-1">
              You are on the free plan. Upgrade to access more features.
            </span>
          </div>
          <Button
            variant="outline"
            className="ml-2"
            onClick={() => window.location.href = "/settings/billing"}
          >
            Upgrade to Premium
          </Button>
        </div>

        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(totalRevenue, 'KES')}
            description="Lifetime sales"
            icon={<CreditCard className="h-6 w-6 text-blue-500" />}
          />
          <MetricCard
            title="Orders"
            value={totalOrders.toString()}
            description="Total orders"
            icon={<ShoppingCart className="h-6 w-6 text-emerald-500" />}
          />
          <MetricCard
            title="Products"
            value={totalProducts.toString()}
            description="Active products"
            icon={<Package className="h-6 w-6 text-amber-500" />}
          />
          <MetricCard
            title="Customers"
            value={totalCustomers.toString()}
            description="Store customers"
            icon={<Users className="h-6 w-6 text-purple-500" />}
          />
        </div>

        {/* Temporary Supabase Connection Status */}
        <div className="mb-6">
          <SupabaseConnectionStatus />
        </div>

        <Tabs defaultValue="orders" className="space-y-4">
          <TabsList>
            <TabsTrigger value="orders">Recent Orders</TabsTrigger>
            <TabsTrigger value="products">Top Products</TabsTrigger>
          </TabsList>
          <TabsContent value="orders" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Recent Orders</CardTitle>
                <CardDescription>
                  Latest orders from your store
                </CardDescription>
              </CardHeader>
              <CardContent>
                {recentOrders.length === 0 ? (
                  <p className="text-muted-foreground">No recent orders found.</p>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="text-sm text-muted-foreground">
                        <th className="text-left py-2">Order ID</th>
                        <th className="text-left py-2">Date</th>
                        <th className="text-left py-2">Status</th>
                        <th className="text-right py-2">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentOrders.map((order) => (
                        <tr key={order.id} className="border-t">
                          <td className="py-3 font-medium">#{order.id.substring(0, 8)}...</td>
                          <td className="py-3">{new Date(order.created_at).toLocaleDateString()}</td>
                          <td className="py-3">
                            <span className={`px-2 py-1 rounded-full text-xs capitalize ${
                              order.status === 'delivered'
                                ? 'bg-green-100 text-green-700'
                                : order.status === 'shipped'
                                  ? 'bg-blue-100 text-blue-700'
                                  : order.status === 'processing'
                                    ? 'bg-yellow-100 text-yellow-700'
                                    : order.status === 'pending'
                                      ? 'bg-gray-100 text-gray-700'
                                      : 'bg-red-100 text-red-700'
                            }`}>
                              {order.status}
                            </span>
                          </td>
                          <td className="py-3 text-right">{formatCurrency(Number(order.total_amount), 'KES')}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => navigate('/orders')} className="w-full">
                  View All Orders
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
          <TabsContent value="products">
            <Card>
              <CardHeader>
                <CardTitle>Top Products</CardTitle>
                <CardDescription>
                  Your best selling products based on recent orders
                </CardDescription>
              </CardHeader>
              <CardContent>
                {topProducts.length === 0 ? (
                    <p className="text-muted-foreground">Not enough order data to determine top products.</p>
                ) : (
                  <table className="w-full">
                    <thead>
                      <tr className="text-sm text-muted-foreground">
                        <th className="text-left py-2">Product</th>
                        <th className="text-right py-2">Units Sold</th>
                      </tr>
                    </thead>
                    <tbody>
                      {topProducts.map(({ product, sales }) => (
                        <tr key={product.id} className="border-t">
                          <td className="py-3 font-medium">{product.name}</td>
                          <td className="py-3 text-right">{sales}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                )}
              </CardContent>
              <CardFooter>
                <Button variant="outline" onClick={() => navigate('/products')} className="w-full">
                  View All Products
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Dashboard;
