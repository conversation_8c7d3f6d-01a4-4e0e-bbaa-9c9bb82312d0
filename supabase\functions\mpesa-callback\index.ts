
import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.36.0';

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Handle M-Pesa callback
serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Validate content type for non-OPTIONS requests
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return new Response(
        JSON.stringify({
          error: 'Invalid content type. Expected application/json',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    // Get the request body
    const mpesaCallback = await req.json();
    console.log('M-Pesa callback received:', mpesaCallback);

    // Extract relevant information
    const {
      TransactionType,
      TransID,
      TransTime,
      TransAmount,
      BusinessShortCode,
      BillRefNumber, // This would be our order ID
      MSISDN, // Customer phone number
      FirstName,
      MiddleName,
      LastName,
      ResultCode,
      ResultDesc,
    } = mpesaCallback;

    // Validate required fields
    if (!TransID || !BillRefNumber || ResultCode === undefined) {
      console.error('Missing required fields in M-Pesa callback:', {
        TransID,
        BillRefNumber,
        ResultCode,
      });
      
      return new Response(
        JSON.stringify({
          error: 'Missing required fields',
          details: 'The callback is missing critical information',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    const orderId = BillRefNumber;
    console.log(`Processing M-Pesa callback for order ${orderId}, transaction ID: ${TransID}`);

    // Find the transaction for this order
    const { data: transactions, error: txError } = await supabase
      .from('payment_transactions')
      .select('*')
      .eq('order_id', orderId)
      .limit(1);

    if (txError) {
      console.error('Database error when fetching transaction:', txError);
      throw txError;
    }

    if (!transactions || transactions.length === 0) {
      console.error(`No transaction found for order: ${orderId}`);
      return new Response(
        JSON.stringify({
          error: 'Transaction not found',
          details: `No transaction exists for order: ${orderId}`,
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      );
    }

    const transaction = transactions[0];

    // Check if the payment was successful (ResultCode === '0')
    if (ResultCode === '0') {
      // Payment was successful
      console.log(`M-Pesa payment successful for order ${orderId}`);

      // Update transaction
      const { error: updateError } = await supabase
        .from('payment_transactions')
        .update({
          status: 'completed',
          transaction_id: TransID,
          transaction_reference: MSISDN,
          payment_data: {
            ...transaction.payment_data,
            mpesa_response: mpesaCallback,
            transaction_time: TransTime,
            customer_name: `${FirstName || ''} ${MiddleName || ''} ${LastName || ''}`.trim(),
            customer_phone: MSISDN,
            completed_at: new Date().toISOString()
          }
        })
        .eq('id', transaction.id);

      if (updateError) {
        console.error('Error updating transaction:', updateError);
        throw updateError;
      }

      // Update order status
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({ status: 'paid' })
        .eq('id', orderId);

      if (orderUpdateError) {
        console.error('Error updating order status:', orderUpdateError);
        throw orderUpdateError;
      }
      
      console.log(`Successfully updated order ${orderId} status to 'paid'`);
    } else {
      // Payment failed
      console.log(`M-Pesa payment failed for order ${orderId}: ${ResultDesc}`);

      // Update transaction
      const { error: updateError } = await supabase
        .from('payment_transactions')
        .update({
          status: 'failed',
          payment_data: {
            ...transaction.payment_data,
            mpesa_response: mpesaCallback,
            failure_reason: ResultDesc,
            failed_at: new Date().toISOString()
          }
        })
        .eq('id', transaction.id);

      if (updateError) {
        console.error('Error updating transaction:', updateError);
        throw updateError;
      }

      // Update order status
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({ status: 'payment_failed' })
        .eq('id', orderId);

      if (orderUpdateError) {
        console.error('Error updating order status:', orderUpdateError);
        throw orderUpdateError;
      }
      
      console.log(`Successfully updated order ${orderId} status to 'payment_failed'`);
    }

    // Send an acknowledgment response
    return new Response(
      JSON.stringify({
        ResultCode: '0',
        ResultDesc: 'Confirmation received successfully',
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error processing M-Pesa callback:', error);

    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
