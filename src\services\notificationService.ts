
import { supabase } from "@/integrations/supabase/client";

export const sendNotificationEmail = async (
  type: "signup" | "subscription",
  userData: { name: string; email: string },
  adminEmail: string
) => {
  try {
    const { data, error } = await supabase.functions.invoke("send-notification", {
      body: {
        type,
        userData,
        adminEmail,
      },
    });

    if (error) throw error;
    return { success: true, data };
  } catch (error) {
    console.error("Error sending notification:", error);
    return { success: false, error };
  }
};

export const saveNotificationSettings = async (adminEmail: string) => {
  // Notification settings functionality not implemented yet
  console.warn('Notification settings not available - notification_settings table does not exist');
  return { success: true, data: null };
};
