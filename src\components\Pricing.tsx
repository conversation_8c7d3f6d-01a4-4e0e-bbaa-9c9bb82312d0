
import React, { useState } from "react";
import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
import { Link } from "react-router-dom";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Check, Calendar, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { planCardData } from "@/components/settings/plans/plansData";
import PlanCard from "@/components/settings/plans/PlanCard";
import PlansComparisonTable from "@/components/settings/plans/PlansComparisonTable";
import { PLAN_FEATURES } from "@/components/settings/plans/planCategories";

// Utility to map PLAN_FEATURES to the correct plan tiers
function generateComparisonFeatures(features) {
  // A feature is included for X plan or higher
  return features.map(f => ({
    name: f.name,
    basic: f.tier === "basic",
    // Only ticked for basic features
    premium: f.tier === "basic" || f.tier === "premium",
    business: true
  }));
}

const comparisonFeatures = generateComparisonFeatures(PLAN_FEATURES);

const Pricing = () => {
  const [isYearly, setIsYearly] = useState(false);
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  
  const togglePricing = (value: string) => {
    setIsYearly(value === "yearly");
  };
  
  // Sort plans to ensure Basic, Premium, Business order
  const planOrder = { "Basic": 0, "Premium": 1, "Business": 2 };
  const sortedPlanCardData = [...planCardData].sort((a, b) => {
    return planOrder[a.title as keyof typeof planOrder] - planOrder[b.title as keyof typeof planOrder];
  });

  return (
    <section id="pricing" ref={sectionRef} className="py-20 bg-gradient-to-b from-accent/30 to-background">
      <div className="container px-4 md:px-6 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center rounded-full bg-white px-3 py-1 text-sm font-medium text-mduka-700 mb-4 shadow-sm"
          >
            <span className="flex h-2 w-2 rounded-full bg-mduka-500 mr-1.5"></span>
            <span>Simple Pricing</span>
          </motion.div>
          
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-balance"
          >
            Pricing plans for businesses of all sizes
          </motion.h2>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-muted-foreground text-lg max-w-2xl mx-auto text-pretty"
          >
            Choose the perfect plan for your business needs. Start with our free tier and upgrade as you grow.
          </motion.p>
        </div>

        <div className="flex justify-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-muted p-1 rounded-lg inline-flex items-center shadow-sm"
          >
            <ToggleGroup type="single" value={isYearly ? 'yearly' : 'monthly'} onValueChange={togglePricing}>
              <ToggleGroupItem value="monthly" className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white">
                <Clock className="h-3.5 w-3.5" />
                Monthly
              </ToggleGroupItem>
              <ToggleGroupItem value="yearly" className="flex items-center gap-1.5 data-[state=on]:bg-green-500 data-[state=on]:text-white">
                <Calendar className="h-3.5 w-3.5" />
                Yearly
              </ToggleGroupItem>
            </ToggleGroup>
          </motion.div>
        </div>
        
        <div className="grid gap-8 md:grid-cols-3 max-w-6xl mx-auto">
          {sortedPlanCardData.map((plan, index) => {
            const [planRef, planInView] = useInView({
              triggerOnce: true,
              threshold: 0.1
            });
            
            const planWithAction = {
              ...plan,
              buttonAction: <Link to="/settings/billing">{plan.buttonText}</Link>
            };
            
            // Add highlight effect for the middle (Premium) card
            const isMiddleCard = plan.title === "Premium";
            
            return (
              <motion.div
                key={index}
                ref={planRef}
                initial={{ opacity: 0, y: 20 }}
                animate={planInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                className={`${isMiddleCard ? 'md:-mt-4 md:mb-4 z-10' : ''}`}
              >
                <PlanCard 
                  {...planWithAction} 
                  isYearly={isYearly} 
                  isPopular={isMiddleCard}
                />
              </motion.div>
            );
          })}
        </div>
        
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={sectionInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="text-center mt-12"
        >
          <p className="text-center text-base text-muted-foreground">
            Need a custom plan for your enterprise? <Link to="/pricing" className="text-mduka-600 hover:underline font-medium">View all plans</Link>
          </p>
        </motion.div>
        
        <div className="max-w-4xl mx-auto mt-24">
          <PlansComparisonTable features={comparisonFeatures} collapsible />
          <div className="text-sm text-muted-foreground mt-4 text-center">
            <p>
              Upgrade anytime and unlock features that suit your growth.
              <span className="ml-1 font-semibold text-primary">Cancel any time.</span>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Pricing;
