
import React from 'react';
import { cn } from '@/lib/utils';

interface PriceDisplayProps {
  price: number;
  period: string;
  priceLabel?: string;
  discount?: string;
  className?: string;
}

const PriceDisplay = ({ price, period, priceLabel, discount, className }: PriceDisplayProps) => {
  return (
    <div className={cn("flex items-center justify-center gap-1", className)}>
      <div className="flex flex-col items-center">
        <div className="flex items-start">
          <span className="text-3xl font-bold">
            {price === 0 ? 'Free' : (
              <>
                <span className="text-lg align-top mt-1 mr-0.5">$</span>
                {price}
              </>
            )}
          </span>
          
          {price !== 0 && (
            <span className="text-sm text-muted-foreground ml-1 self-end mb-1">/{period}</span>
          )}
        </div>
        
        {price === 0 && priceLabel && (
          <span className="text-xs text-muted-foreground">{priceLabel}</span>
        )}
        
        {discount && (
          <span className="text-xs font-medium text-green-600 mt-1 bg-green-100 px-2 py-0.5 rounded-full">
            {discount}
          </span>
        )}
      </div>
    </div>
  );
};

export default PriceDisplay;
