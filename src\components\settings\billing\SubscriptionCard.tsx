
import React from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

const SubscriptionCard = () => {
  return (
    <Card className="mb-8">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" /> 
          Subscription
        </CardTitle>
        <CardDescription>
          Manage your subscription and payment information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between gap-4">
          <div>
            <h3 className="text-lg font-medium text-muted-foreground">No Active Plan</h3>
            <p className="text-sm text-muted-foreground">Select a plan to access premium features</p>
          </div>
          <Button>Choose a Plan</Button>
        </div>
        
        <Separator />
        
        <div className="grid gap-4 md:grid-cols-2">
          <div>
            <h4 className="font-medium mb-1">Current billing cycle</h4>
            <p className="text-sm text-muted-foreground">No active subscription</p>
          </div>
          <div>
            <h4 className="font-medium mb-1">Payment method</h4>
            <p className="text-sm text-muted-foreground">No payment method on file</p>
            <Button variant="link" className="p-0 h-auto text-xs mt-1">Add payment method</Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SubscriptionCard;
