
import type { User as SupabaseUser } from '@supabase/supabase-js';

// Extend the base User type with our custom fields
export interface User {
  id: string;
  email: string;
  name?: string;
  role?: string;
  avatar_url?: string;
}

export type AuthContextType = {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<{ user: User | null; error: any }>;
  signup: (name: string, email: string, password: string, role?: string) => Promise<{ success: boolean; error: any }>;
  signInWithSocial: (provider: string) => Promise<{ success: boolean; url?: string; error?: any }>;
  logout: () => Promise<void>;
  loginAsAdmin: () => Promise<{ success: boolean }>;
  resetPassword: (email: string) => Promise<void>;
  updatePassword: (password: string) => Promise<void>;
  sendMagicLink: (email: string) => Promise<{ success: boolean; error?: any }>;
  inviteUser: (email: string, role: string) => Promise<{ success: boolean; error?: any }>;
  updateEmail: (email: string) => Promise<{ success: boolean; error?: any }>;
  reauthenticate: (password: string) => Promise<{ success: boolean; error?: any }>;
  confirmSignUp: (token: string) => Promise<{ success: boolean; error?: any }>;
};

