
import React from 'react';
import { Link } from 'react-router-dom';
import { Send } from 'lucide-react';
import { Button } from '@/components/ui/button';
import SidebarGroup from './SidebarGroup';

interface MarketingGroupProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}

const MarketingGroup: React.FC<MarketingGroupProps> = ({
  isOpen,
  onOpenChange
}) => {
  return (
    <SidebarGroup
      title="Marketing"
      icon={<Send className="h-5 w-5 mr-2" />}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      isActive={false}
    >
      <Button variant="ghost" className="w-full justify-start px-3 py-2 h-auto" asChild disabled>
        <Link to="/marketing/campaigns">Campaigns</Link>
      </Button>
      <Button variant="ghost" className="w-full justify-start px-3 py-2 h-auto" asChild disabled>
        <Link to="/marketing/automations">Automations</Link>
      </Button>
    </SidebarGroup>
  );
};

export default MarketingGroup;
