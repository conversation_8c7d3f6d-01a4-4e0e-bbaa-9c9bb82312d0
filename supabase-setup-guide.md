# Supabase Setup Guide

Your current Supabase project has been removed. To fix this and enable editing in Lovable, follow these steps:

## Step 1: Create a New Supabase Project

1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Click "New project"
3. Choose your organization
4. Fill in your project details:
   - Name: "M-duka WhatsApp Store" (or any name you prefer)
   - Database Password: Create a strong password
   - Region: Choose the closest region to your users

## Step 2: Get Your Project Credentials

After your project is created, go to Settings > API and copy:
- Project URL (starts with https://xxx.supabase.co)
- Project API Keys > anon public key

## Step 3: Update Your Project Configuration

You need to update the Supabase configuration in your project. Replace the values in:

### File: `src/lib/supabase.ts` (lines 23-24)
```typescript
const supabaseUrl = import.meta.env.VITE_PUBLIC_SUPABASE_URL || 'YOUR_NEW_PROJECT_URL';
const supabaseAnonKey = import.meta.env.VITE_PUBLIC_SUPABASE_ANON_KEY || 'YOUR_NEW_ANON_KEY';
```

### File: `supabase/config.toml` (line 1)
```toml
project_id = "YOUR_NEW_PROJECT_ID"
```

## Step 4: Set Up Database Schema

Your new Supabase project will need the database schema. You can either:

1. **Use the migration files** (if they exist in `/supabase/migrations/`)
2. **Or manually create the tables** needed for the M-duka app

### Required Tables:
- stores
- products  
- orders
- order_items
- customers
- categories
- wishlists

## Step 5: Update Environment Variables

For deployment, set these environment variables in:

### Vercel (if deploying there):
- `VITE_PUBLIC_SUPABASE_URL`: Your new project URL
- `VITE_PUBLIC_SUPABASE_ANON_KEY`: Your new anon key

### Lovable.dev:
In the Lovable editor, go to project settings and update the environment variables.

## Step 6: Test the Connection

After updating the configuration:
1. Restart your development server
2. The Supabase error should be resolved
3. You should be able to edit in the Lovable editor again

## Quick Fix Command

Run this after getting your new credentials:
```bash
# Replace YOUR_PROJECT_ID, YOUR_URL, and YOUR_KEY with actual values
sed -i 's/nheycjpozywomwscplcz/YOUR_PROJECT_ID/g' supabase/config.toml
```

## Need Help?

If you need assistance with any of these steps, please share your new Supabase project credentials and I can help update the configuration files. 