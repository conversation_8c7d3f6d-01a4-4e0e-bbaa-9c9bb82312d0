
import React, { useState, useEffect } from 'react';
import { useStore } from '@/contexts';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AlertCircle, MessageCircle, Plus, Save, Trash2, CheckCircle, RefreshCw, Zap, Link, ExternalLink } from 'lucide-react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';

interface TemplateProps {
  id: string;
  name: string;
  message: string;
  eventType: string;
  status: 'approved' | 'pending' | 'rejected';
  lastUsed?: string;
  sentCount?: number;
}

const WhatsAppWorkflow = () => {
  const { currentStore } = useStore();
  const [activeTab, setActiveTab] = useState('templates');
  const [templates, setTemplates] = useState<TemplateProps[]>([
    {
      id: '1',
      name: 'Order Confirmation',
      message: "Hi {customer}, thank you for your order #{order_id}! We'll let you know when it ships.",
      eventType: 'order.created',
      status: 'approved',
      lastUsed: '2 hours ago',
      sentCount: 243
    },
    {
      id: '2',
      name: 'Shipping Notification',
      message: 'Good news {customer}! Your order #{order_id} has shipped. Track it here: {tracking_link}',
      eventType: 'order.shipped',
      status: 'approved',
      lastUsed: '4 hours ago',
      sentCount: 187
    },
    {
      id: '3',
      name: 'Delivery Confirmation',
      message: 'Your order #{order_id} has been delivered! We hope you enjoy your purchase. Please leave a review if you have a moment.',
      eventType: 'order.delivered',
      status: 'pending',
      lastUsed: 'Never',
      sentCount: 0
    }
  ]);
  
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateProps | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected');
  const [accountStatus, setAccountStatus] = useState({ verified: true, messagesAvailable: 1000, usedMessages: 430 });
  
  // Simulate checking connection status
  useEffect(() => {
    const checkConnection = () => {
      setConnectionStatus('connecting');
      setTimeout(() => {
        setConnectionStatus('connected');
      }, 1500);
    };
    
    checkConnection();
    const interval = setInterval(checkConnection, 60000);
    
    return () => clearInterval(interval);
  }, []);
  
  const handleSaveTemplate = () => {
    if (!selectedTemplate) return;
    
    setIsLoading(true);
    setTimeout(() => {
      setTemplates(prev => 
        prev.map(t => t.id === selectedTemplate.id ? selectedTemplate : t)
      );
      setIsLoading(false);
      toast.success('Template saved and synced with WhatsApp Business API');
    }, 1200);
  };
  
  const handleAddTemplate = () => {
    const newTemplate = {
      id: `template_${Date.now()}`,
      name: 'New Template',
      message: 'Hello {customer}, ',
      eventType: 'custom.event',
      status: 'pending' as const,
      lastUsed: 'Never',
      sentCount: 0
    };
    
    setTemplates(prev => [...prev, newTemplate]);
    setSelectedTemplate(newTemplate);
    toast.success('New template created');
  };
  
  const handleDeleteTemplate = (id: string) => {
    setIsLoading(true);
    setTimeout(() => {
      setTemplates(prev => prev.filter(t => t.id !== id));
      if (selectedTemplate?.id === id) {
        setSelectedTemplate(null);
      }
      setIsLoading(false);
      toast.success('Template deleted and removed from WhatsApp Business API');
    }, 800);
  };
  
  const handleSyncWithWhatsApp = () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      toast.success('Templates synchronized with WhatsApp Business API');
    }, 2000);
  };
  
  const eventTypeOptions = [
    { value: 'order.created', label: 'Order Created' },
    { value: 'order.paid', label: 'Order Paid' },
    { value: 'order.processing', label: 'Order Processing' },
    { value: 'order.shipped', label: 'Order Shipped' },
    { value: 'order.delivered', label: 'Order Delivered' },
    { value: 'order.cancelled', label: 'Order Cancelled' },
    { value: 'customer.registered', label: 'Customer Registered' },
    { value: 'customer.purchase', label: 'Customer Purchase' },
    { value: 'inventory.low', label: 'Low Inventory' },
    { value: 'return.requested', label: 'Return Requested' },
    { value: 'return.approved', label: 'Return Approved' },
    { value: 'custom.event', label: 'Custom Event' }
  ];
  
  const getStatusBadge = (status: TemplateProps['status']) => {
    switch (status) {
      case 'approved':
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-200">Approved</Badge>;
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-200">Pending Review</Badge>;
      case 'rejected':
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-200">Rejected</Badge>;
      default:
        return null;
    }
  };
  
  return (
    <div className="space-y-6">
      {/* Connection Status Card */}
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {connectionStatus === 'connected' ? (
                <CheckCircle className="h-5 w-5 text-green-600" />
              ) : connectionStatus === 'connecting' ? (
                <RefreshCw className="h-5 w-5 text-amber-600 animate-spin" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600" />
              )}
              <div>
                <p className="font-medium">WhatsApp Business API</p>
                <p className="text-sm text-green-700">
                  {connectionStatus === 'connected' 
                    ? 'Connected and operational' 
                    : connectionStatus === 'connecting'
                    ? 'Checking connection...'
                    : 'Disconnected'}
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" className="border-green-200 text-green-700">
                <RefreshCw className="h-4 w-4 mr-2" />
                Test Connection
              </Button>
              <Button variant="outline" size="sm" className="border-green-200 text-green-700">
                <Link className="h-4 w-4 mr-2" />
                Manage Credentials
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      
      <Tabs defaultValue="templates" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="templates">Message Templates</TabsTrigger>
          <TabsTrigger value="settings">WhatsApp Settings</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="automation">Automation Rules</TabsTrigger>
        </TabsList>
        
        <TabsContent value="templates" className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-lg font-medium">Template Library</h3>
              <p className="text-sm text-muted-foreground">Manage your WhatsApp message templates</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleSyncWithWhatsApp} disabled={isLoading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
                Sync Templates
              </Button>
              <Button onClick={handleAddTemplate}>
                <Plus className="h-4 w-4 mr-2" />
                New Template
              </Button>
            </div>
          </div>
          
          <div className="flex flex-col md:flex-row gap-6">
            <Card className="md:w-1/3">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Templates</CardTitle>
                <CardDescription>
                  {templates.length} templates configured
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                {templates.map(template => (
                  <div 
                    key={template.id}
                    className={`flex justify-between items-center p-3 rounded-md cursor-pointer ${selectedTemplate?.id === template.id ? 'bg-secondary' : 'hover:bg-secondary/50'}`}
                    onClick={() => setSelectedTemplate(template)}
                  >
                    <div>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">{template.name}</p>
                        {getStatusBadge(template.status)}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {eventTypeOptions.find(e => e.value === template.eventType)?.label || template.eventType}
                      </p>
                      <div className="flex items-center gap-2 mt-1 text-xs text-muted-foreground">
                        <span>Sent: {template.sentCount}</span>
                        <span>•</span>
                        <span>Last used: {template.lastUsed}</span>
                      </div>
                    </div>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleDeleteTemplate(template.id);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </CardContent>
            </Card>
            
            <Card className="md:w-2/3">
              <CardHeader>
                <CardTitle>
                  {selectedTemplate ? `Edit Template: ${selectedTemplate.name}` : 'Select a Template'}
                </CardTitle>
                <CardDescription>
                  Customize message content and settings
                </CardDescription>
              </CardHeader>
              <CardContent>
                {selectedTemplate ? (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="template-name">Template Name</Label>
                      <Input 
                        id="template-name"
                        value={selectedTemplate.name}
                        onChange={(e) => setSelectedTemplate({
                          ...selectedTemplate,
                          name: e.target.value
                        })}
                      />
                    </div>
                    
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="event-type">Trigger Event</Label>
                        <Select 
                          value={selectedTemplate.eventType}
                          onValueChange={(value) => setSelectedTemplate({
                            ...selectedTemplate,
                            eventType: value
                          })}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select an event" />
                          </SelectTrigger>
                          <SelectContent>
                            {eventTypeOptions.map(option => (
                              <SelectItem key={option.value} value={option.value}>
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="space-y-2">
                        <Label htmlFor="template-status">Template Status</Label>
                        <div className="flex items-center h-10 px-3 border rounded-md">
                          {getStatusBadge(selectedTemplate.status)}
                          {selectedTemplate.status === 'pending' && (
                            <span className="ml-2 text-xs text-muted-foreground">
                              Under review by WhatsApp
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="message-content">Message Content</Label>
                      <Textarea 
                        id="message-content"
                        rows={6}
                        value={selectedTemplate.message}
                        onChange={(e) => setSelectedTemplate({
                          ...selectedTemplate,
                          message: e.target.value
                        })}
                        placeholder="Enter your message template here"
                        className="resize-none"
                      />
                      <div className="flex flex-wrap gap-1 mt-1">
                        {['{customer}', '{order_id}', '{total}', '{status}', '{tracking_link}', '{product}', '{date}'].map(variable => (
                          <Badge key={variable} variant="outline" className="cursor-pointer hover:bg-secondary"
                            onClick={() => {
                              if (selectedTemplate) {
                                setSelectedTemplate({
                                  ...selectedTemplate,
                                  message: selectedTemplate.message + variable
                                });
                              }
                            }}
                          >
                            {variable}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="p-4 bg-secondary/50 rounded-md">
                      <p className="text-sm font-medium mb-2">Preview:</p>
                      <div className="flex items-start gap-3">
                        <div className="mt-0.5 rounded-full bg-green-100 p-1.5">
                          <MessageCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div className="bg-white rounded-lg p-3 shadow-sm text-sm">
                          {selectedTemplate.message
                            .replace('{customer}', 'John Doe')
                            .replace('{order_id}', '#1234')
                            .replace('{total}', '$125.00')
                            .replace('{status}', 'Shipped')
                            .replace('{tracking_link}', 'https://tracking.example.com/1234')
                            .replace('{product}', 'Premium T-shirt')
                            .replace('{date}', new Date().toLocaleDateString())}
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <MessageCircle className="h-12 w-12 text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">Select a template to edit or create a new one</p>
                  </div>
                )}
              </CardContent>
              {selectedTemplate && (
                <CardFooter className="flex justify-between">
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      View in WhatsApp
                    </Button>
                    <Button variant="outline" size="sm">
                      Test Send
                    </Button>
                  </div>
                  <Button 
                    onClick={handleSaveTemplate}
                    disabled={isLoading}
                  >
                    {isLoading ? 'Saving...' : 'Save Template'}
                    <Save className="ml-2 h-4 w-4" />
                  </Button>
                </CardFooter>
              )}
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Business API Configuration</CardTitle>
              <CardDescription>
                Configure your WhatsApp Business account settings for workflow automation
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
                <div className="flex items-start gap-3">
                  <Zap className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="font-medium text-blue-800">Connected to Pubbly Connect</p>
                    <p className="text-sm text-blue-700">
                      Your WhatsApp Business Account is connected and authenticated with Pubbly Connect Integration.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium mb-2">Account Status</p>
                  <div className="flex flex-col space-y-2">
                    <div className="flex justify-between">
                      <span>Verified Business Account</span>
                      <Badge variant={accountStatus.verified ? "default" : "outline"}>
                        {accountStatus.verified ? "Verified" : "Unverified"}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Messages Available</span>
                      <span className="font-medium">{accountStatus.messagesAvailable - accountStatus.usedMessages} / {accountStatus.messagesAvailable}</span>
                    </div>
                    <Progress 
                      value={(accountStatus.usedMessages / accountStatus.messagesAvailable) * 100} 
                      className="h-2"
                    />
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Auto-Respond to Customer Messages</Label>
                  <p className="text-sm text-muted-foreground">
                    Automatically reply to incoming customer messages
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              
              <div className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Queue Messages</Label>
                  <p className="text-sm text-muted-foreground">
                    Queue messages during non-business hours for later delivery
                  </p>
                </div>
                <Switch />
              </div>
              
              <div className="space-y-2">
                <Label>Business Hours</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm mb-1">Opening Time</p>
                    <Input type="time" defaultValue="09:00" />
                  </div>
                  <div>
                    <p className="text-sm mb-1">Closing Time</p>
                    <Input type="time" defaultValue="17:00" />
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter>
              <Button className="ml-auto">
                Save Settings
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle>WhatsApp Message Analytics</CardTitle>
              <CardDescription>
                Track and analyze your WhatsApp messaging performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-4 mb-6">
                <div className="bg-secondary/50 p-4 rounded-md">
                  <p className="text-sm text-muted-foreground mb-1">Messages Sent (30 days)</p>
                  <p className="text-2xl font-bold">1,243</p>
                  <p className="text-xs text-green-600">↑ 12% from last month</p>
                </div>
                <div className="bg-secondary/50 p-4 rounded-md">
                  <p className="text-sm text-muted-foreground mb-1">Open Rate</p>
                  <p className="text-2xl font-bold">97.8%</p>
                  <p className="text-xs text-green-600">↑ 2.1% from last month</p>
                </div>
                <div className="bg-secondary/50 p-4 rounded-md">
                  <p className="text-sm text-muted-foreground mb-1">Response Rate</p>
                  <p className="text-2xl font-bold">41.3%</p>
                  <p className="text-xs text-red-600">↓ 3.5% from last month</p>
                </div>
              </div>
              
              <div className="space-y-6">
                <div>
                  <h3 className="text-base font-medium mb-2">Top Performing Templates</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <p className="font-medium">Order Confirmation</p>
                        <p className="text-sm text-muted-foreground">Sent 243 times</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-600">98.7% Open Rate</p>
                        <p className="text-sm text-muted-foreground">52.6% Response Rate</p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center p-3 border rounded-md">
                      <div>
                        <p className="font-medium">Shipping Notification</p>
                        <p className="text-sm text-muted-foreground">Sent 187 times</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-green-600">99.1% Open Rate</p>
                        <p className="text-sm text-muted-foreground">35.8% Response Rate</p>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h3 className="text-base font-medium mb-2">Message Volume by Day</h3>
                  <div className="h-48 bg-slate-100 rounded-md flex items-center justify-center">
                    <p className="text-muted-foreground">Chart visualization would appear here</p>
                  </div>
                </div>
              </div>
              
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="automation">
          <Card>
            <CardHeader>
              <CardTitle>Automation Rules</CardTitle>
              <CardDescription>
                Create complex automation rules combining different triggers and actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-8 text-center">
                <MessageCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Advanced Automation Coming Soon</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  Pubbly Connect integration is setting up powerful automation capabilities 
                  including conditional logic, delays, and multi-step workflows.
                </p>
                <Button variant="outline" className="mt-4">
                  Join Early Access
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default WhatsAppWorkflow;
