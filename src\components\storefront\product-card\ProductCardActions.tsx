
import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShoppingCart, Heart, Loader2 } from "lucide-react";
import { Product } from "@/types/unified-product";
import { useCart, useWishlist } from "@/contexts";
import { useToast } from "@/hooks/use-toast";

interface ProductCardActionsProps {
  product: Product;
}

const ProductCardActions: React.FC<ProductCardActionsProps> = ({ product }) => {
  const { addToCart } = useCart();
  const { addToWishlist, removeFromWishlist, isInWishlist, isAddingToWishlist } = useWishlist();
  const { toast } = useToast();
  const [isAddingToCart, setIsAddingToCart] = React.useState(false);

  const productInWishlist = isInWishlist(product.id || '');

  const handleAddToCart = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (product.stock_quantity && product.stock_quantity > 0) {
      setIsAddingToCart(true);
      await addToCart(product, 1);
      setIsAddingToCart(false);
    } else {
      toast({
        title: "Cannot add to cart",
        description: "This product is out of stock.",
        variant: "destructive"
      });
    }
  };

  const handleToggleWishlist = async (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (productInWishlist) {
      await removeFromWishlist(product.id || '');
      toast({
        title: "Removed from wishlist",
        description: `${product.name} has been removed from your wishlist.`
      });
    } else {
      await addToWishlist(product);
      toast({
        title: "Added to wishlist",
        description: `${product.name} has been added to your wishlist.`
      });
    }
  };

  return (
    <div className="flex gap-2">
      <Button 
        className="flex-1"
        size="sm"
        disabled={product.stock_quantity === 0 || isAddingToCart}
        onClick={handleAddToCart}
      >
        {isAddingToCart ? (
          <>
            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            Adding...
          </>
        ) : (
          <>
            <ShoppingCart className="h-4 w-4 mr-2" />
            Add to Cart
          </>
        )}
      </Button>
      <Button
        variant={productInWishlist ? "default" : "outline"}
        size="icon"
        className="shrink-0"
        onClick={handleToggleWishlist}
        disabled={isAddingToWishlist}
      >
        {isAddingToWishlist ? (
          <Loader2 className="h-4 w-4 animate-spin" />
        ) : (
          <Heart className={`h-4 w-4 ${productInWishlist ? 'fill-current' : ''}`} />
        )}
      </Button>
    </div>
  );
};

export default ProductCardActions;
