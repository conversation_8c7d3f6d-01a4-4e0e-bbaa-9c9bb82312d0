
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { CreditCard, Loader2, Lock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CreditCardPaymentFormProps {
  amount: number;
  onPaymentComplete: (transactionId: string) => void;
}

const CreditCardPaymentForm: React.FC<CreditCardPaymentFormProps> = ({ amount, onPaymentComplete }) => {
  const [cardNumber, setCardNumber] = useState('');
  const [cardName, setCardName] = useState('');
  const [expiryDate, setExpiryDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [processing, setProcessing] = useState(false);
  const { toast } = useToast();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFormValid()) {
      toast({
        title: "Invalid card information",
        description: "Please check your card details and try again.",
        variant: "destructive",
      });
      return;
    }
    
    // For demo, we simulate a payment process
    setProcessing(true);
    
    // In a real implementation, this would call a payment processor
    setTimeout(() => {
      const mockTransactionId = 'CARD-' + Math.floor(Math.random() * 100000000);
      onPaymentComplete(mockTransactionId);
      setProcessing(false);
      
      toast({
        title: "Card authorized",
        description: "Your card has been successfully authorized.",
      });
    }, 2000);
  };
  
  const isFormValid = () => {
    // Basic validation - in a real app you'd use a library like card-validator
    return (
      cardNumber.replace(/\s/g, '').length === 16 &&
      cardName.length > 3 &&
      expiryDate.length === 5 &&
      cvv.length === 3
    );
  };
  
  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Add space after every 4 digits
    const formatted = digits.replace(/(\d{4})(?=\d)/g, '$1 ');
    
    return formatted.slice(0, 19); // 16 digits + 3 spaces = 19 chars
  };
  
  const formatExpiryDate = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Format as MM/YY
    if (digits.length > 2) {
      return digits.slice(0, 2) + '/' + digits.slice(2, 4);
    }
    
    return digits;
  };
  
  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="p-4 bg-muted rounded-md border text-sm mb-4 flex items-center">
        <Lock className="h-4 w-4 mr-2 text-muted-foreground" />
        <p>Your payment information is encrypted and secure.</p>
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="cardNumber">Card Number</Label>
        <Input
          id="cardNumber"
          placeholder="1234 5678 9012 3456"
          value={cardNumber}
          onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
          required
          maxLength={19}
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="cardName">Cardholder Name</Label>
        <Input
          id="cardName"
          placeholder="John Doe"
          value={cardName}
          onChange={(e) => setCardName(e.target.value)}
          required
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="expiryDate">Expiry Date</Label>
          <Input
            id="expiryDate"
            placeholder="MM/YY"
            value={expiryDate}
            onChange={(e) => setExpiryDate(formatExpiryDate(e.target.value))}
            required
            maxLength={5}
          />
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="cvv">CVV</Label>
          <Input
            id="cvv"
            type="text"
            placeholder="123"
            value={cvv}
            onChange={(e) => setCvv(e.target.value.replace(/\D/g, '').slice(0, 3))}
            required
            maxLength={3}
          />
        </div>
      </div>
      
      <Button 
        type="submit" 
        className="w-full mt-4"
        disabled={processing || !isFormValid()}
      >
        {processing ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Processing...
          </>
        ) : (
          <>
            <CreditCard className="mr-2 h-4 w-4" />
            Pay {amount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
          </>
        )}
      </Button>
    </form>
  );
};

export default CreditCardPaymentForm;
