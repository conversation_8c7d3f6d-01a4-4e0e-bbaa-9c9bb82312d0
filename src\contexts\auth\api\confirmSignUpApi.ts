
import { supabase } from '@/integrations/supabase/client';

export const confirmSignUp = async (token: string): Promise<{ success: boolean; error?: any }> => {
  try {
    const { data, error } = await supabase.auth.verifyOtp({
      token_hash: token,
      type: 'email',
    });
    
    if (error) {
      console.error("OTP verification error:", error.message);
      return { success: false, error };
    }
    
    console.log("OTP verified successfully:", data);
    return { success: true };
  } catch (error: any) {
    console.error('OTP verification error:', error);
    return { success: false, error };
  }
};
