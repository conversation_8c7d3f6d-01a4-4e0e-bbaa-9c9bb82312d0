
import React from 'react';
import { X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProductFiltersProps {
  selectedCategory: string | null;
  minPrice: string;
  maxPrice: string;
  onCategoryClick: (category: string | null) => void;
  onMinPriceChange: (value: string) => void;
  onMaxPriceChange: (value: string) => void;
  onClearFilters: () => void;
  onApplyPriceFilter: () => void;
  allCategories: string[];
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  selectedCategory,
  minPrice,
  maxPrice,
  onCategoryClick,
  onMinPriceChange,
  onMaxPriceChange,
  onClearFilters,
  onApplyPriceFilter,
  allCategories,
}) => {
  return (
    <div className="border rounded-md p-4 mb-4">
      <div className="flex justify-between items-center mb-3">
        <h3 className="font-medium">Filters</h3>
        <Button size="sm" variant="ghost" onClick={onClearFilters}>
          <X className="h-4 w-4 mr-1" />
          Clear All
        </Button>
      </div>
      
      <div className="mb-4">
        <h4 className="text-sm font-medium mb-2">Categories</h4>
        <div className="flex flex-wrap gap-2">
          <Button
            size="sm"
            variant={selectedCategory === null ? "default" : "outline"}
            onClick={() => onCategoryClick(null)}
          >
            All
          </Button>
          {allCategories.map(cat => (
            <Button
              key={cat}
              size="sm"
              variant={selectedCategory === cat ? "default" : "outline"}
              onClick={() => onCategoryClick(cat)}
            >
              {cat}
            </Button>
          ))}
        </div>
      </div>
      
      <div>
        <h4 className="text-sm font-medium mb-2">Price Range</h4>
        <div className="flex gap-2 items-center">
          <Input
            type="number"
            placeholder="Min"
            value={minPrice}
            onChange={(e) => onMinPriceChange(e.target.value)}
            className="w-24"
          />
          <span>-</span>
          <Input
            type="number"
            placeholder="Max"
            value={maxPrice}
            onChange={(e) => onMaxPriceChange(e.target.value)}
            className="w-24"
          />
          <Button size="sm" onClick={onApplyPriceFilter}>Apply</Button>
        </div>
      </div>
    </div>
  );
};

export default ProductFilters;
