import { motion } from "framer-motion";
import { useInView } from "react-intersection-observer";
const features = [{
  title: "Multi-Tenant Store Creation",
  description: "Create your own store with a custom subdomain in minutes. Connect your own domain with automatic SSL certificates.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
        <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
        <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path>
        <path d="M21 9V3"></path>
        <path d="M9 12v5"></path>
        <path d="M15 12v5"></path>
        <path d="M3 3v6"></path>
      </svg>
}, {
  title: "Product & Inventory Management",
  description: "Add, edit, and delete products with ease. Manage stock levels with low-stock alerts and product variants.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
        <rect width="6" height="6" x="15" y="15" rx="1"></rect>
        <rect width="6" height="6" x="3" y="15" rx="1"></rect>
        <rect width="6" height="6" x="9" y="3" rx="1"></rect>
        <path d="M6 15v-1a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v1"></path>
        <path d="M12 9v3"></path>
      </svg>
}, {
  title: "Order Management & Checkout",
  description: "Process, track, and manage orders with ease. Integrated shipping with real-time tracking for your customers.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
        <circle cx="8" cy="21" r="1"></circle>
        <circle cx="19" cy="21" r="1"></circle>
        <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12"></path>
      </svg>
}, {
  title: "Payment Integration",
  description: "Accept payments via M-Pesa, Tigo Pesa, Airtel Money, PayPal, and Stripe. Manage payouts and sales analytics.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
        <rect width="20" height="14" x="2" y="5" rx="2"></rect>
        <line x1="2" x2="22" y1="10" y2="10"></line>
      </svg>
}, {
  title: "AI Assistant",
  description: "WhatsApp integration with AI-powered customer support, automated responses, and smart order management through your WhatsApp business.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor" className="h-6 w-6">
        <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 ************* 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.890-5.335 11.893-11.893A11.821 11.821 0 0020.891 3.515"/>
      </svg>
}, {
  title: "Marketing & SEO Tools",
  description: "Discount coupons, promo codes, social media integration, and SEO-optimized store pages to boost your sales.",
  icon: <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="h-6 w-6">
        <path d="m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"></path>
        <path d="M5 3v4"></path>
        <path d="M19 17v4"></path>
        <path d="M3 5h4"></path>
        <path d="M17 19h4"></path>
      </svg>
}];
const Features = () => {
  const fadeInUpVariants = {
    hidden: {
      opacity: 0,
      y: 30
    },
    visible: (index: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        delay: 0.1 * index
      }
    })
  };
  const [sectionRef, sectionInView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  });
  return <section id="features" ref={sectionRef} className="py-20 sm:py-24 lg:py-[120px]">
      <div className="container px-4 md:px-6">
        <div className="text-center mb-16">
          <motion.div initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5
        }} className="inline-flex items-center rounded-full bg-accent px-3 py-1 text-sm font-medium text-mduka-700 mb-4">
            <span className="flex h-2 w-2 rounded-full bg-mduka-500 mr-1.5"></span>
            <span>Powerful Features</span>
          </motion.div>
          
          <motion.h2 initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.1
        }} className="text-3xl md:text-4xl font-bold tracking-tight mb-4 text-balance">
            Everything you need to run your <span className="text-gradient">online store</span>
          </motion.h2>
          
          <motion.p initial={{
          opacity: 0,
          y: 20
        }} animate={sectionInView ? {
          opacity: 1,
          y: 0
        } : {
          opacity: 0,
          y: 20
        }} transition={{
          duration: 0.5,
          delay: 0.2
        }} className="text-muted-foreground text-lg max-w-2xl mx-auto text-pretty">
            M-Duka provides all the tools you need to create, manage, and grow your online store, from product management to payment integration.
          </motion.p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => {
          const [itemRef, itemInView] = useInView({
            triggerOnce: true,
            threshold: 0.1
          });
          return <motion.div key={index} ref={itemRef} custom={index} variants={fadeInUpVariants} initial="hidden" animate={itemInView ? "visible" : "hidden"} className="group bg-background border border-border rounded-xl p-6 hover:shadow-subtle transition-all duration-300 hover:-translate-y-1">
                <div className="h-12 w-12 rounded-lg bg-accent flex items-center justify-center mb-5 text-mduka-600 group-hover:bg-mduka-50 group-hover:text-mduka-700 transition-colors duration-300">
                  {feature.icon}
                </div>
                
                <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                
                <p className="text-muted-foreground text-pretty">
                  {feature.description}
                </p>
              </motion.div>;
        })}
        </div>
      </div>
    </section>;
};
export default Features;