-- Create stores table
CREATE TABLE IF NOT EXISTS stores (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    logo TEXT,
    banner_image TEXT,
    store_url TEXT,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    category TEXT,
    theme_id TEXT DEFAULT 'default',
    store_type TEXT CHECK (store_type IN ('physical', 'digital', 'service', 'hybrid')),
    payment_methods TEXT[],
    notifications_email TEXT,
    address TEXT,
    location TEXT,
    about_text TEXT,
    phone TEXT,
    email TEXT,
    business_hours TEXT,
    notifications JSONB DEFAULT '{"orderNotifications": true, "stockNotifications": true, "marketingNotifications": false}'::jsonb,
    whatsapp_settings JSONB DEFAULT '{"businessNumber": "", "enableOrderNotifications": true, "enableCustomerUpdates": true, "customMessage": "", "autoReply": false}'::jsonb,
    workflow_settings JSONB DEFAULT '{"enableAutomation": false, "messageTemplates": [], "orderWorkflows": [], "customerWorkflows": [], "inventoryWorkflows": []}'::jsonb,
    theme_options JSONB DEFAULT '{"darkMode": false, "customHeader": false, "customFooter": false, "colorScheme": "light", "primaryColor": "#10b981", "layoutType": "grid", "displayCurrency": true, "showProductCount": true, "enableNewBadge": true, "enableShareButtons": true}'::jsonb,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create RLS policies
ALTER TABLE stores ENABLE ROW LEVEL SECURITY;

-- Allow users to view their own stores
CREATE POLICY "Users can view their own stores"
    ON stores FOR SELECT
    USING (auth.uid() = owner_id);

-- Allow users to insert their own stores
CREATE POLICY "Users can insert their own stores"
    ON stores FOR INSERT
    WITH CHECK (auth.uid() = owner_id);

-- Allow users to update their own stores
CREATE POLICY "Users can update their own stores"
    ON stores FOR UPDATE
    USING (auth.uid() = owner_id)
    WITH CHECK (auth.uid() = owner_id);

-- Allow users to delete their own stores
CREATE POLICY "Users can delete their own stores"
    ON stores FOR DELETE
    USING (auth.uid() = owner_id);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_stores_updated_at
    BEFORE UPDATE ON stores
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column(); 