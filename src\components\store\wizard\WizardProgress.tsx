
import React from 'react';
import { useStoreWizard } from './StoreWizardContext';
import { steps } from './StoreWizardContext';
import { useIsMobile } from '@/hooks/use-mobile';

const WizardProgress: React.FC = () => {
  const { currentStep } = useStoreWizard();
  const isMobile = useIsMobile();

  if (!isMobile) {
    // Hide on desktop since we have the sidebar
    return null;
  }

  return (
    <div className="mb-4">
      <div className="flex items-center justify-between bg-muted/30 rounded-lg p-2">
        <span className="text-sm font-medium">Step {currentStep + 1} of {steps.length}</span>
        <span className="text-sm font-medium">{steps[currentStep].label}</span>
      </div>
      {/* Simple progress bar */}
      <div className="w-full bg-muted h-1 mt-2 rounded-full">
        <div 
          className="bg-primary h-1 rounded-full transition-all duration-300" 
          style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
        />
      </div>
    </div>
  );
};

export default WizardProgress;
