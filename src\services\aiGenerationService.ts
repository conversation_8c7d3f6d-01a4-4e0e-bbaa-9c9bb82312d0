// AI Content Generation Service
// This service handles AI-powered content generation for the application

import { supabase } from '@/lib/supabase';

/**
 * Request parameters for AI content generation
 */
export interface AIGenerationRequest {
  prompt: string;
  context?: string;
  maxTokens?: number;
  temperature?: number;
  type?: 'description' | 'features' | 'title' | 'seo' | 'general' | 'marketing' | 'faq' | 'pricing';
}

/**
 * Response from AI content generation
 */
export interface AIGenerationResponse {
  content: string;
  isError: boolean;
  errorMessage?: string;
}

// Define OpenAI compatible message format
interface AIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * Generate content using AI
 * @param params Request parameters
 * @returns Generated content
 */
export async function generateAIContent(params: AIGenerationRequest): Promise<AIGenerationResponse> {
  try {
    console.log('Generating AI content with params:', params);
    
    // Construct the system and user messages based on the request type
    const messages: AIMessage[] = preparePrompt(params);
    
    // Try to use Supabase function (preferred method)
    try {
      const response = await callSupabaseAIFunction(messages);
      return {
        content: response,
        isError: false
      };
    } catch (supabaseError) {
      console.warn('Supabase AI function error:', supabaseError);
      console.log('Falling back to direct OpenAI API...');
      
      // Fallback to direct OpenAI API call
      const openAIKey = import.meta.env.VITE_OPENAI_API_KEY;
      if (!openAIKey) {
        throw new Error('OpenAI API key not found in environment variables');
      }
      
      const response = await callOpenAIDirectly(messages, openAIKey);
      return {
        content: response,
        isError: false
      };
    }
  } catch (error) {
    console.error('Error generating AI content:', error);
    return {
      content: '',
      isError: true,
      errorMessage: error instanceof Error ? error.message : 'Failed to generate content'
    };
  }
}

/**
 * Call the Supabase AI function
 */
async function callSupabaseAIFunction(messages: AIMessage[]): Promise<string> {
  const { data, error } = await supabase.functions.invoke('ai-chat', {
    body: { messages },
  });
  
  if (error) {
    throw new Error(`Supabase function error: ${error.message}`);
  }
  
  return data.content || '';
}

/**
 * Call OpenAI API directly if Supabase function is unavailable
 */
async function callOpenAIDirectly(messages: AIMessage[], apiKey: string): Promise<string> {
  const response = await fetch('https://api.openai.com/v1/chat/completions', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${apiKey}`
    },
    body: JSON.stringify({
      model: 'gpt-4-turbo-preview',
      messages,
      max_tokens: 800,
      temperature: 0.7,
    })
  });
  
  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
  }
  
  const data = await response.json();
  return data.choices[0].message.content;
}

/**
 * Prepare prompt based on request type
 */
function preparePrompt(params: AIGenerationRequest): AIMessage[] {
  const { prompt, context, type, temperature = 0.7 } = params;
  
  // Common system prompt structure
  let systemPrompt = 'You are an expert AI assistant specializing in e-commerce and marketing content creation.';
  let userPrompt = '';
  
  // Customize prompts based on generation type
  switch (type) {
    case 'description':
      systemPrompt += ' Create compelling, SEO-friendly store descriptions that highlight unique selling points.';
      userPrompt = `Write a concise, engaging store description for a ${prompt} store.${context ? ` Additional context: ${context}` : ''}`;
      break;
      
    case 'features':
      systemPrompt += ' Create bullet point features that highlight the most important aspects of products or services.';
      userPrompt = `Generate 5-6 key features for a ${prompt} product or service. Format them as bullet points with emojis.`;
      break;
      
    case 'title':
      systemPrompt += ' Create attention-grabbing, concise titles that highlight value proposition.';
      userPrompt = `Create a compelling title for a ${prompt} store or product.`;
      break;
      
    case 'seo':
      systemPrompt += ' Create SEO-optimized descriptions with relevant keywords for better search visibility.';
      userPrompt = `Write an SEO-friendly description for ${prompt}${context ? ` with these keywords: ${context}` : ''}.`;
      break;
    
    case 'marketing':
      systemPrompt += ' Create persuasive marketing copy for promotions, sales and special offers.';
      userPrompt = `Generate marketing copy for ${prompt}${context ? `. Context: ${context}` : ''}.`;
      break;
      
    case 'faq':
      systemPrompt += ' Generate helpful FAQs that address common customer questions about products or services.';
      userPrompt = `Create 5 frequently asked questions with answers for ${prompt}${context ? `. Product info: ${context}` : ''}.`;
      break;
      
    case 'pricing':
      systemPrompt += ' Suggest effective pricing strategies based on market trends and product positioning.';
      userPrompt = `Suggest pricing strategies for ${prompt}${context ? `. Product details: ${context}` : ''}.`;
      break;
      
    case 'general':
    default:
      userPrompt = prompt + (context ? ` ${context}` : '');
      break;
  }
  
  return [
    { role: 'system', content: systemPrompt },
    { role: 'user', content: userPrompt }
  ];
}