import { describe, it, expect } from 'vitest';
import { generateSku } from './skuGenerator';

describe('generateSku', () => {
  it('should generate a SKU with size and color', () => {
    const baseSku = 'TSHIRT001';
    const size = 'L';
    const color = 'Blue';
    const sku = generateSku(baseSku, size, color);
    expect(sku).toBe('TSHIRT001-L-BLU');
  });

  it('should generate a SKU with only size', () => {
    const baseSku = 'TSHIRT001';
    const size = 'M';
    const sku = generateSku(baseSku, size);
    expect(sku).toBe('TSHIRT001-M');
  });

  it('should generate a SKU with only color', () => {
    const baseSku = 'TSHIRT001';
    const color = 'Red';
    const sku = generateSku(baseSku, undefined, color);
    expect(sku).toBe('TSHIRT001-RED');
  });

  it('should return the base SKU if no variants are provided', () => {
    const baseSku = 'TSHIRT001';
    const sku = generateSku(baseSku);
    expect(sku).toBe('TSHIRT001');
  });

  it('should handle N/A values', () => {
    const baseSku = 'TSHIRT001';
    const size = 'N/A';
    const color = 'N/A';
    const sku = generateSku(baseSku, size, color);
    expect(sku).toBe('TSHIRT001');
  });
});
