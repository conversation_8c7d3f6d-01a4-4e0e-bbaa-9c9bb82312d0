
import React from 'react';
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { useDomains } from './hooks/useDomains';
import { AddDomainForm } from './components/AddDomainForm';
import { VerificationInstructions } from './components/VerificationInstructions';
import { DomainsList } from './components/DomainsList';
import { DefaultDomain } from './components/DefaultDomain';
import { DevelopmentModeWarning } from './components/DevelopmentModeWarning';
import { useUIStore } from '@/hooks/useUIStore';

const CustomDomainCard = () => {
  const {
    domains,
    newDomain,
    setNewDomain,
    loading,
    checkingVerification,
    verificationInstructions,
    verificationCode,
    isDomainValid,
    handleAddDomain,
    handleVerifyDomain,
    handleDeleteDomain,
    dismissVerificationInfo,
  } = useDomains();
  
  const { currentStore } = useUIStore();

  return (
    <Card>
      <CardHeader>
        <CardTitle>Custom Domains</CardTitle>
        <CardDescription>
          Connect your own domain to your store
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Default Domain */}
          <DefaultDomain storeUrl={currentStore?.storeUrl || currentStore?.store_url || null} />

          {/* Add Domain Form */}
          <AddDomainForm
            newDomain={newDomain}
            setNewDomain={setNewDomain}
            isDomainValid={isDomainValid}
            loading={loading}
            handleAddDomain={handleAddDomain}
          />
          
          {/* Verification Instructions */}
          {verificationInstructions && verificationCode && (
            <VerificationInstructions
              verificationInstructions={verificationInstructions}
              verificationCode={verificationCode}
              onDismiss={dismissVerificationInfo}
            />
          )}
          
          {/* Development Mode Warning */}
          {currentStore?.id && currentStore.id.includes('mock') && domains.length === 0 && (
            <DevelopmentModeWarning storeId={currentStore.id} />
          )}
          
          {/* Domains List */}
          <DomainsList
            domains={domains}
            checkingVerification={checkingVerification}
            handleVerifyDomain={handleVerifyDomain}
            handleDeleteDomain={handleDeleteDomain}
          />
          
          {/* Info Text */}
          <div className="pt-4 text-sm text-muted-foreground">
            <p>
              Adding a custom domain allows you to use your own domain name for your store instead of
              the default m-duka.app URL.
            </p>
            <p className="mt-2">
              You'll need to create a CNAME record with your domain registrar pointing to 
              <span className="font-mono bg-muted px-1 mx-1 rounded">stores.m-duka.app</span> 
              and then verify domain ownership.
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CustomDomainCard;
