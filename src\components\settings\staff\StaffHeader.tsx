
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusCircle } from 'lucide-react';

interface StaffHeaderProps {
  onAddStaff: () => void;
}

const StaffHeader: React.FC<StaffHeaderProps> = ({ onAddStaff }) => {
  return (
    <div className="flex items-center justify-between">
      <div>
        <h2 className="text-2xl font-bold">Staff Management</h2>
        <p className="text-muted-foreground">
          Manage your store staff and their permissions
        </p>
      </div>
      <Button 
        onClick={onAddStaff}
        className="flex items-center gap-2"
      >
        <PlusCircle className="h-4 w-4" />
        Add Staff
      </Button>
    </div>
  );
};

export default StaffHeader;
