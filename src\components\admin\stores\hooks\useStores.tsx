
import { useState, useEffect } from 'react';
import { AdminStoreType } from '@/types/admin';
import { fetchAllStores, updateStoreStatus } from '@/contexts/admin/api/adminStores';
import { toast } from 'sonner';

export const useStores = () => {
  const [stores, setStores] = useState<AdminStoreType[]>([]);
  const [filteredStores, setFilteredStores] = useState<AdminStoreType[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  
  // Fetch stores data from Supabase
  useEffect(() => {
    const loadStores = async () => {
      setIsLoading(true);
      try {
        const storesData = await fetchAllStores();
        setStores(storesData);
        setFilteredStores(storesData);
      } catch (error) {
        console.error('Error loading stores data:', error);
        toast.error('Failed to load stores data');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadStores();
  }, []);
  
  // Filter stores based on search query
  useEffect(() => {
    if (!searchQuery) {
      setFilteredStores(stores);
      return;
    }
    
    const filtered = stores.filter(store => 
      store.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      store.owner.toLowerCase().includes(searchQuery.toLowerCase()) ||
      store.url.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredStores(filtered);
  }, [searchQuery, stores]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleStoreAction = async (action: string, storeId: string) => {
    const store = stores.find(s => s.id === storeId);
    if (!store) return;
    
    switch (action) {
      case 'view':
        toast.info(`Viewing store: ${store.name}`);
        // In a real implementation, this would navigate to the store details page
        break;
      case 'visit':
        // Open store in a new tab
        if (store.url) {
          window.open(`/${store.url}`, '_blank');
        } else {
          toast.error('Store URL not available');
        }
        break;
      case 'approve':
      case 'reactivate':
        const activateSuccess = await updateStoreStatus(storeId, true);
        if (activateSuccess) {
          setStores(prevStores => 
            prevStores.map(s => 
              s.id === storeId ? { ...s, status: 'active' } : s
            )
          );
        }
        break;
      case 'suspend':
        const suspendSuccess = await updateStoreStatus(storeId, false);
        if (suspendSuccess) {
          setStores(prevStores => 
            prevStores.map(s => 
              s.id === storeId ? { ...s, status: 'suspended' } : s
            )
          );
        }
        break;
      case 'flag':
        toast.info(`Store ${store.name} flagged for review`);
        // In a real implementation, this would mark the store for admin review
        break;
      default:
        console.log(`Action: ${action}, Store ID: ${storeId}`);
    }
  };

  return {
    stores: filteredStores,
    isLoading,
    searchQuery,
    setSearchQuery,
    formatDate,
    handleStoreAction
  };
};
