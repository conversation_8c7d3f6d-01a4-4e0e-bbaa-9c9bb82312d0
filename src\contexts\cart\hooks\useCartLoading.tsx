
import { useState, useEffect } from 'react';
import { CartItem } from '../types';
import { useAuth } from '@/contexts/auth/AuthContext';
import { fetchCartItems } from '../cartApi';

export const useCartLoading = () => {
  const [items, setItems] = useState<CartItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const { user } = useAuth();
  
  // Load cart items when component mounts or user changes
  useEffect(() => {
    const loadCartItems = async () => {
      if (!user) return;
      
      setIsLoading(true);
      try {
        const cartItems = await fetchCartItems(user.id);
        setItems(cartItems);
      } catch (error) {
        console.error('Error loading cart items:', error);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadCartItems();
  }, [user]);
  
  return { items, setItems, isLoading, setIsLoading };
};
