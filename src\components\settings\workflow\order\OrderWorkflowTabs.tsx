
import React from 'react';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import WorkflowRulesTab from './WorkflowRulesTab';
import ActivityTab from './ActivityTab';
import SettingsTab from './SettingsTab';

interface OrderWorkflowTabsProps {
  activeTab: string;
  setActiveTab: (value: string) => void;
  workflowRules: any[];
  setWorkflowRules: React.Dispatch<React.SetStateAction<any[]>>;
  recentActivity: any[];
}

const OrderWorkflowTabs: React.FC<OrderWorkflowTabsProps> = ({
  activeTab,
  setActiveTab,
  workflowRules,
  setWorkflowRules,
  recentActivity
}) => {
  return (
    <Tabs defaultValue="workflows" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="mb-4">
        <TabsTrigger value="workflows">Workflow Rules</TabsTrigger>
        <TabsTrigger value="activity">Recent Activity</TabsTrigger>
        <TabsTrigger value="settings">Settings</TabsTrigger>
      </TabsList>
      
      <TabsContent value="workflows">
        <WorkflowRulesTab workflowRules={workflowRules} setWorkflowRules={setWorkflowRules} />
      </TabsContent>
      
      <TabsContent value="activity">
        <ActivityTab recentActivity={recentActivity} />
      </TabsContent>
      
      <TabsContent value="settings">
        <SettingsTab />
      </TabsContent>
    </Tabs>
  );
};

export default OrderWorkflowTabs;
