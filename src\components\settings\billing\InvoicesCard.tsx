
import React from 'react';
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Receipt } from "lucide-react";

const InvoicesCard = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Receipt className="h-5 w-5" /> 
          Invoices
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-6">
          <Receipt className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
          <h3 className="text-lg font-medium mb-2">No invoices yet</h3>
          <p className="text-sm text-muted-foreground">
            Your invoice history will appear here once you subscribe to a plan
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default InvoicesCard;
