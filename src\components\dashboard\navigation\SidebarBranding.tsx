
import React from "react";
import { Link } from "react-router-dom";
import StoreSelector from "./StoreSelector";

const SidebarBranding = () => {
  return (
    <div className="p-4 border-b">
      <Link to="/dashboard" className="flex items-center gap-2 mb-1">
        <div className="relative">
          <div className="bg-primary rounded-xl text-white px-3 py-2 inline-block shadow-sm">
            M
            {/* SMS-style tail */}
            <div className="absolute -bottom-[6px] left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary rotate-45 rounded-sm"></div>
          </div>
        </div>
        <span className="text-2xl font-bold">-Duka</span>
      </Link>
      <StoreSelector variant="desktop" />
    </div>
  );
};

export default SidebarBranding;
