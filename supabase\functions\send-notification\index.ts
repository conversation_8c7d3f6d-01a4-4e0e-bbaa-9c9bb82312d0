
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

interface NotificationRequest {
  type: "signup" | "subscription";
  userData: {
    name: string;
    email: string;
  };
  adminEmail: string;
}

const handler = async (req: Request): Promise<Response> => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { type, userData, adminEmail }: NotificationRequest = await req.json();

    const subject = type === "signup" 
      ? "New User Registration" 
      : "New Subscription";

    const html = type === "signup"
      ? `
        <h1>New User Registration</h1>
        <p>A new user has registered:</p>
        <ul>
          <li>Name: ${userData.name}</li>
          <li>Email: ${userData.email}</li>
        </ul>
        <p>Time: ${new Date().toLocaleString()}</p>
      `
      : `
        <h1>New Subscription</h1>
        <p>A user has subscribed to your service:</p>
        <ul>
          <li>Name: ${userData.name}</li>
          <li>Email: ${userData.email}</li>
        </ul>
        <p>Time: ${new Date().toLocaleString()}</p>
      `;

    const emailResponse = await resend.emails.send({
      from: "M-Duka <<EMAIL>>",
      to: [adminEmail],
      subject,
      html,
    });

    console.log("Email sent successfully:", emailResponse);

    return new Response(JSON.stringify(emailResponse), {
      status: 200,
      headers: { ...corsHeaders, "Content-Type": "application/json" },
    });
  } catch (error: any) {
    console.error("Error in send-notification function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { ...corsHeaders, "Content-Type": "application/json" },
      }
    );
  }
};

serve(handler);
