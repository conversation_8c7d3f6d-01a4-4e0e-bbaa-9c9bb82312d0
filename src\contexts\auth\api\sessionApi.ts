
import { supabase } from '@/integrations/supabase/client';
import { User } from '../types';
import { UserRole, stringToUserRole } from '@/constants/roles';

/**
 * Fetch user profile from Supabase
 */
export const fetchUserProfile = async (userId: string) => {
  try {
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', userId)
      .single();
    
    if (profileError) {
      console.error("Profile fetch error:", profileError);
      if (profileError.code === 'PGRST116') {
        // No profile found, which might be normal for a new user
        console.log("No profile found for user with ID:", userId);
        return null;
      }
      throw profileError;
    }
    
    if (!profileData) {
      console.log("No profile data found for user");
      return null;
    }
    
    return profileData;
  } catch (error) {
    console.error("Error fetching user profile:", error);
    throw error;
  }
};

/**
 * Get current session and user data
 */
export const getCurrentSession = async (): Promise<{
  session: any | null;
  profile: any | null;
  user: User | null;
}> => {
  try {
    // Get session from Supabase
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError) {
      console.error("Session error:", sessionError);
      return { session: null, profile: null, user: null };
    }
    
    if (!session) {
      console.log("No active session found");
      return { session: null, profile: null, user: null };
    }
    
    console.log("Session found, loading user profile");
    const profileData = await fetchUserProfile(session.user.id);
    
    if (profileData) {
      console.log("Profile data loaded:", profileData);
      const userRole = stringToUserRole(profileData.role || 'store_owner');
      
      const user: User = {
        id: session.user.id,
        email: session.user.email || '',
        name: profileData.name || '',
        role: userRole,
        avatar_url: profileData.avatar_url || null
      };
      
      return { session, profile: profileData, user };
    }
    
    // Even if no profile is found, return basic user info from session
    const user: User = {
      id: session.user.id,
      email: session.user.email || '',
      name: session.user.user_metadata?.name || '',
      role: UserRole.StoreOwner,
      avatar_url: null
    };
    
    return { session, profile: null, user };
  } catch (error) {
    console.error('Error getting current session:', error);
    return { session: null, profile: null, user: null };
  }
};
