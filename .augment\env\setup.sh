#!/bin/bash

# M-duka React/TypeScript/Vite Project Setup Script
set -e

echo "🚀 Setting up M-duka React/TypeScript/Vite development environment..."

# Update system packages
echo "📦 Updating system packages..."
sudo apt-get update -y

# Install Node.js 18 (LTS)
echo "📦 Installing Node.js 18..."
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify Node.js and npm installation
echo "✅ Node.js version: $(node --version)"
echo "✅ npm version: $(npm --version)"

# Navigate to workspace directory
cd /mnt/persist/workspace

# Install project dependencies
echo "📦 Installing project dependencies..."
npm ci

# Add npm global bin to PATH in user profile
echo "🔧 Configuring PATH in ~/.profile..."
echo 'export PATH="$HOME/.npm-global/bin:$PATH"' >> ~/.profile
echo 'export PATH="./node_modules/.bin:$PATH"' >> ~/.profile

# Create npm global directory
mkdir -p ~/.npm-global
npm config set prefix '~/.npm-global'

# Source the profile to update current session
source ~/.profile

echo "✅ Setup completed successfully!"
echo "📋 Project Information:"
echo "   - Language: TypeScript/React"
echo "   - Build Tool: Vite"
echo "   - Package Manager: npm"
echo "   - Test Framework: Vitest"
echo "   - Node.js: $(node --version)"
echo "   - npm: $(npm --version)"