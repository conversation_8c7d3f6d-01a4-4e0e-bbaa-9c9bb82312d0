
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Store, 
  ShoppingCart,
  Settings,
  User,
  LogOut
} from 'lucide-react';
import { useAuth } from '@/contexts';
import { 
  Sidebar, 
  SidebarContent, 
  SidebarHeader, 
  SidebarFooter,
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton,
  SidebarSeparator
} from '@/components/ui/sidebar';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import NavLink from './NavLink';

const AdminSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  
  const handleSignOut = async () => {
    await logout();
    navigate('/signin');
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n) => n[0])
      .join('')
      .toUpperCase();
  };

  const isActive = (path: string) => {
    if (path === '/admin' && location.pathname === '/admin') return true;
    if (path !== '/admin' && location.pathname.includes(path)) return true;
    
    // Check for URL params for tab-specific views
    if (path === '/admin/users' && location.search.includes('tab=users')) return true;
    if (path === '/admin/stores' && location.search.includes('tab=stores')) return true;
    if (path === '/admin/orders' && location.search.includes('tab=orders')) return true;
    if (path === '/admin/settings' && location.search.includes('tab=settings')) return true;
    
    return false;
  };
  
  const userInitials = user?.name ? getInitials(user.name) : 'A';

  // Handler for navigating to tabs
  const handleNavigation = (tab: string) => {
    if (tab === 'dashboard') {
      navigate('/admin');
    } else {
      navigate(`/admin?tab=${tab}`);
    }
  };

  return (
    <Sidebar variant="sidebar" collapsible="icon">
      <SidebarHeader className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-2">
          <span className="text-lg font-semibold">Admin Panel</span>
        </div>
      </SidebarHeader>
      
      <SidebarContent>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Dashboard" 
              isActive={isActive('/admin') && !location.search.includes('tab=')}
              onClick={() => handleNavigation('dashboard')}
            >
              <LayoutDashboard className="h-5 w-5" />
              <span>Dashboard</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Users" 
              isActive={isActive('/admin/users') || location.search.includes('tab=users')}
              onClick={() => handleNavigation('users')}
            >
              <Users className="h-5 w-5" />
              <span>Users</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Stores" 
              isActive={isActive('/admin/stores') || location.search.includes('tab=stores')}
              onClick={() => handleNavigation('stores')}
            >
              <Store className="h-5 w-5" />
              <span>Stores</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Orders" 
              isActive={isActive('/admin/orders') || location.search.includes('tab=orders')}
              onClick={() => handleNavigation('orders')}
            >
              <ShoppingCart className="h-5 w-5" />
              <span>Orders</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Settings" 
              isActive={isActive('/admin/settings') || location.search.includes('tab=settings')}
              onClick={() => handleNavigation('settings')}
            >
              <Settings className="h-5 w-5" />
              <span>Platform Settings</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        <SidebarSeparator className="my-4" />
        
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="My Profile" 
              isActive={isActive('/admin/profile')}
              onClick={() => navigate('/admin/profile')}
            >
              <User className="h-5 w-5" />
              <span>My Profile</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
          
          <SidebarMenuItem>
            <SidebarMenuButton 
              tooltip="Logout" 
              onClick={handleSignOut}
            >
              <LogOut className="h-5 w-5" />
              <span>Logout</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarContent>
      
      <SidebarFooter className="p-4 border-t">
        <div className="flex items-center space-x-3">
          <Avatar className="h-9 w-9">
            <AvatarImage src={user?.avatar_url || ''} alt={user?.name} />
            <AvatarFallback>{userInitials}</AvatarFallback>
          </Avatar>
          <div className="space-y-0.5">
            <p className="text-sm font-medium">{user?.name}</p>
            <p className="text-xs text-muted-foreground">{user?.email}</p>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  );
};

export default AdminSidebar;
