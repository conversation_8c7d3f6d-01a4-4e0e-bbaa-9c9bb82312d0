import { supabase } from '@/integrations/supabase/client';

/**
 * Fix missing user profile by creating it manually
 */
export const createMissingProfile = async (email: string) => {
  try {
    console.log('🔧 Attempting to fix missing profile for:', email);
    
    // First, get the user from auth.users
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return { success: false, error: 'Cannot access user list' };
    }
    
    const authUser = users.find(u => u.email === email);
    
    if (!authUser) {
      return { success: false, error: 'User not found in auth.users' };
    }
    
    console.log('Found auth user:', authUser.id, authUser.email);
    
    // Check if profile already exists
    const { data: existingProfile, error: profileCheckError } = await supabase
      .from('profiles')
      .select('*')
      .eq('user_id', authUser.id)
      .single();
    
    if (existingProfile) {
      console.log('Profile already exists:', existingProfile);
      return { success: true, message: 'Profile already exists' };
    }
    
    // Create the missing profile
    const profileData = {
      user_id: authUser.id,
      email: authUser.email,
      name: authUser.user_metadata?.name || authUser.email?.split('@')[0] || 'User',
      role: authUser.user_metadata?.role || 'store_owner'
    };
    
    console.log('Creating profile with data:', profileData);
    
    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert(profileData)
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating profile:', createError);
      return { success: false, error: createError.message };
    }
    
    console.log('✅ Profile created successfully:', newProfile);
    return { success: true, profile: newProfile };
    
  } catch (error) {
    console.error('Fix profile error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Fix all users missing profiles
 */
export const fixAllMissingProfiles = async () => {
  try {
    console.log('🔧 Fixing all missing profiles...');
    
    // Get all auth users
    const { data: { users }, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('Error fetching users:', usersError);
      return { success: false, error: 'Cannot access user list' };
    }
    
    // Get all existing profiles
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('user_id');
    
    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return { success: false, error: 'Cannot access profiles' };
    }
    
    const existingProfileUserIds = profiles?.map(p => p.user_id) || [];
    
    // Find users without profiles
    const usersWithoutProfiles = users.filter(user => 
      !existingProfileUserIds.includes(user.id)
    );
    
    console.log(`Found ${usersWithoutProfiles.length} users without profiles`);
    
    if (usersWithoutProfiles.length === 0) {
      return { success: true, message: 'All users have profiles' };
    }
    
    // Create profiles for users without them
    const profilesToCreate = usersWithoutProfiles.map(user => ({
      user_id: user.id,
      email: user.email,
      name: user.user_metadata?.name || user.email?.split('@')[0] || 'User',
      role: user.user_metadata?.role || 'store_owner'
    }));
    
    const { data: createdProfiles, error: createError } = await supabase
      .from('profiles')
      .insert(profilesToCreate)
      .select();
    
    if (createError) {
      console.error('Error creating profiles:', createError);
      return { success: false, error: createError.message };
    }
    
    console.log(`✅ Created ${createdProfiles?.length || 0} profiles`);
    return { 
      success: true, 
      created: createdProfiles?.length || 0,
      profiles: createdProfiles 
    };
    
  } catch (error) {
    console.error('Fix all profiles error:', error);
    return { success: false, error: error.message };
  }
};

/**
 * Simple profile creation without admin access
 */
export const createProfileSimple = async (email: string, name: string, role: string = 'store_owner') => {
  try {
    // Get current user session
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return { success: false, error: 'No authenticated user' };
    }
    
    if (user.email !== email) {
      return { success: false, error: 'Email mismatch' };
    }
    
    // Try to create profile for current user
    const { data: profile, error: createError } = await supabase
      .from('profiles')
      .insert({
        user_id: user.id,
        email: user.email,
        name: name,
        role: role
      })
      .select()
      .single();
    
    if (createError) {
      console.error('Error creating profile:', createError);
      return { success: false, error: createError.message };
    }
    
    console.log('✅ Profile created:', profile);
    return { success: true, profile };
    
  } catch (error) {
    console.error('Simple profile creation error:', error);
    return { success: false, error: error.message };
  }
};
