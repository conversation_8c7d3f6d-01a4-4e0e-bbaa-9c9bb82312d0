
import { useState, useEffect } from 'react';
import { User } from '@/contexts/auth/types';
import { getUserProfile } from '@/contexts/auth/api/profileApi';

export const useProfileTabState = (user: User | null) => {
  const [userName, setUserName] = useState(user?.name || '');
  const [userPhone, setUserPhone] = useState('');
  const [userBio, setUserBio] = useState('');
  const [avatarUrl, setAvatarUrl] = useState(user?.avatar_url || '');
  const [receiveMarketingEmails, setReceiveMarketingEmails] = useState(true);
  const [receiveOrderUpdates, setReceiveOrderUpdates] = useState(true);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);

  useEffect(() => {
    const fetchProfile = async () => {
      if (user?.id) {
        try {
          const profile = await getUserProfile(user.id);
          if (profile) {
            setUserName(profile.name || '');
            setUserPhone(''); // Phone not available in current profile schema
            setUserBio(''); // Bio not available in current profile schema
            setAvatarUrl(profile.avatar_url || '');
          }
        } catch (error) {
          console.error('Error fetching profile:', error);
        }
      }
    };
    
    fetchProfile();
  }, [user?.id]);

  return {
    userName,
    setUserName,
    userPhone,
    setUserPhone,
    userBio,
    setUserBio,
    avatarUrl,
    setAvatarUrl,
    receiveMarketingEmails,
    setReceiveMarketingEmails,
    receiveOrderUpdates,
    setReceiveOrderUpdates,
    twoFactorEnabled,
    setTwoFactorEnabled,
  };
};
