
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowLeft, ArrowRight } from 'lucide-react';
import { useStoreWizard } from './StoreWizardContext';
import { steps } from './StoreWizardContext';

const WizardNavigation: React.FC = () => {
  const { currentStep, isSubmitting, handleNext, handlePrevious, handleSubmit } = useStoreWizard();
  const navigate = useNavigate();

  return (
    <div className="flex justify-between mt-8">
      <Button
        variant="outline"
        onClick={() => navigate('/dashboard')}
        disabled={isSubmitting}
      >
        Cancel
      </Button>
      
      <div className="flex gap-2">
        {currentStep > 0 && (
          <Button 
            variant="outline" 
            onClick={handlePrevious}
            disabled={isSubmitting}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Previous
          </Button>
        )}
        
        {currentStep < steps.length - 1 ? (
          <Button 
            onClick={handleNext} 
            disabled={isSubmitting}
          >
            Next
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        ) : (
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitting}
            className="min-w-[120px]"
          >
            {isSubmitting ? (
              <>Creating Store...</>
            ) : (
              <>Create Store</>
            )}
          </Button>
        )}
      </div>
    </div>
  );
};

export default WizardNavigation;
