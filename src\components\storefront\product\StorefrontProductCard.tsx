import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ShoppingCart, MessageCircle, Heart, Star, AlertCircle } from 'lucide-react';
import { Product } from '@/types/unified-product';
import { formatCurrency } from '@/utils/formatters';
import { useCart } from '@/contexts/cart/CartContext';
import { useWishlistProvider } from '@/contexts/wishlist/useWishlistProvider';

interface StorefrontProductCardProps {
  product: Product;
  onWhatsAppOrder?: (product: Product) => void;
}

const StorefrontProductCard: React.FC<StorefrontProductCardProps> = ({ 
  product, 
  onWhatsAppOrder 
}) => {
  const { addToCart } = useCart();
  const { addToWishlist, isInWishlist } = useWishlistProvider();
  
  const handleAddToCart = () => {
    addToCart(product, 1);
  };

  const handleAddToWishlist = () => {
    addToWishlist(product);
  };

  const handleWhatsAppOrder = () => {
    if (onWhatsAppOrder) {
      onWhatsAppOrder(product);
    } else {
      // Default WhatsApp message
      const message = `Hi! I'm interested in "${product.name}" for ${formatCurrency(product.price, product.currency)}. Can you provide more details?`;
      const whatsappUrl = `https://wa.me/254700000000?text=${encodeURIComponent(message)}`;
      window.open(whatsappUrl, '_blank');
    }
  };

  const isOutOfStock = (product.stock_quantity || 0) <= 0;
  const isLowStock = (product.stock_quantity || 0) <= 5 && !isOutOfStock;

  return (
    <Card className="h-full flex flex-col hover:shadow-lg transition-shadow relative">
      <CardHeader className="p-0">
        <div className="relative">
          {product.images && product.images.length > 0 ? (
            <img 
              src={product.images[0]} 
              alt={product.name} 
              className="w-full h-48 object-cover rounded-t-lg"
            />
          ) : (
            <div className="w-full h-48 bg-gray-200 flex items-center justify-center rounded-t-lg">
              <span className="text-gray-400">No Image</span>
            </div>
          )}
          
          {/* Wishlist button */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute top-2 right-2 bg-white/80 hover:bg-white"
            onClick={handleAddToWishlist}
          >
            <Heart 
              className={`h-4 w-4 ${isInWishlist(product.id || '') ? 'fill-red-500 text-red-500' : 'text-gray-600'}`} 
            />
          </Button>

          {/* Out of stock badge */}
          {isOutOfStock && (
            <Badge className="absolute top-2 left-2 bg-red-500">
              Out of Stock
            </Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-4 flex-grow">
        <h3 className="text-lg font-semibold mb-2 line-clamp-2">{product.name}</h3>
        
        {/* Price */}
        <div className="mb-2">
          <p className="text-2xl font-bold text-green-600">
            {formatCurrency(product.price, product.currency)}
          </p>
        </div>
        
        {/* Description */}
        {product.description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-3">
            {product.description}
          </p>
        )}

        {/* Stock warning */}
        {isLowStock && (
          <div className="mb-3 flex items-center text-amber-600 text-sm">
            <AlertCircle className="h-4 w-4 mr-1" />
            Only {product.stock_quantity} left in stock
          </div>
        )}

        {/* Category */}
        {product.category && (
          <div className="mb-2">
            <Badge variant="outline" className="text-xs">
              {product.category}
            </Badge>
          </div>
        )}

        {/* Rating placeholder - can be implemented later */}
        <div className="flex items-center mb-2">
          <div className="flex items-center">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="ml-1 text-sm font-medium">4.5</span>
          </div>
          <span className="text-sm text-gray-500 ml-2">(12 reviews)</span>
        </div>
      </CardContent>
      
      <CardFooter className="flex flex-col space-y-2 p-4">
        {/* Add to Cart Button */}
        <Button 
          onClick={handleAddToCart}
          disabled={isOutOfStock}
          className="w-full bg-green-600 hover:bg-green-700 disabled:bg-gray-300"
        >
          <ShoppingCart className="h-4 w-4 mr-2" />
          {isOutOfStock ? 'Out of Stock' : 'Add to Cart'}
        </Button>
        
        {/* WhatsApp Order Button */}
        <Button 
          variant="outline" 
          onClick={handleWhatsAppOrder}
          className="w-full border-green-600 text-green-600 hover:bg-green-50"
        >
          <MessageCircle className="h-4 w-4 mr-2" />
          Order via WhatsApp
        </Button>
      </CardFooter>
    </Card>
  );
};

export default StorefrontProductCard; 