
import { serve } from 'https://deno.land/std@0.192.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.36.0';

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') || '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
};

// Handle requests
serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // Validate content type for non-OPTIONS requests
    const contentType = req.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      return new Response(
        JSON.stringify({
          error: 'Invalid content type. Expected application/json',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }

    const { orderId, userId } = await req.json();
    
    // Validate required parameters
    if (!orderId || !userId) {
      console.error('Missing required parameters:', { orderId, userId });
      return new Response(
        JSON.stringify({
          error: 'Missing required parameters',
          details: 'Both orderId and userId must be provided',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 400,
        }
      );
    }
    
    console.log(`Checking payment status for order ${orderId} and user ${userId}`);
    
    // Check payment status in database
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('status')
      .eq('id', orderId)
      .eq('user_id', userId)
      .maybeSingle();
    
    if (orderError) {
      console.error('Database error when fetching order:', orderError);
      throw orderError;
    }
    
    if (!order) {
      console.log(`Order not found: ${orderId}`);
      return new Response(
        JSON.stringify({
          error: 'Order not found',
          details: 'No order exists with the provided ID for this user',
        }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 404,
        }
      );
    }
    
    // Get transaction details
    const { data: transaction, error: txError } = await supabase
      .from('payment_transactions')
      .select('status, transaction_id, payment_method, updated_at')
      .eq('order_id', orderId)
      .eq('user_id', userId)
      .maybeSingle();
    
    if (txError && txError.code !== 'PGRST116') {
      console.error('Database error when fetching transaction:', txError);
      throw txError;
    }
    
    console.log(`Payment status retrieved successfully for order ${orderId}`);
    
    return new Response(
      JSON.stringify({
        orderStatus: order.status,
        paymentStatus: transaction ? transaction.status : 'not_found',
        transactionId: transaction ? transaction.transaction_id : null,
        paymentMethod: transaction ? transaction.payment_method : null,
        lastUpdated: transaction ? transaction.updated_at : null,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    );
  } catch (error) {
    console.error('Error checking payment status:', error);
    
    return new Response(
      JSON.stringify({
        error: 'Internal Server Error',
        details: error.message,
        timestamp: new Date().toISOString(),
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      }
    );
  }
});
