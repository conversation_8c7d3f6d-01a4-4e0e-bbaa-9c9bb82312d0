
import React, { useState, useEffect } from 'react';
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Check, ChevronsUpDown, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

// Predefined categories
const PREDEFINED_CATEGORIES = [
  { value: 'electronics', label: 'Electronics' },
  { value: 'clothing', label: 'Clothing & Apparel' },
  { value: 'home', label: 'Home & Garden' },
  { value: 'beauty', label: 'Beauty & Personal Care' },
  { value: 'sports', label: 'Sports & Outdoors' },
  { value: 'toys', label: 'Toys & Games' },
  { value: 'books', label: 'Books & Media' },
  { value: 'automotive', label: 'Automotive' },
  { value: 'health', label: 'Health & Wellness' },
  { value: 'food', label: 'Food & Groceries' },
  { value: 'pets', label: 'Pet Supplies' },
  { value: 'jewelry', label: 'Jewelry & Accessories' },
  { value: 'art', label: 'Art & Collectibles' },
  { value: 'office', label: 'Office Supplies' },
  { value: 'baby', label: 'Baby & Kids' },
  { value: 'other', label: 'Other' },
];

interface CategorySelectorProps {
  value: string;
  onChange: (value: string) => void;
  customCategories?: Array<{ value: string; label: string }>;
}

const CategorySelector: React.FC<CategorySelectorProps> = ({
  value,
  onChange,
  customCategories = []
}) => {
  const [open, setOpen] = useState(false);
  const [customCategory, setCustomCategory] = useState('');
  const [allCategories, setAllCategories] = useState([...PREDEFINED_CATEGORIES, ...customCategories]);

  useEffect(() => {
    // Merge predefined categories with custom categories, avoiding duplicates
    const mergedCategories = [...PREDEFINED_CATEGORIES];
    
    customCategories.forEach(custom => {
      if (!mergedCategories.some(cat => cat.value === custom.value)) {
        mergedCategories.push(custom);
      }
    });
    
    setAllCategories(mergedCategories);
  }, [customCategories]);

  const handleAddCustomCategory = () => {
    if (!customCategory.trim()) return;
    
    const formattedValue = customCategory.toLowerCase().replace(/\s+/g, '_');
    const newCategory = { 
      value: formattedValue, 
      label: customCategory.trim() 
    };
    
    setAllCategories(prev => {
      if (prev.some(cat => cat.value === formattedValue)) {
        return prev;
      }
      return [...prev, newCategory];
    });
    
    onChange(formattedValue);
    setCustomCategory('');
    setOpen(false);
  };

  const selectedCategory = allCategories.find(cat => cat.value === value);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between"
        >
          {value ? (
            <span>{selectedCategory?.label || value}</span>
          ) : (
            <span className="text-muted-foreground">Select category</span>
          )}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0">
        <Command>
          <CommandInput 
            placeholder="Search or add custom category..." 
            value={customCategory}
            onValueChange={setCustomCategory}
          />
          <CommandList>
            <CommandEmpty className="py-2 px-1">
              <Button 
                variant="ghost" 
                className="w-full justify-start text-left"
                onClick={handleAddCustomCategory}
              >
                <Plus className="mr-2 h-4 w-4" />
                Add "{customCategory}"
              </Button>
            </CommandEmpty>
            <CommandGroup>
              {allCategories.map((category) => (
                <CommandItem
                  key={category.value}
                  value={category.value}
                  onSelect={() => {
                    onChange(category.value);
                    setOpen(false);
                  }}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      value === category.value ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {category.label}
                  {!PREDEFINED_CATEGORIES.some(c => c.value === category.value) && (
                    <Badge variant="outline" className="ml-2 text-xs">Custom</Badge>
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default CategorySelector;
