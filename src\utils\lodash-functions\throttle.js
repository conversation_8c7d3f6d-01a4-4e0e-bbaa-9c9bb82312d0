
/**
 * Creates a throttled function that only invokes func at most once per
 * every wait milliseconds.
 *
 * @param {Function} func - The function to throttle
 * @param {number} wait - The number of milliseconds to throttle invocations to
 * @param {Object} options - The options object
 * @returns {Function} Returns the new throttled function
 */
function throttle(func, wait, options = {}) {
  let timeoutId;
  let lastArgs;
  let lastCallTime = 0;
  const leading = options.leading ?? true;
  const trailing = options.trailing ?? true;
  
  function invoke(thisArg, args) {
    lastCallTime = Date.now();
    func.apply(thisArg, args);
  }
  
  return function(...args) {
    const now = Date.now();
    const remaining = wait - (now - lastCallTime);
    
    lastArgs = args;
    
    if (remaining <= 0) {
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
      invoke(this, args);
    } else if (!timeoutId && trailing) {
      timeoutId = setTimeout(() => {
        const shouldInvoke = leading ? Date.now() - lastCallTime >= wait : true;
        if (shouldInvoke && lastArgs) {
          invoke(this, lastArgs);
        }
        timeoutId = null;
        lastArgs = null;
      }, remaining);
    }
  };
}

// Support both ESM and CJS
export default throttle;
