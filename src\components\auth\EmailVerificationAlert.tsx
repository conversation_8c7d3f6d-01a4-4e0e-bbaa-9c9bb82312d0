
import React from "react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { useLocation } from "react-router-dom";

const EmailVerificationAlert: React.FC = () => {
  const location = useLocation();
  // Check if we have a pending verification (from URL parameters)
  const urlParams = new URLSearchParams(location.search);
  const verificationStatus = urlParams.get('verification');
  const email = urlParams.get('email');
  
  // Check if there was a verification error
  const showAlert = verificationStatus === 'pending' || verificationStatus === 'error';
  
  if (!showAlert) {
    return null;
  }

  return (
    <Alert 
      variant={verificationStatus === 'error' ? "destructive" : "default"}
      className="mb-6 bg-amber-50 border-amber-200"
    >
      <AlertCircle className="h-4 w-4 mr-2 text-amber-600" />
      <AlertDescription>
        {verificationStatus === 'pending' ? (
          <>
            <strong>Email verification required.</strong>
            <p className="text-sm mt-1">
              {email ? `We sent a verification link to ${email}.` : 'We sent a verification link to your email.'}
              Please check your inbox and follow the link to verify your account.
            </p>
            <p className="text-sm mt-2">
              If you don't see the email, please check your spam folder. 
              The verification link may not work if clicked from some email clients - 
              try opening the link in a new browser tab instead.
            </p>
          </>
        ) : (
          <>
            <strong>Verification failed.</strong>
            <p className="text-sm mt-1">
              There was a problem verifying your email. The link may have expired. 
              Please try signing in or signing up again to receive a new verification link.
            </p>
          </>
        )}
      </AlertDescription>
    </Alert>
  );
};

export default EmailVerificationAlert;
