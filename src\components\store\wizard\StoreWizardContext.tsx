
import React, { createContext, useContext, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useStore } from '@/contexts';
import { StoreFormData } from '@/types/store';
import { toast } from 'sonner';
import { ArrowLeft, ArrowRight, Store as StoreIcon, Palette, Globe, CreditCard, Mail, CheckCircle } from 'lucide-react';
import BasicInfoStep from './BasicInfoStep';
import CategoryStep from './CategoryStep';
import ThemeStep from './ThemeStep';
import PaymentStep from './PaymentStep';
import NotificationsStep from './NotificationsStep';
import SummaryStep from './SummaryStep';

export const steps = [
  { id: 'basic-info', label: 'Basic Info', icon: StoreIcon },
  { id: 'category', label: 'Category', icon: Globe },
  { id: 'theme', label: 'Theme', icon: Palette },
  { id: 'payment', label: 'Payment', icon: CreditCard },
  { id: 'notifications', label: 'Notifications', icon: Mail },
  { id: 'summary', label: 'Summary', icon: CheckCircle },
];

type StoreWizardContextType = {
  currentStep: number;
  formData: StoreFormData;
  isSubmitting: boolean;
  handleNext: () => void;
  handlePrevious: () => void;
  handleSubmit: () => Promise<void>;
  updateFormData: (stepData: Partial<StoreFormData>) => void;
  renderStep: () => React.ReactNode;
};

const StoreWizardContext = createContext<StoreWizardContextType | undefined>(undefined);

export const StoreWizardProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [formData, setFormData] = useState<StoreFormData>({
    name: '',
    description: '',
    storeUrl: '',
    category: '',
    themeId: 'default',
    paymentMethods: ['credit-card'],
    notificationsEmail: '',
    notifications: {
      orderNotifications: true,
      stockNotifications: true,
      marketingNotifications: false,
    },
    themeOptions: {
      darkMode: false,
      customHeader: false,
      customFooter: false,
      primaryColor: '#10b981',
      colorScheme: 'light',
      layoutType: 'grid',
      displayCurrency: true,
      showProductCount: true,
      enableNewBadge: true,
      enableShareButtons: true,
    },
    storeType: 'physical',
    whatsappSettings: {
      businessNumber: '',
      enableOrderNotifications: true,
      enableCustomerUpdates: true,
      customMessage: 'Thank you for your order! We will keep you updated on its status.',
      autoReply: true
    },
    workflowSettings: {
      enableAutomation: true,
      messageTemplates: [],
      orderWorkflows: [],
      customerWorkflows: []
    }
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { createStore } = useStore();
  const navigate = useNavigate();

  const updateFormData = (stepData: Partial<StoreFormData>) => {
    setFormData(prev => ({ ...prev, ...stepData }));
  };

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
      window.scrollTo(0, 0);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleSubmit = async () => {
    // Validate required fields
    if (!formData.name || !formData.description) {
      toast.error('Please fill in all required fields');
      setCurrentStep(0); // Go to basic info step
      return;
    }

    setIsSubmitting(true);
    try {
      const store = await createStore(formData);
      toast.success('Store created successfully!');
      navigate('/dashboard');
    } catch (error: any) {
      console.error('Error creating store:', error);
      toast.error(error.message || 'Failed to create store. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return <BasicInfoStep data={formData} updateData={updateFormData} />;
      case 1:
        return <CategoryStep data={formData} updateData={updateFormData} />;
      case 2:
        return <ThemeStep data={formData} updateData={updateFormData} />;
      case 3:
        return <PaymentStep data={formData} updateData={updateFormData} />;
      case 4:
        return <NotificationsStep data={formData} updateData={updateFormData} />;
      case 5:
        return <SummaryStep data={formData} />;
      default:
        return <BasicInfoStep data={formData} updateData={updateFormData} />;
    }
  };

  return (
    <StoreWizardContext.Provider
      value={{
        currentStep,
        formData,
        isSubmitting,
        handleNext,
        handlePrevious,
        handleSubmit,
        updateFormData,
        renderStep,
      }}
    >
      {children}
    </StoreWizardContext.Provider>
  );
};

export const useStoreWizard = () => {
  const context = useContext(StoreWizardContext);
  if (!context) {
    throw new Error('useStoreWizard must be used within a StoreWizardProvider');
  }
  return context;
};
