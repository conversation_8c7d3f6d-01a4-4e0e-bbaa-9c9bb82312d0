
import React from 'react';
import { Container } from "@/components/ui/container";
import Footer from "@/components/Footer";
import { Helmet } from "react-helmet-async";
import { Separator } from "@/components/ui/separator";
import { Link } from "react-router-dom";

const PrivacyPolicy = () => {
  return (
    <>
      <Helmet>
        <title>Privacy Policy - M-Duka</title>
        <meta name="description" content="M-Duka privacy policy and data protection practices." />
      </Helmet>
      <div className="min-h-screen bg-background">
        <main className="py-12 space-y-8">
          <Container>
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl font-bold tracking-tight mb-6">Privacy Policy</h1>
              <p className="text-muted-foreground mb-8">Last updated: April 25, 2025</p>

              <div className="prose prose-gray max-w-none space-y-8">
                <section>
                  <h2 className="text-2xl font-semibold mb-4">1. Information We Collect</h2>
                  <p className="text-base leading-7 mb-4">We collect several types of information, including:</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Personal identification information (name, email, phone number)</li>
                    <li>Business information (company details, tax numbers)</li>
                    <li>Payment information</li>
                    <li>Usage data and analytics</li>
                    <li>Technical data (IP address, browser type, device information)</li>
                  </ul>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">2. How We Use Your Information</h2>
                  <p className="text-base leading-7 mb-4">We use your information to:</p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Provide and maintain our services</li>
                    <li>Process your transactions</li>
                    <li>Send service updates and marketing communications</li>
                    <li>Improve our platform and user experience</li>
                    <li>Comply with legal obligations</li>
                  </ul>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">3. Data Protection</h2>
                  <p className="text-base leading-7">
                    We implement appropriate security measures to protect your personal information from 
                    unauthorized access, alteration, disclosure, or destruction. These measures include 
                    encryption, secure socket layer technology (SSL), and regular security assessments.
                  </p>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">4. Information Sharing</h2>
                  <p className="text-base leading-7 mb-4">
                    We may share your information with:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Service providers and business partners</li>
                    <li>Payment processors</li>
                    <li>Legal authorities when required by law</li>
                  </ul>
                </section>

                <Separator className="my-8" />

                <section>
                  <h2 className="text-2xl font-semibold mb-4">5. Your Rights</h2>
                  <p className="text-base leading-7 mb-4">
                    You have the right to:
                  </p>
                  <ul className="list-disc pl-6 space-y-2">
                    <li>Access your personal data</li>
                    <li>Correct inaccurate data</li>
                    <li>Request deletion of your data</li>
                    <li>Object to data processing</li>
                    <li>Data portability</li>
                  </ul>
                </section>

                <section className="mt-12 p-6 bg-muted rounded-lg">
                  <h2 className="text-xl font-semibold mb-4">Contact Our Data Protection Officer</h2>
                  <p className="text-base leading-7">
                    For privacy-related inquiries, contact our Data Protection <NAME_EMAIL>
                  </p>
                </section>
              </div>
              
              <div className="mt-8 flex justify-center">
                <Link to="/" className="text-primary hover:underline">
                  Return to Home
                </Link>
              </div>
            </div>
          </Container>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default PrivacyPolicy;
