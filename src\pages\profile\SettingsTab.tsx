
import React from "react";
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardDescription, 
  Card<PERSON><PERSON><PERSON>, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const SettingsTab: React.FC = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Account Settings</CardTitle>
        <CardDescription>
          Manage your account settings and preferences.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div className="flex flex-col">
            <h3 className="font-medium mb-2">Email Notifications</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Configure how you receive email notifications.
            </p>
            <Button variant="outline" className="w-full md:w-auto">
              Manage Email Preferences
            </Button>
          </div>
          
          <div className="border-t pt-6">
            <h3 className="font-medium mb-2">Account Management</h3>
            <p className="text-sm text-destructive mb-4">
              These actions are irreversible. Please be certain.
            </p>
            <div className="flex gap-2">
              <Button variant="destructive" className="w-full md:w-auto">
                Delete Account
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SettingsTab;
