
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Container } from "@/components/ui/container";
import { Button } from "@/components/ui/button";
import { 
  Breadcrumb, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbList,
  BreadcrumbPage, 
  BreadcrumbSeparator 
} from "@/components/ui/breadcrumb";
import { formatCurrency } from "@/utils/formatters";
import { useCart, useAuth } from "@/contexts";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info, CheckCircle, Loader2 } from "lucide-react";
import CheckoutSummary from "@/components/storefront/checkout/CheckoutSummary";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Address } from "@/types/order";
import CheckoutStepper from "@/components/storefront/checkout/CheckoutStepper";
import ShippingStep from "@/components/storefront/checkout/steps/ShippingStep";
import PaymentStep from "@/components/storefront/checkout/steps/PaymentStep";
import ReviewStep from "@/components/storefront/checkout/steps/ReviewStep";

const CHECKOUT_STEPS = ["Shipping", "Payment", "Review"];

const Checkout: React.FC = () => {
  const navigate = useNavigate();
  const { items, subtotal, shipping, total, clearCart } = useCart();
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  
  // Step state
  const [currentStep, setCurrentStep] = useState(0);
  
  // Form data
  const [shippingAddress, setShippingAddress] = useState<Address>({});
  const [paymentMethod, setPaymentMethod] = useState<string>("mpesa");
  
  // Order state
  const [paymentStatus, setPaymentStatus] = useState<"idle" | "processing" | "success" | "error">("idle");
  const [orderId, setOrderId] = useState<string | null>(null);
  
  // Redirect to cart if it's empty
  useEffect(() => {
    if (items.length === 0) {
      navigate("/shop/cart");
    }
  }, [items, navigate]);
  
  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      toast({
        title: "Authentication required",
        description: "Please sign in to continue with checkout.",
        variant: "destructive",
      });
      navigate("/sign-in?redirect=/shop/checkout");
    }
  }, [isAuthenticated, navigate, toast]);
  
  const handleShippingSubmit = (data: Address) => {
    setShippingAddress(data);
    setCurrentStep(1);
  };
  
  const createOrder = async () => {
    if (!user) return null;
    
    try {
      // Map cart items to order items format
      const orderItems = items.map(item => ({
        product_id: item.product.id,
        name: item.product.name,
        price: item.product.price,
        quantity: item.quantity,
        variant_id: item.variant?.id || null
      }));
      
      // Get first store_id from items (assuming all items are from same store)
      const storeId = items[0]?.product.store_id;
      
      // Create order in database - Convert Address to JSON
      const { data, error } = await supabase
        .from('orders')
        .insert({
          customer_id: user.id,
          store_id: storeId,
          status: 'pending',
          total_amount: total,
          payment_method: paymentMethod,
          shipping_address: JSON.parse(JSON.stringify(shippingAddress))
        })
        .select()
        .single();
      
      if (error) throw error;
      
      return data.id;
    } catch (error) {
      console.error('Error creating order:', error);
      return null;
    }
  };
  
  const createPaymentTransaction = async (orderId: string) => {
    if (!user) return false;
    
    try {
      const { error } = await supabase
        .from('payment_transactions')
        .insert({
          order_id: orderId,
          amount: total,
          payment_method: paymentMethod,
          status: 'processing'
        });
      
      if (error) throw error;
      
      return true;
    } catch (error) {
      console.error('Error creating payment transaction:', error);
      return false;
    }
  };
  
  const updateOrderStatus = async (orderId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('orders')
        .update({ status })
        .eq('id', orderId);
      
      if (error) throw error;
      
      return true;
    } catch (error) {
      console.error('Error updating order status:', error);
      return false;
    }
  };
  
  const checkPaymentStatus = async (orderId: string, userId: string) => {
    try {
      const response = await fetch(`${window.location.origin}/functions/check-payment-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId, userId }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to check payment status');
      }
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error checking payment status:', error);
      return null;
    }
  };
  
  const handleProcessPayment = async () => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to continue with checkout.",
        variant: "destructive",
      });
      return;
    }
    
    setPaymentStatus("processing");
    
    try {
      // Create order in database
      const newOrderId = await createOrder();
      
      if (!newOrderId) {
        throw new Error("Failed to create order");
      }
      
      setOrderId(newOrderId);
      
      // Create payment transaction
      const transactionCreated = await createPaymentTransaction(newOrderId);
      
      if (!transactionCreated) {
        throw new Error("Failed to create payment transaction");
      }
      
      // In a real application, here we would redirect to the payment gateway
      // For now, we'll simulate a successful payment after a delay
      setTimeout(async () => {
        // Update order status
        await updateOrderStatus(newOrderId, 'paid');
        
        setPaymentStatus("success");
        toast({
          title: "Payment successful",
          description: "Your order has been placed successfully.",
        });
        
        // Clear cart and navigate to confirmation page
        setTimeout(() => {
          clearCart();
          navigate(`/shop/order-confirmation?order_id=${newOrderId}`);
        }, 2000);
      }, 2000);
    } catch (error) {
      console.error('Error processing payment:', error);
      setPaymentStatus("error");
      toast({
        title: "Payment failed",
        description: "There was an error processing your payment. Please try again.",
        variant: "destructive",
      });
    }
  };
  
  if (!isAuthenticated) {
    return (
      <Container className="py-12">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="mb-4">Please sign in to continue with checkout.</p>
          <Button onClick={() => navigate("/sign-in?redirect=/shop/checkout")}>
            Sign In
          </Button>
        </div>
      </Container>
    );
  }
  
  return (
    <Container className="py-12">
      {/* Breadcrumbs */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/shop">Home</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink href="/shop/cart">Cart</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Checkout</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>
      
      <h1 className="text-3xl font-bold mb-8">Checkout</h1>
      
      {/* Checkout Steps */}
      <CheckoutStepper currentStep={currentStep} steps={CHECKOUT_STEPS} />
      
      {paymentStatus === "success" ? (
        <Alert className="mb-6 border-green-200 bg-green-50">
          <CheckCircle className="h-5 w-5 text-green-600" />
          <AlertTitle className="text-green-800">Payment successful!</AlertTitle>
          <AlertDescription className="text-green-700">
            Your payment has been processed successfully. Redirecting to order confirmation...
          </AlertDescription>
        </Alert>
      ) : paymentStatus === "error" ? (
        <Alert className="mb-6" variant="destructive">
          <Info className="h-5 w-5" />
          <AlertTitle>Payment failed</AlertTitle>
          <AlertDescription>
            There was an error processing your payment. Please try again.
          </AlertDescription>
        </Alert>
      ) : paymentStatus === "processing" ? (
        <Alert className="mb-6 border-blue-200 bg-blue-50">
          <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />
          <AlertTitle className="text-blue-800">Processing payment...</AlertTitle>
          <AlertDescription className="text-blue-700">
            Please wait while we process your payment. Do not refresh the page.
          </AlertDescription>
        </Alert>
      ) : null}
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Checkout Steps Content */}
        <div className="lg:col-span-2">
          {currentStep === 0 && (
            <ShippingStep 
              onNext={handleShippingSubmit}
              initialData={shippingAddress}
            />
          )}
          
          {currentStep === 1 && (
            <PaymentStep
              paymentMethod={paymentMethod}
              onSelectMethod={setPaymentMethod}
              onPrev={() => setCurrentStep(0)}
              onNext={() => setCurrentStep(2)}
              isProcessing={paymentStatus === "processing"}
            />
          )}
          
          {currentStep === 2 && (
            <ReviewStep
              shippingAddress={shippingAddress}
              paymentMethod={paymentMethod}
              onPrev={() => setCurrentStep(1)}
              onPlaceOrder={handleProcessPayment}
              isProcessing={paymentStatus === "processing"}
            />
          )}
        </div>
        
        {/* Order Summary */}
        <div>
          <CheckoutSummary 
            subtotal={subtotal} 
            shipping={shipping} 
            total={total} 
            paymentMethod={paymentMethod}
            paymentStatus={paymentStatus}
            onProcessPayment={currentStep === 2 ? handleProcessPayment : undefined}
          />
        </div>
      </div>
    </Container>
  );
};

export default Checkout;
