
import React from 'react';
import { Layers, Package, Percent, Tag } from 'lucide-react';
import SidebarGroup from './SidebarGroup';
import SidebarItem from './SidebarItem';

interface ProductsGroupProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  isActive: boolean;
  isProductsActive: boolean;
  isCategoriesActive: boolean;
  isDiscountsActive: boolean;
}

const ProductsGroup: React.FC<ProductsGroupProps> = ({
  isOpen,
  onOpenChange,
  isActive,
  isProductsActive,
  isCategoriesActive,
  isDiscountsActive
}) => {
  return (
    <SidebarGroup
      title="Products"
      icon={<Package className="h-5 w-5 mr-2" />}
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      isActive={isActive}
    >
      <SidebarItem
        icon={<Layers className="h-5 w-5" />}
        label="All Products"
        href="/products"
        active={isProductsActive}
      />
      <SidebarItem
        icon={<Tag className="h-5 w-5" />}
        label="Categories"
        href="/categories"
        active={isCategoriesActive}
      />
      <SidebarItem
        icon={<Percent className="h-5 w-5" />}
        label="Discounts"
        href="/discounts"
        active={isDiscountsActive}
      />
    </SidebarGroup>
  );
};

export default ProductsGroup;
