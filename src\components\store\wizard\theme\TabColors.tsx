
import React from 'react';
import { Slider } from '@/components/ui/slider';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';

interface TabColorsProps {
  themeOptions: {
    colorScheme?: string;
    cornerRadius?: number;
    buttonStyle?: string;
  };
  handleUpdateThemeOptions: (updates: Partial<TabColorsProps['themeOptions']>) => void;
  colorSchemes: {
    id: string;
    name: string;
    primary: string;
    secondary: string;
  }[];
}

const TabColors: React.FC<TabColorsProps> = ({
  themeOptions,
  handleUpdateThemeOptions,
  colorSchemes,
}) => {
  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Color Scheme</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
          {colorSchemes.map((scheme) => (
            <div 
              key={scheme.id}
              className={cn(
                "border rounded-md p-3 cursor-pointer hover:border-green-500 transition-all",
                themeOptions.colorScheme === scheme.id && "border-2 border-green-500"
              )}
              onClick={() => handleUpdateThemeOptions({ colorScheme: scheme.id })}
            >
              <div className="flex items-center gap-2 mb-2">
                <div 
                  className="w-4 h-4 rounded-full" 
                  style={{ backgroundColor: scheme.primary }}
                />
                <span>{scheme.name}</span>
              </div>
              <div className="flex gap-1">
                <div 
                  className="h-6 flex-1 rounded-l-md" 
                  style={{ backgroundColor: scheme.primary }}
                />
                <div 
                  className="h-6 flex-1 rounded-r-md" 
                  style={{ backgroundColor: scheme.secondary }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">UI Elements</h3>
        
        <div className="space-y-2">
          <Label htmlFor="corner-radius">Corner Radius</Label>
          <div className="flex items-center gap-4">
            <Slider 
              id="corner-radius"
              min={0}
              max={16}
              step={1}
              value={[themeOptions.cornerRadius || 8]}
              onValueChange={(values) => handleUpdateThemeOptions({ cornerRadius: values[0] })}
              className="flex-1"
            />
            <span className="text-sm font-medium w-8 text-center">{themeOptions.cornerRadius || 8}px</span>
          </div>
          <div className="mt-2 flex gap-2">
            <div className={cn("w-16 h-16 bg-green-500", `rounded-[${themeOptions.cornerRadius || 8}px]`)} />
            <div className="flex-1">
              <p className="text-sm text-muted-foreground">
                Controls the roundness of buttons, cards, and other UI elements
              </p>
            </div>
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Button Style</Label>
          <RadioGroup 
            value={themeOptions.buttonStyle || 'rounded'} 
            onValueChange={(value) => handleUpdateThemeOptions({ buttonStyle: value })}
            className="flex flex-wrap gap-3"
          >
            <div className="flex items-center gap-2">
              <RadioGroupItem value="rounded" id="rounded" />
              <Label htmlFor="rounded" className="cursor-pointer">Rounded</Label>
            </div>
            <div className="flex items-center gap-2">
              <RadioGroupItem value="square" id="square" />
              <Label htmlFor="square" className="cursor-pointer">Square</Label>
            </div>
            <div className="flex items-center gap-2">
              <RadioGroupItem value="pill" id="pill" />
              <Label htmlFor="pill" className="cursor-pointer">Pill</Label>
            </div>
          </RadioGroup>
        </div>
      </div>
    </div>
  );
};

export default TabColors;
