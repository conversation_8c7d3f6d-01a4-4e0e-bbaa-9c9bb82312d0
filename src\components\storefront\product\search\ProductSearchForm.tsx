
import React from 'react';
import { Search, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface ProductSearchFormProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  onSubmit: (e: React.FormEvent) => void;
  onFilterToggle: () => void;
}

const ProductSearchForm: React.FC<ProductSearchFormProps> = ({
  searchQuery,
  onSearchChange,
  onSubmit,
  onFilterToggle,
}) => {
  return (
    <form onSubmit={onSubmit} className="flex gap-2 mb-4">
      <div className="relative flex-1">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder="Search products..."
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className="pl-9"
        />
      </div>
      <Button type="submit" variant="outline" className="shrink-0">Search</Button>
      <Button 
        type="button"
        variant="outline" 
        onClick={onFilterToggle}
        className="shrink-0"
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
      </Button>
    </form>
  );
};

export default ProductSearchForm;
